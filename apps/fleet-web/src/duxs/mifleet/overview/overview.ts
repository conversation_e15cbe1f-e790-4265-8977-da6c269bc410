import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import { createStatusSelectors, apiStatusReducers } from 'duxs/utils'
import type { FixMeAny, DateTimeRange } from 'src/types'
import type { AppState } from 'src/root-reducer'
import { actions as operationalActions } from 'duxs/mifleet/operational'
import { actions as regulatoryActions } from 'duxs/mifleet/regulatory'
import { actions as vehicleCostsActions } from 'duxs/mifleet/vehicle-costs'
import type {
  VehicleDetails,
  VehicleDetailsFuelling,
  VehicleDetailsToll,
  VehicleDetailsCleaning,
  VehicleDetailsMaintenance,
  VehicleDetailsFine,
  VehicleDetailsBreakdown,
  VehicleDetailsPermit,
  VehicleDetailsTax,
  VehicleDetailsPurchase,
  VehicleDetailsAccessories,
  VehicleDetailsFinancing,
  VehicleDetailsAssignedList,
  VehicleDetailsLeasing,
  VehicleDetailsConsumable,
  VehicleDetailsOil,
  VehicleDetailsRental,
  MiFleetDriver,
  VehicleDetailsMonthlyCosts,
  VehicleDetailsCostsPerKilometer,
} from 'src/modules/mifleet/lite/generalTypes'
import { ctIntl } from 'src/util-components/ctIntl'
import type { OperationalFineType } from 'src/modules/mifleet/operational/shared/type'
type State = {
  miFleetDrivers: Array<MiFleetDriver>
  list: Array<Status>
  detailsList: Array<{
    document_concept: string
    list: Array<StatusDetail>
  }>
  selectedVehicle?: {
    plate: string
    manufacturer: string
    model: string
    vehicle_id: string
  }
  defaultContractFormValues: Record<string, any>
  vehicleDetails?: VehicleDetails
  vehicleMonthlyCost?: { monthlyCost: VehicleDetailsMonthlyCosts }
  vehicleCostPerKm?: { costPerKm: VehicleDetailsCostsPerKilometer }
  // Fuel Fraud
  fuelList: Array<Status>
  detailsFuelList: Record<string, any>
  fineList: Array<OperationalFineType.FinesValidationList>
  detailsFineFraud?: FineFraudDetail
  fuelGeoLocationList: Array<GeoFuelEntry>
  fuelGeoLocationEntryDetails: GeoFuelEntry | Record<string, any>
  fuelGeoLocationCreationResult: GeoFuelEntry | Record<string, any>
  miFleetResources: MiFleetResources
  serviceTask: Array<ServiceTask>
  reminderList: Array<ServiceReminder>
  reminderDetails: ServiceReminder
  isReminderDetailsLoading: boolean
}

const initialState: State = {
  miFleetDrivers: [],
  list: [],
  detailsList: [],
  defaultContractFormValues: {},
  selectedVehicle: undefined,
  vehicleDetails: undefined,
  vehicleMonthlyCost: undefined,
  vehicleCostPerKm: undefined,
  // Fraud
  fuelList: [],
  detailsFuelList: {},
  fineList: [],
  detailsFineFraud: undefined,
  fuelGeoLocationList: [],
  fuelGeoLocationEntryDetails: {},
  fuelGeoLocationCreationResult: {},
  miFleetResources: { drivers: [], vehicles: [] },
  serviceTask: [],
  reminderList: [],
  reminderDetails: {},
  isReminderDetailsLoading: false,
}

const removeServerDateStringTimezone = (data: GeoFuelEntry) => {
  let parsedData = data
  if (parsedData && parsedData.fuel_date) {
    parsedData = {
      ...parsedData,
      fuel_date: ctIntl.removeServerDateStringTimezone(parsedData.fuel_date) as string,
    }
  }
  return parsedData
}

const { reducer, actions } = createSlice({
  name: 'costsOverview',
  initialState,
  reducers: {
    ...apiStatusReducers,
    fetchMiFleetDrivers: (draft) => draft,
    onFetchMiFleetDrivers: (
      draft,
      { payload: list }: { payload: Array<MiFleetDriver> },
    ) => {
      draft.miFleetDrivers = list
    },
    fetchMFResources: (draft) => draft,
    onFetchMFResources: (draft, { payload: resources }: PayloadAction<FixMeAny>) => {
      draft.miFleetResources = resources
    },
    fetchList: (draft) => draft,
    fetchFuelList: (draft, _action?: PayloadAction<FixMeAny>) => draft,
    onFetchList: (draft, { payload: list }: PayloadAction<Array<Status>>) => {
      draft.list = list
    },
    onFetchFuelList: (draft, { payload: fuelList }: PayloadAction<Array<Status>>) => {
      draft.fuelList = fuelList
    },
    fetchFineList: (draft, _action?: PayloadAction<FixMeAny>) => draft,
    onFetchFineList: (
      draft,
      {
        payload: fineList,
      }: PayloadAction<Array<OperationalFineType.FinesValidationList>>,
    ) => {
      draft.fineList = fineList
    },
    fetchFuelGeoLocationList: (draft) => draft,
    onFetchFuelGeoLocationList: (
      draft,
      { payload: fuelGeoLocationList }: PayloadAction<Array<GeoFuelEntry>>,
    ) => {
      const list = fuelGeoLocationList.map((item) =>
        removeServerDateStringTimezone(item),
      )

      draft.fuelGeoLocationList = list
    },
    fetchFuelGeoLocationEntryDetails: (draft, _payload: PayloadAction<string>) => draft,
    onFetchFuelGeoLocationEntryDetails: (
      draft,
      { payload: fuelGeoLocationEntryDetails }: PayloadAction<GeoFuelEntry>,
    ) => {
      const index = draft.fuelGeoLocationList.findIndex(
        (item) =>
          item.fuel_validation_id === fuelGeoLocationEntryDetails.fuel_validation_id,
      )
      if (index != null && index !== -1) {
        const details = removeServerDateStringTimezone(fuelGeoLocationEntryDetails)
        const updatedItem = {
          ...draft.fuelGeoLocationList[index],
          fuel_validation_status_id: details.fuel_validation_status_id,
          fuel_date: details.fuel_date,
        }
        draft.fuelGeoLocationList.splice(index, 1, updatedItem)
      }
      let entryDetails = fuelGeoLocationEntryDetails
      if (fuelGeoLocationEntryDetails && fuelGeoLocationEntryDetails.fuel_date) {
        entryDetails = removeServerDateStringTimezone(fuelGeoLocationEntryDetails)
      }
      draft.fuelGeoLocationEntryDetails = entryDetails
    },
    resetFuelGeoLocationEntryDetails: (draft) => {
      draft.fuelGeoLocationEntryDetails = {}
      draft.fuelGeoLocationCreationResult = {}
    },
    createFuelGeoLocationEntry: (draft, _payload: PayloadAction<GeoFuelEntry>) => draft,
    onCreateFuelGeoLocationEntry: (
      draft,
      { payload: data }: PayloadAction<GeoFuelEntry>,
    ) => {
      draft.fuelGeoLocationCreationResult = data
      const allData = JSON.parse(JSON.stringify(draft.fuelGeoLocationList))
      allData.push({
        ...data,
        is_deleted: 'f',
      })
      draft.fuelGeoLocationList = allData
    },
    addNewFuelFraudFuelStation: (
      draft,
      _payload: PayloadAction<FuelFraudFuelStation>,
    ) => draft,
    onAddNewFuelFraudFuelStation: (
      draft,
      { payload: data }: PayloadAction<FuelFraudFuelStation>,
    ) => {
      const index = draft.fuelList.findIndex(
        (item) => item.document_line_id === data.document_line_id,
      )
      const detailsToUpdateEntry = {
        fleet_check_result_id: 5,
        fleet_check: true,
        validation_status: 'Validated',
        fuel_validation_status_id: 1,
        possible_fuel_stations: [
          {
            fuel_station_id: data.fuel_station_id,
            fuel_station: data.fuel_station,
            latitude: data.latitude,
            longitude: data.longitude,
            supplier_name: data.supplier_name,
          },
        ],
      }
      const updatedItem = {
        ...draft.fuelList[index],
        ...detailsToUpdateEntry,
      }
      draft.fuelList.splice(index, 1, updatedItem)
      draft.detailsFuelList = {
        ...draft.detailsFuelList,
        ...detailsToUpdateEntry,
      }
    },
    fetchDetails: (draft, _: PayloadAction<string>) => {
      draft.detailsList = []
    },
    fetchFuelDetails: (draft, _: PayloadAction<number>) => {
      draft.detailsFuelList = {}
    },
    deleteFuelGeoLocationFraud: (draft, _payload: PayloadAction<FixMeAny>) => draft,
    onDeleteFuelGeoLocationFraud: (
      draft,
      { payload: data }: PayloadAction<GeoFuelEntry>,
    ) => {
      const index = draft.fuelGeoLocationList.findIndex(
        (item) => item.fuel_validation_id === data.fuel_validation_id,
      )
      if (index != null && index !== -1) {
        const updatedItem = {
          ...draft.fuelGeoLocationList[index],
          is_deleted: 't',
        }
        draft.fuelGeoLocationList.splice(index, 1, updatedItem)
      }
    },
    updateFuelGeoLocationFraud: (draft, _payload: PayloadAction<GeoFuelEntry>) => draft,
    onUpdateFuelGeoLocationFraud: (
      draft,
      { payload: data }: PayloadAction<GeoFuelEntry>,
    ) => {
      const index = draft.fuelGeoLocationList.findIndex(
        (item) => item.fuel_validation_id === data.fuel_validation_id,
      )
      if (index != null && index !== -1) {
        const updatedItem = {
          ...draft.fuelGeoLocationList[index],
          fuel_station_merchant: data.fuel_station_merchant,
          fuel_station_address: data.fuel_station_address,
          validated_fuel_station_id: data.validated_fuel_station_id,
          fuel_validation_status_id: data.fuel_validation_status_id || '',
        }
        draft.fuelGeoLocationList.splice(index, 1, updatedItem)
      }
      const updatedEntryItem = {
        ...draft.fuelGeoLocationEntryDetails,
        fuel_validation_status_id: data.fuel_validation_status_id || '',
        fuel_station_merchant: data.fuel_station_merchant,
        fuel_station_address: data.fuel_station_address,
        validated_fuel_station_id: data.validated_fuel_station_id,
      }
      draft.fuelGeoLocationEntryDetails = updatedEntryItem
    },
    updateFuelFraud: (
      draft,
      _payload: PayloadAction<{
        document_line_id: string
        validated: boolean
        fleet_check: boolean
      }>,
    ) => draft,
    updateFuelCapacity: (
      draft,
      _payload: PayloadAction<{
        document_line_id: number
        vehicle_id: string
        fuel_tank_capacity: string
        dateFilters?: {
          start_date: string
          end_date: string
        }
      }>,
    ) => draft,

    recheckFuelFraud: (
      draft,
      _: PayloadAction<{
        document_line_id: number
        dateFilters?: {
          start_date: string
          end_date: string
        }
      }>,
    ) => draft,
    recheckTollFraud: (
      draft,
      _: PayloadAction<{
        document_line_id: number
        dateFilters?: {
          start_date: string
          end_date: string
        }
      }>,
    ) => draft,
    onUpdateFuelFraud: (
      draft,
      {
        payload: data,
      }: PayloadAction<{
        document_line_id: number
        validated: boolean
        company_id: string
        validation_status: string
        fuel_validation_status_id: number
      }>,
    ) => {
      const index = draft.fuelList.findIndex(
        (item) => item.document_line_id === data.document_line_id,
      )
      if (index != null && index !== -1) {
        const updatedItem = {
          ...draft.fuelList[index],
          validated: data.validated,
          FEValidated: data.validated,
          validation_status: data.validation_status || '',
          fuel_validation_status_id: data.fuel_validation_status_id,
        }
        draft.fuelList.splice(index, 1, updatedItem)
        if (data.fuel_validation_status_id) {
          const oldData = draft.detailsFuelList
          const id = data.fuel_validation_status_id
          draft.detailsFuelList = { ...oldData, fuel_validation_status_id: id }
        }
      }
    },
    updateFineFraud: (
      draft,
      _payload: PayloadAction<{
        document_line_id: number
        validated: boolean
        fleet_check: boolean
      }>,
    ) => draft,
    onUpdateFineFraud: (
      draft,
      {
        payload: data,
      }: PayloadAction<{
        document_line_id: number
        validated: boolean
        company_id: string
        validation_status: string
      }>,
    ) => {
      const index = draft.fineList.findIndex(
        (item) => item.document_line_id === data.document_line_id,
      )
      if (index != null && index !== -1) {
        const updatedItem = {
          ...draft.fineList[index],
          validated: data.validated,
          FEValidated: data.validated,
          validation_status: data.validation_status || '',
        }
        draft.fineList.splice(index, 1, updatedItem)
      }
    },
    onFetchDetails: (
      draft,
      { payload: detailsList }: PayloadAction<Array<StatusDetail>>,
    ) => {
      const documentConceptKeysIndex: FixMeAny = {}
      const documentConceptList: Array<{
        document_concept: string
        list: Array<StatusDetail>
      }> = []
      detailsList.forEach((item) => {
        if (documentConceptKeysIndex[item.document_concept] == null) {
          documentConceptKeysIndex[item.document_concept] = documentConceptList.length

          documentConceptList.push({
            document_concept: item.document_concept,
            list: [{ ...item, expiration_date: item.expiration_date }],
          })
        } else {
          documentConceptList[
            documentConceptKeysIndex[item.document_concept]
          ].list.push({
            ...item,
            expiration_date: item.expiration_date,
          })
        }
      })

      draft.detailsList = documentConceptList
    },
    onFetchFuelDetails: (
      draft,
      { payload: detailsFuelList }: PayloadAction<StatusDetail>,
    ) => {
      draft.detailsFuelList = detailsFuelList

      const index = draft.fuelList.findIndex(
        (item) => item.document_line_id === detailsFuelList.document_line_id,
      )
      if (index != null && index !== -1) {
        const updatedItem = {
          ...draft.fuelList[index],
          fuel_validation_status_id: detailsFuelList.fuel_validation_status_id,
        }
        draft.fuelList.splice(index, 1, updatedItem)
      }
    },
    fetchFineDetails: (draft, _: PayloadAction<number>) => draft,
    onFetchFineDetails: (
      draft,
      { payload: detailsFineFraud }: PayloadAction<FineFraudDetail>,
    ) => {
      draft.detailsFineFraud = detailsFineFraud
    },
    setSelectedVehicle: (
      draft,
      {
        payload: vehicle,
      }: PayloadAction<{
        plate: string
        manufacturer: string
        model: string
        vehicle_id: string
      }>,
    ) => {
      draft.selectedVehicle = vehicle
    },
    fetchVehicleDetails: (draft, _: PayloadAction<string>) => draft,
    onFetchVehicleDetails: (
      draft,
      { payload: list }: PayloadAction<VehicleDetails>,
    ) => {
      draft.vehicleDetails = list
    },
    fetchVehicleMonthlyCost: (
      draft,
      _: PayloadAction<{ vehicleId: string; date: DateTimeRange }>,
    ) => draft,
    onFetchVehicleMonthlyCost: (
      draft,
      { payload: list }: PayloadAction<{ monthlyCost: VehicleDetailsMonthlyCosts }>,
    ) => {
      draft.vehicleMonthlyCost = list
    },
    fetchVehicleCostPerKm: (
      draft,
      _: PayloadAction<{ vehicleId: string; date: DateTimeRange }>,
    ) => draft,
    onFetchVehicleCostPerKm: (
      draft,
      { payload: list }: PayloadAction<{ costPerKm: VehicleDetailsCostsPerKilometer }>,
    ) => {
      draft.vehicleCostPerKm = list
    },
    fetchAddAssignedDriver: (
      draft,
      _: PayloadAction<{
        driver_id: string
        vehicle_id: string
        from_date: string
        to_date: string | null
      }>,
    ) => draft,
    fetchUpdateAssignedDriver: (
      draft,
      _: PayloadAction<VehicleDetailsAssignedList & { vehicle_id: string }>,
    ) => draft,
    setDefaultContractFormValues: (
      draft,
      { payload: values }: PayloadAction<Record<string, any>>,
    ) => {
      draft.defaultContractFormValues = values
    },
    fetchServicesList: (draft) => draft,
    onFetchServicesList: (
      draft,
      { payload: list }: PayloadAction<Array<ServiceReminder>>,
    ) => {
      draft.reminderList = list
    },
    fetchServiceTaskList: (draft) => draft,
    onFetchServiceTaskList: (
      draft,
      { payload: list }: PayloadAction<Array<ServiceTask>>,
    ) => {
      draft.serviceTask = list
    },
    createServiceReminder: (draft, _: PayloadAction<ServiceReminder>) => draft,
    onCreateServiceReminder: (
      draft,
      { payload: line }: PayloadAction<ServiceReminder>,
    ) => {
      draft.reminderList.unshift(line)
    },
    fetchServiceReminderDetail: (draft, _: PayloadAction<string>) => {
      draft.isReminderDetailsLoading = true
    },
    onFetchServiceReminderDetail: (
      draft,
      { payload: detailsService }: PayloadAction<ServiceReminder>,
    ) => {
      draft.reminderDetails = detailsService
      draft.isReminderDetailsLoading = false
    },
    updateServiceReminder: (draft, _payload: PayloadAction<Partial<ServiceReminder>>) =>
      draft,
    onUpdateServiceReminder: (
      draft,
      { payload: data }: PayloadAction<ServiceReminder>,
    ) => {
      const index = draft.reminderList.findIndex(
        (item) => item.service_id === data.service_id,
      )
      if (index != null && index !== -1) {
        const updatedItem = {
          ...draft.reminderList[index],
          service_id: data.service_id,
          vehicle_id: data.vehicle_id,
          vehicle_service_type_id: data.vehicle_service_type_id,
          mileage_period: data.mileage_period,
          service_interval: data.service_interval,
          expiration_date: data.expiration_date,
          service_date_interval: data.service_date_interval,
          service_interval_unit: data.service_interval_unit,
        }
        draft.reminderList.splice(index, 1, updatedItem)
      }
    },
    deleteServiceReminder: (draft, _payload: PayloadAction<FixMeAny>) => draft,
    onDeleteServiceReminder: (
      draft,
      { payload: data }: PayloadAction<ServiceReminder>,
    ) => {
      const index = draft.reminderList.findIndex(
        (item) => item.service_id === data.service_id,
      )
      if (index != null && index !== -1) {
        const updatedItem = {
          ...draft.reminderList[index],
          is_deleted: 't',
        }
        draft.reminderList.splice(index, 1, updatedItem)
      }
    },
  },
  extraReducers: (builder) => {
    builder.addCase(
      operationalActions.onUpdateOperationalFuel,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsFuelling>) => {
        const index = draft.vehicleDetails?.fuellings.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.fuellings.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      operationalActions.onUpdateOperationalToll,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsToll>) => {
        const index = draft.vehicleDetails?.tolls.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.tolls.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      operationalActions.onUpdateOperationalCleaning,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsCleaning>) => {
        const index = draft.vehicleDetails?.cleanings.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.cleanings.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      operationalActions.onUpdateOperationalMaintenance,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsMaintenance>) => {
        const index = draft.vehicleDetails?.maintenances.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.maintenances.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      operationalActions.onUpdateOperationalFines,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsFine>) => {
        const index = draft.vehicleDetails?.fines.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.fines.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      operationalActions.onUpdateOperationalTowing,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsBreakdown>) => {
        const index = draft.vehicleDetails?.breakdowns.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.breakdowns.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      regulatoryActions.onUpdateRegulatoryPermits,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsPermit>) => {
        const index = draft.vehicleDetails?.permits.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.permits.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      regulatoryActions.onUpdateRegulatoryTaxes,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsTax>) => {
        const index = draft.vehicleDetails?.taxes.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.taxes.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      vehicleCostsActions.onUpdateVehicleCostsPurchase,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsPurchase>) => {
        const index = draft.vehicleDetails?.purchase.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.purchase.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      vehicleCostsActions.onUpdateVehicleCostsFinancing,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsFinancing>) => {
        const index = draft.vehicleDetails?.financing.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.financing.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      vehicleCostsActions.onUpdateVehicleCostsAccessories,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsAccessories>) => {
        const index = draft.vehicleDetails?.accessories.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.accessories.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      operationalActions.onUpdateOperationalLeasingCosts,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsLeasing>) => {
        const index = draft.vehicleDetails?.leasings.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.leasings.splice(index, 1, updatedValue)
        }
      },
    )

    builder.addCase(
      operationalActions.onUpdateOperationalConsumables,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsConsumable>) => {
        const index = draft.vehicleDetails?.consumables.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.consumables.splice(index, 1, updatedValue)
        }
      },
    )
    builder.addCase(
      operationalActions.onUpdateOperationalOils,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsOil>) => {
        const index = draft.vehicleDetails?.oils.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.oils.splice(index, 1, updatedValue)
        }
      },
    )
    builder.addCase(
      operationalActions.onUpdateOperationalRentalCosts,
      (draft, { payload: updatedValue }: PayloadAction<VehicleDetailsRental>) => {
        const index = draft.vehicleDetails?.rentals.findIndex(
          (item) => item.document_line_id === updatedValue.document_line_id,
        )
        if (index != null && index !== -1) {
          draft.vehicleDetails?.rentals.splice(index, 1, updatedValue)
        }
      },
    )
  },
})

const getReducerState = (state: AppState): State => state.overview

const statusSelectors = createStatusSelectors(getReducerState)
const selectors = {
  getMiFleetDrivers: (state: AppState) => getReducerState(state).miFleetDrivers,
  getMiFleetResources: (state: AppState) => getReducerState(state).miFleetResources,
  getDefaultContractFormValues: (state: AppState) =>
    getReducerState(state).defaultContractFormValues,
  getDetailsList: (state: AppState) => getReducerState(state).detailsList,
  getDetailsFuelList: (state: AppState) => getReducerState(state).detailsFuelList,
  getFuelGeoLocationEntryDetails: (state: AppState) =>
    getReducerState(state).fuelGeoLocationEntryDetails,
  getFuelGeoLocationCreationResult: (state: AppState) =>
    getReducerState(state).fuelGeoLocationCreationResult,
  getVehicleDetails: (state: AppState) => getReducerState(state).vehicleDetails,
  getVehicleMonthlyCost: (state: AppState) => getReducerState(state).vehicleMonthlyCost,
  getVehicleCostPerKm: (state: AppState) => getReducerState(state).vehicleCostPerKm,
  isFuelGeoEntryLoading: (state: AppState) =>
    statusSelectors.isSomeLoadingForActions(state, [
      actions.fetchFuelGeoLocationEntryDetails,
      actions.fetchMFResources,
      actions.createFuelGeoLocationEntry,
      actions.updateFuelGeoLocationFraud,
    ]),
  isAssignedDriversLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.fetchAddAssignedDriver),
  isDetailsLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.fetchDetails),
  isFuelListLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.fetchFuelList),
  isFineListLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.fetchFineList),
  isDetailsFineLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.fetchFineDetails),
  isFuelGeoLocationListLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.fetchFuelGeoLocationList),
  isMonthlyCostLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.fetchVehicleMonthlyCost),
  isCostPerKmLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.fetchVehicleCostPerKm),
  isLoading: (state: AppState) =>
    statusSelectors.isSomeLoadingForActions(state, [
      'fetchDetails',
      operationalActions.fetchOperationalFuel.type,
      operationalActions.fetchOperationalCleaning.type,
      operationalActions.fetchOperationalMaintenance.type,
      operationalActions.fetchOperationalTires.type,
      operationalActions.fetchOperationalFines.type,
      operationalActions.fetchOperationalTowing.type,
      operationalActions.fetchOperationalIncidents.type,
      regulatoryActions.fetchRegulatoryPermits.type,
      regulatoryActions.fetchRegulatoryTaxes.type,
      vehicleCostsActions.fetchVehicleCostsAccessories.type,
      'fetchVehicleMonthlyCost',
      'fetchVehicleCostPerKm',
    ]),
  isFuelGeoLocationCreationLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.createFuelGeoLocationEntry),
  isDetailsFuelLoading: (state: AppState) =>
    statusSelectors.isLoadingFor(state, actions.fetchFuelDetails),
}

export { reducer, actions, selectors }
