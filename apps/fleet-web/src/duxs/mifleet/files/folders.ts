import { isNil, random, remove, isEmpty } from 'lodash'
import type { FolderType } from 'src/modules/mifleet/components/files/types/folder'
import { createSlice } from '@reduxjs/toolkit'
import { apiStatusReducers, createStatusSelectors } from 'duxs/utils'
import type { AppState } from 'src/root-reducer'

type State = {
  folders: Array<FolderType>
}

const initialState: State = {
  folders: [],
}

const isRootFolder = (folder: FolderType) => isNil(folder.parent_id)

const addFolder = (
  folders: Array<FolderType>,
  folderToAdd: FolderType,
  folderToAddParentId: string,
): Array<FolderType> => {
  const groupWithNewParent = { ...folderToAdd, parent_id: folderToAddParentId }
  if (isRootFolder(groupWithNewParent)) {
    return [{ ...groupWithNewParent }, ...folders]
  }

  return folders.map((folder) => {
    if (folder.folder_id === folderToAddParentId) {
      return {
        ...folder,
        objectList: [{ ...groupWithNewParent }, ...folder.objectList],
      }
    }

    if (folder.objectList.length > 0) {
      return {
        ...folder,
        objectList: addFolder(folder.objectList, folderToAdd, folderToAddParentId),
      }
    }

    return folder
  })
}

const addOrRemoveNewFolderToNested = (
  action: 'add' | 'remove',
  nodesArray: Array<string>,
  fullArr: Array<FolderType>,
  folderActive: FolderType,
  newFolder?: FolderType,
) => {
  // Get the first folder for iteration and remove it from nodesArray
  const targetID = nodesArray.shift()

  for (let index = 0; index < fullArr.length; index++) {
    // Check is iteration is valid
    if (fullArr[index].folder_id === targetID) {
      // Check if it is the final iteration/folder
      if (nodesArray.length > 0) {
        // It's not, do it again with updated values
        addOrRemoveNewFolderToNested(
          action,
          nodesArray,
          fullArr[index].objectList,
          folderActive,
          newFolder,
        )
      } else {
        // It is the target folder, add/remove the new folder
        if (action === 'add') {
          fullArr[index].objectList.push(newFolder as FolderType)
        } else {
          remove(
            fullArr[index].objectList,
            (o) => o.folder_id === folderActive.folder_id,
          )
        }
      }

      index = fullArr.length
    }
  }
}

const { reducer, actions } = createSlice({
  name: 'folders',
  initialState,
  reducers: {
    ...apiStatusReducers,
    fetchFolders: (draft) => draft,
    onFetchFolders: (draft, { payload: folders }: { payload: Array<FolderType> }) => {
      draft.folders = folders
    },
    createFolder: (
      draft,
      {
        payload: { folder, activeNodes },
      }: { payload: { folder?: FolderType; activeNodes: Array<string> } },
    ) => {
      const emptyFolder = {
        folder_id: `temporary${random(5, true)}`,
        folder_name: '',
        flag: true,
        parent_id: folder?.folder_id || '',
        objectList: [],
      }

      if (isNil(folder)) {
        draft.folders.push(emptyFolder as FolderType)
      } else {
        addOrRemoveNewFolderToNested(
          'add',
          JSON.parse(JSON.stringify(activeNodes)),
          draft.folders,
          folder,
          emptyFolder,
        )
      }
    },
    fetchCreateFolder: (draft, _: { payload: FolderType }) => draft,
    onFetchCreateFolder: (
      draft,
      { payload: createdFolder }: { payload: FolderType },
    ) => {
      draft.folders = addFolder(
        draft.folders,
        createdFolder,
        createdFolder.parent_id || '',
      )
    },
    fetchUpdateFolder: (draft, _: { payload: FolderType }) => draft,
    updateFolder: (draft, { payload: updatedFolder }: { payload: FolderType }) => {
      const index = draft.folders.findIndex(
        (folder) => folder.folder_id === updatedFolder.folder_id,
      )

      if (index !== -1) {
        draft.folders[index] = updatedFolder
      }
    },
    fetchDeleteFolder: (draft, _: { payload: string }) => draft,
    deleteFolder: (
      draft,
      {
        payload: { folder, activeNodes },
      }: { payload: { folder: FolderType; activeNodes: Array<string> } },
    ) => {
      const newFolder = isEmpty(folder.parent_id) ? undefined : folder
      if (isEmpty(newFolder)) {
        remove(draft.folders, (o) => o.folder_id === folder.folder_id)
      } else {
        //DELETING TEMP FOLDER
        activeNodes.pop()
        addOrRemoveNewFolderToNested(
          'remove',
          JSON.parse(JSON.stringify(activeNodes)),
          draft.folders,
          {
            folder_id: folder.folder_id,
            folder_name: '',
            flag: true,
            parent_id: '',
            objectList: [],
          },
        )
      }
    },
    fetchSortFolder: (
      draft,
      _: { payload: FolderType & { sortList: Array<{ folder_id: string }> } },
    ) => draft,
  },
})

const statusSelectors = createStatusSelectors((state: AppState) => state.folders)
const selectors = {
  getFolders: (state: AppState) => state.folders.folders,
  isLoading: statusSelectors.isSomeLoading,
}

export { reducer, actions, selectors }
