import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import { apiStatusReducers, createStatusSelectors } from 'duxs/utils'
import type { FileType } from 'src/modules/mifleet/components/files/types/file'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny } from 'src/types'

type State = {
  files: Array<FileType>
  mifleetVehicles: Array<FixMeAny>
  mifleetDrivers: Array<FixMeAny>
  fileBeingCreated: FileType | Record<string, unknown>
  isDeletingFile: boolean
}

const initialState: State = {
  files: [],
  mifleetVehicles: [],
  mifleetDrivers: [],
  fileBeingCreated: {},
  isDeletingFile: false,
}

const { reducer, actions } = createSlice({
  name: 'files',
  initialState,
  reducers: {
    ...apiStatusReducers,
    fetchFiles: (draft, _: PayloadAction<string>) => draft,
    onFetchFiles: (
      draft,
      {
        payload: fileObject,
      }: PayloadAction<{
        fileDocument: Array<FileType>
        vehicles: Array<FixMeAny>
        drivers: Array<FixMeAny>
      }>,
    ) => {
      draft.files = fileObject.fileDocument || []
      draft.mifleetDrivers = fileObject.drivers
      draft.mifleetVehicles = fileObject.vehicles
    },
    fetchCreateFile: (draft, _: PayloadAction<FileType>) => draft,
    onFetchCreateFile: (
      draft,
      { payload: createdFile }: PayloadAction<FileType | null>,
    ) => {
      if (createdFile) {
        draft.files.push(createdFile)
      }
    },
    createFile: (draft, { payload: folder_id }: PayloadAction<string>) => {
      draft.fileBeingCreated = {
        file_document_id: `temporary${draft.files.length}`,
        folder_id,
        vehicle_id: '',
        driver_id: '',
        description: '',
        notes: '',
        filename: '',
      }
    },
    fetchUpdateFile: (draft, _: PayloadAction<FileType>) => draft,
    updateFile: (draft, { payload: updatedFile }) => {
      if (updatedFile) {
        const index = draft.files.findIndex(
          (file) => file.file_document_id === updatedFile.file_document_id,
        )

        if (index !== -1) {
          draft.files[index] = updatedFile
        }
      }
    },
    fetchDeleteFile: (draft, _: PayloadAction<FileType>) => {
      draft.isDeletingFile = true
    },
    deleteFile: (draft, { payload: fileId }: PayloadAction<string>) => {
      if (fileId) {
        draft.isDeletingFile = false
        const index = draft.files.findIndex((file) => file.file_document_id === fileId)

        if (index !== -1) {
          draft.files.splice(index, 1)
        }
      }
    },
    fetchReadFile: (draft, _: PayloadAction<FileType>) => draft,
    fetchDownloadImportErrors: (
      draft,
      _: PayloadAction<{ import_id: number; filename: string }>,
    ) => draft,
    clearFilesList: (draft) => {
      draft.files = []
    },
  },
})

const getReducerState = (state: AppState): State => state.files

const statusSelectors = createStatusSelectors(getReducerState)
const selectors = {
  getIsDeletingFile: (state: AppState) => getReducerState(state).isDeletingFile,
  getFiles: (state: AppState) => getReducerState(state).files,
  getFilesDrivers: (state: AppState) => getReducerState(state).mifleetDrivers,
  getFilesVehicles: (state: AppState) => getReducerState(state).mifleetVehicles,
  getFileBeingCreated: (state: AppState) => getReducerState(state).fileBeingCreated,
  isLoading: statusSelectors.isSomeLoading,
}

export { reducer, actions, selectors }
