import { isNil, isEmpty, remove } from 'lodash'
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import {
  actions as costCentresGroupActions,
  type CostCentreGroupId,
} from 'duxs/mifleet/cost-centres-groups'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny } from 'src/types'

const { onDeleteCostCenterGroup } = costCentresGroupActions

export type CostCentre = {
  company_id: string
  cost_centre: string
  cost_centre_family_id: CostCentreGroupId
  cost_centre_id: string
  is_deleted: string
  flag?: boolean
}

type State = {
  items: Array<CostCentre>
  loading: boolean
  editing: boolean
  status: { ok: boolean; message: string }
}

// Default state
const initialState: State = {
  items: [],
  loading: false,
  editing: false,
  status: { ok: true, message: '' },
}

const { reducer, actions } = createSlice({
  name: 'costCentres',
  initialState,
  reducers: {
    fetchCostCenters: (draft, _: PayloadAction<{ item: CostCentre }>) => {
      draft.loading = true
    },
    fetchCreateCostCenter: (draft, _: PayloadAction<{ item: CostCentre }>) => {
      draft.loading = true
    },
    updateCostCenter: (draft, _: PayloadAction<{ item: CostCentre }>) => {
      draft.loading = true
    },
    deleteCostCenter: (draft, _: PayloadAction<{ item: CostCentre }>) => {
      draft.loading = true
    },
    onFetchCostCenters: (
      draft,
      { payload: items }: PayloadAction<Array<CostCentre>>,
    ) => {
      draft.items = items
      draft.loading = false
    },
    onFetchCostCentersFailure: (draft) => {
      draft.loading = false
    },
    createCostCenter: (
      draft,
      { payload: { item } }: PayloadAction<{ item: CostCentre }>,
    ) => {
      const check = draft.items.filter((c) => c.cost_centre_id === 'temp')

      if (isEmpty(check)) {
        const newObj = {
          company_id: '',
          cost_centre_family_id: item.cost_centre_family_id,
          cost_centre_id: 'temp',
          cost_centre: '',
          is_deleted: '',
          flag: true,
        }

        draft.items = [...draft.items, newObj]
        draft.editing = true
      }
    },
    onCreateCostCenter: (
      draft,
      {
        payload: { newItem, prevId, res },
      }: PayloadAction<{
        newItem?: CostCentre
        prevId?: CostCentre['cost_centre_id']
        res: FixMeAny
      }>,
    ) => {
      const updatedItems = [...draft.items]
      if (res.success) {
        const itemIndex = updatedItems.findIndex(
          (item) => item.cost_centre_id === prevId,
        )

        if (itemIndex !== -1 && newItem) {
          updatedItems[itemIndex] = { ...newItem, flag: false }
        }
      }

      draft.items = updatedItems
      draft.loading = false
      draft.editing = true
      draft.status = {
        ok: res.success,
        message: formatNotificationMessage(res, 'Cost Centre Created'),
      }
    },
    onUpdateCostCenter: (
      draft,
      {
        payload: { newItem, res, prevId },
      }: PayloadAction<{
        newItem?: CostCentre
        prevId?: CostCentre['cost_centre_id']
        res: FixMeAny
      }>,
    ) => {
      draft.items = res.success
        ? draft.items.map((item) =>
            item.cost_centre_id === prevId && newItem
              ? {
                  ...newItem,
                  flag: false,
                }
              : item,
          )
        : draft.items
      draft.loading = false
      draft.editing = false
      draft.status = {
        ok: res.success,
        message: formatNotificationMessage(res, 'Cost Centre Updated'),
      }
    },
    updateCostCenterStatus: (draft, { payload: { message, ok } }) => {
      draft.status = {
        ok,
        message,
      }
    },
    onDeleteCostCenter: (
      draft,
      { payload: { item } }: PayloadAction<{ item: CostCentre }>,
    ) => {
      const lastId = item.cost_centre_id
      draft.items = draft.items.filter((item) => item.cost_centre_id !== lastId)
      draft.loading = false
    },
    onDeleteCostCenterFailure: (draft) => {
      draft.loading = false
    },
    cancelCreateCostCenter: (draft) => {
      remove(draft.items, (o) => o.cost_centre_id === 'temp')
    },
  },
  extraReducers: (builder) => {
    builder.addCase(onDeleteCostCenterGroup, (draft) => {
      draft.items = []
    })
  },
})

const formatNotificationMessage = (res: FixMeAny, st: string) =>
  res.success ? st : 'It seems there was an error, please try again.'

export function updateItemIfValid(data: { item: CostCentre }) {
  const value = data.item.cost_centre
  if (isNil(value) || value === '') {
    return actions.updateCostCenterStatus({
      ok: false,
      message: 'Field can not be empty',
    })
  }

  return actions.updateCostCenter(data)
}

export { reducer, actions }

const getReducerState = (state: AppState) => state.costCentres as State

// Selectors
export const getItems = (state: AppState) => getReducerState(state).items
export const getLoading = (state: AppState) => getReducerState(state).loading
export const getEditing = (state: AppState) => getReducerState(state).editing
export const getStatus = (state: AppState) => getReducerState(state).status
