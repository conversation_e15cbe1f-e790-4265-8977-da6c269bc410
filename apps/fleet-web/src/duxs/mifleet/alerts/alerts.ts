import { createSlice, type PayloadAction, createSelector } from '@reduxjs/toolkit'
import { apiStatusReducers, createStatusSelectors } from 'duxs/utils'
import type {
  CostsAlert,
  CostsAlertNotification,
  CostsAlertTypes,
  CostsAlertApiObject,
} from 'src/types/api/mifleet/alerts'
import type { AppState } from 'src/root-reducer'

import { selectors as activeAlertsSelectors } from './active-alerts'
import { format, formatDistanceStrict } from 'date-fns'
import type { IconName, IconPrefix } from '@fortawesome/fontawesome-svg-core'
import { getCachedVehicles } from 'duxs/vehicles'
import { getDrivers } from 'duxs/drivers'
import { getUsers } from 'duxs/admin'

const { getCostsAlertTypes } = activeAlertsSelectors

type State = {
  costsAlerts: Array<CostsAlert>
  costsDrivers: Array<Record<string, any>>
  costsVehicles: Array<Record<string, any>>
  costsAlertsNotifications: Array<CostsAlertNotification>
}

const initialState: State = {
  costsAlerts: [],
  costsAlertsNotifications: [],
  costsDrivers: [],
  costsVehicles: [],
}

const { reducer, actions } = createSlice({
  name: 'costsAlerts',
  initialState,
  reducers: {
    ...apiStatusReducers,
    fetchCostsAlerts: (
      state,
      _: {
        payload: { startDate: string; endDate: string }
      },
    ) => state,
    updateCostsAlert: (state, _: { payload: CostsAlert }) => state,
    dismissCostsAlert: (state, _: { payload: string }) => state,
    fetchCostsAlertsNotifications: (
      state,
      _: {
        payload: PayloadAction
      },
    ) => state,
    markAlertsNotificationsAsRead: (
      state,
      _: { payload: Array<{ alert_id: string }> },
    ) => state,
    onFetchCostsAlerts: (
      draft,
      {
        payload: costsAlerts,
      }: {
        payload: CostsAlertApiObject
      },
    ) => {
      draft.costsAlerts = costsAlerts.alerts || []
      draft.costsDrivers = costsAlerts.drivers || []
      draft.costsVehicles = costsAlerts.vehicles || []
    },
    onCreateCostsAlert: (
      draft,
      { payload: createdCostsAlert }: { payload: CostsAlert },
    ) => {
      draft.costsAlerts.push(createdCostsAlert)
    },
    onUpdateCostsAlert: (
      draft,
      { payload: updatedCostsAlert }: { payload: CostsAlert },
    ) => {
      const costsAlertsIndex = draft.costsAlerts.findIndex(
        (costsAlert) => costsAlert.alertId === updatedCostsAlert.alertId,
      )

      if (costsAlertsIndex !== -1) {
        draft.costsAlerts[costsAlertsIndex] = updatedCostsAlert
      }
    },
    onDismissCostsAlert: (draft, { payload: dismissedId }: { payload: string }) => {
      const dismissedCostsAlertIndex = draft.costsAlerts.findIndex(
        (costsAlert) => costsAlert.alertId === dismissedId,
      )

      if (dismissedCostsAlertIndex !== -1) {
        draft.costsAlerts[dismissedCostsAlertIndex].isDismissed = true
      }
    },
    onFetchCostsAlertsNotifications: (
      draft,
      { payload: costsAlertsNotifications }: { payload: Array<CostsAlertNotification> },
    ) => {
      draft.costsAlertsNotifications = costsAlertsNotifications
    },
    onMarkAlertsNotificationsAsRead: (
      draft,
      { payload: readAlertsNotifications }: { payload: Array<{ alert_id: string }> },
    ) => {
      // Update costsAlerts list
      readAlertsNotifications.map((readAlertNotification) => {
        const costsAlertsIndex = draft.costsAlerts.findIndex(
          (costsAlert) => costsAlert.alertId === readAlertNotification.alert_id,
        )

        if (costsAlertsIndex !== -1) {
          draft.costsAlerts[costsAlertsIndex].isRead = true
        }

        // Update costsAlertsNotifications list
        const costsAlertsNotificationsIndex = draft.costsAlertsNotifications.findIndex(
          (costsAlertNotification) =>
            costsAlertNotification.alertId === readAlertNotification.alert_id,
        )

        if (costsAlertsNotificationsIndex !== -1) {
          draft.costsAlertsNotifications.splice(costsAlertsNotificationsIndex, 1)
        }
      })
    },
    removeAlertsNotifications: (
      draft,
      { payload: alertsNotifications }: { payload: Array<{ alert_id: string }> },
    ) => {
      draft.costsAlertsNotifications = draft.costsAlertsNotifications.filter(
        (alertNotification: CostsAlertNotification) =>
          !alertsNotifications.find(
            (alert) => alertNotification.alertId === alert.alert_id,
          ),
      )
    },
  },
})

const getReducerState = (state: AppState) => state.costsAlerts as State
const statusSelectors = createStatusSelectors(getReducerState)

const getAlertNotificationIcon = (
  warningTypeId: string,
  costsAlertTypes: CostsAlertTypes,
): {
  name: IconName
  prefix: IconPrefix
  color: string
} => {
  const alertType = costsAlertTypes[warningTypeId]

  if (alertType) {
    switch (alertType.type) {
      case 'Vehicle':
        return {
          name: 'car',
          prefix: 'fas',
          color: '#FF7E00',
        }
      case 'Driver':
        return {
          name: 'user',
          prefix: 'fas',
          color: '#B5D55E',
        }
      case 'Fuel':
        return {
          name: 'credit-card',
          prefix: 'fal',
          color: '#53B8C6',
        }
      case 'Integration':
      case 'Report':
      case 'Import':
      case 'Login':
        return {
          name: 'chart-line',
          prefix: 'fas',
          color: '#F47735',
        }
      default:
        return {
          name: 'chart-line',
          prefix: 'fas',
          color: '#F47735',
        }
    }
  }

  return {
    name: 'dollar-sign',
    prefix: 'fas',
    color: '#F47735',
  }
}

const getCostsAlertsNotifications = createSelector(
  getCostsAlertTypes,
  (state: AppState) => getReducerState(state).costsAlertsNotifications,
  (costsAlertTypes, costsAlertsNotifications) =>
    costsAlertsNotifications.slice(0, 5).map((costsAlertNotification) => ({
      timestamp: costsAlertNotification.warningDate
        ? new Date(costsAlertNotification.warningDate).getTime()
        : undefined,
      time: costsAlertNotification.warningDate
        ? format(new Date(costsAlertNotification.warningDate), 'p')
        : undefined,
      when: costsAlertNotification.warningDate
        ? formatDistanceStrict(
            new Date(costsAlertNotification.warningDate),
            new Date(),
            {
              addSuffix: true,
            },
          )
        : undefined,
      title:
        costsAlertTypes[costsAlertNotification.warningTypeId]?.description ||
        'Alert Notification',
      message: costsAlertNotification.message || '',
      icon: getAlertNotificationIcon(
        costsAlertNotification.warningTypeId || '',
        costsAlertTypes,
      ),
      alertId: costsAlertNotification.alertId || '',
      alertConfigurationId: costsAlertNotification.alertConfigurationId || '',
      documentId: costsAlertNotification.documentId || '',
      type: 'costs' as const,
    })) || [],
)

const getCostsAlerts = createSelector(
  getCostsAlertTypes,
  getCachedVehicles,
  getUsers,
  getDrivers,
  (state: AppState) => getReducerState(state).costsAlerts,
  (costsAlertTypes, vehicles, users, drivers, costsAlerts) =>
    costsAlerts.map((costsAlert) => ({
      ...costsAlert,
      vehicleRegistration: Object.prototype.hasOwnProperty.call(
        vehicles,
        costsAlert.vehicleId,
      )
        ? vehicles[costsAlert.vehicleId].registration
        : null,
      username:
        users.find((user) => Number(user.id) === costsAlert.userId)?.username || '',
      driver: drivers.find((driver) => driver.id === costsAlert.driverId)?.name || '',
      warningDescription: costsAlertTypes[costsAlert.warningTypeId]?.description || '',
    })) || [],
)

const getCostsAlertsById = createSelector(
  getCostsAlerts,
  (_: AppState, id: string) => id,
  (costsAlerts, id) =>
    costsAlerts.find((costsAlert) => costsAlert.alertConfigurationId === id),
)

const selectors = {
  ...statusSelectors,
  getCostsAlerts,
  getCostsAlertsById,
  getCostsAlertsNotifications,
  getCostsAlertsDrivers: (state: AppState) =>
    getReducerState(state).costsDrivers.map((d) => ({
      ...d,
      name: d.short_name,
      label: d.short_name,
      value: d.driver_id,
    })),
  getCostsAlertsVehicles: (state: AppState) =>
    getReducerState(state).costsVehicles.map((v) => ({
      ...v,
      name: v.plate,
      label: v.plate,
      value: v.vehicle_id,
    })),
  getCostsAlertsNotificationsCount: (state: AppState) =>
    getReducerState(state).costsAlertsNotifications.length,
  isCostsAlertsLoading: statusSelectors.isSomeLoading,
}

export { reducer, actions, selectors }
