import { createSlice, createSelector, type PayloadAction } from '@reduxjs/toolkit'
import { apiStatusReducers, createStatusSelectors } from 'duxs/utils'
import type {
  CostsActiveAlert,
  CostsActiveAlertUserOptions,
  CostsActiveAlertUser,
  CostsAlertTypes,
  CostsActiveAlertVehicle,
  CostsActiveAlertDriver,
} from 'src/types/api/mifleet/alerts'
import type { AppState } from 'src/root-reducer'

type State = {
  costsActiveAlerts: Array<CostsActiveAlert>
  costsAlertTypes: CostsAlertTypes
  users: Array<CostsActiveAlertUser>
  mifleetDrivers: Array<CostsActiveAlertDriver>
  mifleetVehicles: Array<CostsActiveAlertVehicle>
}

const initialState: State = {
  costsActiveAlerts: [],
  costsAlertTypes: {},
  users: [],
  mifleetDrivers: [],
  mifleetVehicles: [],
}

const { reducer, actions } = createSlice({
  name: 'costsActiveAlerts',
  initialState,
  reducers: {
    ...apiStatusReducers,
    fetchCostsActiveAlerts: (state) => state,
    fetchCostsAlertTypes: (state) => state,
    createCostsActiveAlert: (state, _: { payload: CostsActiveAlert }) => state,
    updateCostsActiveAlert: (state, _: { payload: CostsActiveAlert }) => state,
    deleteCostsActiveAlert: (state, _: { payload: string }) => state,
    clearCostsActiveAlerts: (draft) => {
      draft.costsActiveAlerts = []
    },

    onFetchCostsActiveAlerts: (
      draft,
      {
        payload: { costsActiveAlerts, users, mifleetDrivers, mifleetVehicles },
      }: {
        payload: {
          costsActiveAlerts: Array<CostsActiveAlert>
          users: Array<CostsActiveAlertUser>
          mifleetDrivers: Array<CostsActiveAlertDriver>
          mifleetVehicles: Array<CostsActiveAlertVehicle>
        }
      },
    ) => {
      draft.costsActiveAlerts = costsActiveAlerts
      draft.users = users
      draft.mifleetDrivers = mifleetDrivers
      draft.mifleetVehicles = mifleetVehicles
    },
    onFetchCostsAlertTypes: (
      draft,
      { payload: costsAlertTypes }: PayloadAction<CostsAlertTypes>,
    ) => {
      draft.costsAlertTypes = costsAlertTypes
    },
    onDeleteCostsActiveAlert: (draft, { payload: deletedId }: { payload: string }) => {
      const deletedCostsActiveAlertIndex = draft.costsActiveAlerts.findIndex(
        (costsActiveAlert) => costsActiveAlert.alertConfigurationId === deletedId,
      )

      if (deletedCostsActiveAlertIndex !== -1) {
        draft.costsActiveAlerts.splice(deletedCostsActiveAlertIndex, 1)
      }
    },
  },
})

const getReducerState = (state: AppState) => state.costsActiveAlerts as State
const statusSelectors = createStatusSelectors(getReducerState)

const getCostsAlertsMifleetDrivers = (state: AppState) =>
  getReducerState(state).mifleetDrivers || []

const getCostsAlertsMifleetVehicles = (state: AppState) =>
  getReducerState(state).mifleetVehicles || []

const getCostsAlertTypes = (state: AppState) =>
  getReducerState(state).costsAlertTypes || {}

const getCostsAlertTypesOptions = createSelector(
  getCostsAlertTypes,
  (costsAlertTypes) =>
    Object.keys(costsAlertTypes).map((key) => ({
      name: costsAlertTypes[key]?.description,
      value: key,
    })) || [],
)

const getCostsActiveAlerts = createSelector(
  getCostsAlertTypes,
  (state: AppState) => getReducerState(state).costsActiveAlerts || [],
  (costsAlertTypes, costsActiveAlerts) =>
    costsActiveAlerts.map((costsActiveAlert) => {
      const alertEmails = costsActiveAlert.alertEmails
        .map((alertEmail) => alertEmail.email)
        .filter((email) => email) // Remove undefined

      const alertPhones = costsActiveAlert.alertPhones
        .map((alertPhone) => alertPhone.phone)
        .filter((phone) => phone) // Remove undefined

      return {
        ...costsActiveAlert,
        contact: alertEmails.concat(alertPhones).join(', '),
        description: costsAlertTypes[costsActiveAlert.warningTypeId]?.description || '',
      }
    }),
)

const getCostsActiveAlertsUsers = (state: AppState) =>
  getReducerState(state).users || []

const getCostsActiveAlertsById = createSelector(
  getCostsActiveAlerts,
  (_: AppState, id: string) => id,
  (costsActiveAlerts, id) =>
    costsActiveAlerts.find(
      (costActiveAlert) => Number(costActiveAlert.alertConfigurationId) === Number(id), // Force Number just in case the type changes in the BE
    ),
)

const getCostsActiveAlertsUsersOptions = createSelector(
  getCostsActiveAlertsUsers,
  (costsActiveAlertsUsers: Array<CostsActiveAlertUser>) => {
    if (costsActiveAlertsUsers.length > 0) {
      const getMultiSelectOptions = (
        user: CostsActiveAlertUser,
      ): CostsActiveAlertUserOptions => ({
        key: 'user',
        label: user.username,
        value: user.fleet_user_id,
        email: user.email,
        cell_number: user.cell_number,
      })
      // Dropdown + MultiSelect objects
      return costsActiveAlertsUsers
        .map(getMultiSelectOptions)
        .sort((a, b) => a.label.localeCompare(b.label))
    }

    return []
  },
)

const selectors = {
  ...statusSelectors,
  getCostsActiveAlerts,
  getCostsActiveAlertsById,
  getCostsAlertTypes,
  getCostsAlertTypesOptions,
  getCostsActiveAlertsUsers,
  getCostsActiveAlertsUsersOptions,
  getCostsAlertsMifleetDrivers,
  getCostsAlertsMifleetVehicles,
  isCostsActiveAlertsLoading: statusSelectors.isSomeLoading,
}

export { reducer, actions, selectors }
