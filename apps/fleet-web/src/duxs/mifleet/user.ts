import { createAction } from '@reduxjs/toolkit'
import { isEmpty, isNil } from 'lodash'
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'
import { doesCurrentUserHaveAccessFromSetting } from '../user'

/* NOTE
This reducer was explicitly done without immer due to redux-persist not playing by the rules.
It tries to mutate the state outside of the reducer which immer does not allow. There are workarounds but we rather make it explicit this way.

More info: https://github.com/rt2zz/redux-persist/issues/747
*/

type State = {
  permissions: {
    [tag: string]: {
      tag: string
      granted: boolean
    }
  }
}

const initialState: State = {
  permissions: {},
}

const actions = {
  receivePermissions: createAction<State['permissions']>(
    'mifleetUser/receivePermissions',
  ),
}

function reducer(state = initialState, action: FixMeAny): State {
  const { payload } = action
  switch (action.type) {
    case actions.receivePermissions.type: {
      return {
        ...state,
        permissions: payload,
      }
    }

    default:
      return state
  }
}

const getReducerState = (state: AppState) => state.mifleetUser

export const getMifleetUserPermissions = (state: AppState) =>
  getReducerState(state).permissions

export const canCurrentMifleetUserPerform = (
  state: AppState,
  action: MifleetPermissionsTags,
) => {
  const isMifleetUser = doesCurrentUserHaveAccessFromSetting(state, 'costs')

  // It only makes sense to check permissions if the user has indeed logged in mifleet (which is where Permissions object comes from)
  if (isMifleetUser) {
    const permissions = getMifleetUserPermissions(state)
    const permission = permissions[action]

    let errorMessage
    if (isEmpty(permissions)) {
      errorMessage = `[Cartrack] - You tried to access permission ${JSON.stringify(
        action,
      )} when Permissions object was not yet initialized.`
    } else if (isEmpty(permissions) === false && permission === undefined) {
      errorMessage = `[Cartrack] - There is no permission tag with name ${JSON.stringify(
        action,
      )}`
    }

    if (errorMessage !== undefined) {
      if (ENV.NODE_ENV !== 'production') {
        throw new Error(errorMessage)
      }

      // It will be logged to sentry but not crash the app since it's production
      console.error(errorMessage)
    }

    if (!isNil(permission)) {
      return permission.granted
    }
  }

  return false
}

export { reducer, actions }

type MifleetPermissionsTags =
  | 'PERMISSION_GENERAL_SETTINGS_COST_CENTRES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_COST_CENTRES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_COST_CENTRES_LIST'
  | 'PERMISSION_GENERAL_SETTINGS_DRIVER_COST_TYPES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_DRIVER_COST_TYPES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_DRIVER_COST_TYPES_LIST'
  | 'PERMISSION_GENERAL_SETTINGS_DRIVER_LEAVE_TYPES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_DRIVER_LEAVE_TYPES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_DRIVER_LEAVE_TYPES_LIST'
  | 'PERMISSION_GENERAL_SETTINGS_DRIVER_PERMIT_TYPES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_DRIVER_PERMIT_TYPES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_DRIVER_PERMIT_TYPES_LIST'
  | 'PERMISSION_GENERAL_SETTINGS_IMPORTING_TYPES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_IMPORTING_TYPES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_IMPORTING_TYPES_VIEW'
  | 'PERMISSION_GENERAL_SETTINGS_INSURANCE_TYPES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_INSURANCE_TYPES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_INSURANCE_TYPES_VIEW'
  | 'PERMISSION_GENERAL_SETTINGS_INTEGRATION_TYPES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_INTEGRATION_TYPES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_INTEGRATION_TYPES_VIEW'
  | 'PERMISSION_GENERAL_SETTINGS_MENU_VIEW'
  | 'PERMISSION_GENERAL_SETTINGS_PAYMENT_METHODS_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_PAYMENT_METHODS_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_PAYMENT_METHODS_LIST'
  | 'PERMISSION_GENERAL_SETTINGS_STAFF_POSITION_TYPES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_STAFF_POSITION_TYPES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_STAFF_POSITION_TYPES_LIST'
  | 'PERMISSION_GENERAL_SETTINGS_SUPPLIERS_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_SUPPLIERS_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_SUPPLIERS_LIST'
  | 'PERMISSION_GENERAL_SETTINGS_SUPPLIER_TYPES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_SUPPLIER_TYPES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_SUPPLIER_TYPES_LIST'
  | 'PERMISSION_GENERAL_SETTINGS_TAXES_DELETE'
  | 'PERMISSION_GENERAL_SETTINGS_TAXES_EDIT'
  | 'PERMISSION_GENERAL_SETTINGS_TAXES_LIST'
  | 'PERMISSION_CONFIGURATION_PERMISSION_ROLES_DELETE'
  | 'PERMISSION_CONFIGURATION_PERMISSION_ROLES_EDIT'
  | 'PERMISSION_CONFIGURATION_PERMISSION_ROLES_LIST'
