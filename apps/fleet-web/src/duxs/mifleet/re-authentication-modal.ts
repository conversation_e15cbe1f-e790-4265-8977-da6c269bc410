import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { AppState } from 'src/root-reducer'

type State = {
  isOpen: boolean
  passwordBuffer: number
  isValidatingPassword: boolean
  error: string
}

const getInitialState = (): State => ({
  isOpen: false,
  passwordBuffer: 3,
  isValidatingPassword: false,
  error: '',
})

const { reducer, actions } = createSlice({
  name: 're-authentication-modal',
  initialState: getInitialState(),
  reducers: {
    close: (_draft) => {},
    open: (draft) => {
      draft.isOpen = true
    },
    reset: getInitialState,
    validateLoginCredentials: (draft, _: PayloadAction<string>) => {
      draft.isValidatingPassword = true
      draft.error = ''
    },
    onValidationFailure: (draft) => {
      draft.passwordBuffer = draft.passwordBuffer - 1
      draft.isValidatingPassword = false
      draft.error = 'Incorrect Password'
    },
    onLoginGenericFailure: (draft, { payload: errorMessage }: { payload: string }) => {
      draft.isValidatingPassword = false
      draft.error = errorMessage
    },
  },
})

const getReducerState = (state: AppState) => state.reAuthenticationModal

const selectors = {
  getIsOpen: (state: AppState) => getReducerState(state).isOpen,
  getIsValidatingPassword: (state: AppState) =>
    getReducerState(state).isValidatingPassword,
  getError: (state: AppState) => getReducerState(state).error,
  getPasswordBuffer: (state: AppState) => getReducerState(state).passwordBuffer,
}

export { reducer, actions, selectors }
