import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { FetchUserRoles, FetchUserRole } from 'src/api/mifleet/user-roles/types'
import type { AppState } from 'src/root-reducer'
import { createStatusSelectors, apiStatusReducers } from 'duxs/utils'

type State = {
  roles: FetchUserRoles.Return
  currentUserRole: FetchUserRole.Return | undefined
}

const initialState: State = {
  roles: [],
  currentUserRole: undefined,
}

const { actions, reducer } = createSlice({
  name: 'user-roles',
  initialState,
  reducers: {
    ...apiStatusReducers,
    resetState: () => initialState,
    /* List */
    fetchUserRoles: (draft) => draft,
    createUserRole: (draft, _: PayloadAction<{ tag: string }>) => draft,
    updateUserRoleFeaturePermissions: (
      draft,
      _: PayloadAction<{
        userRoleId: number
        permissions: Array<FetchUserRole.FeaturePermission>
      }>,
    ) => draft,
    onUpdateUserRoleFeaturePermissions: (
      draft,
      { payload: permissions }: PayloadAction<Array<FetchUserRole.FeaturePermission>>,
    ) => {
      permissions.forEach((permission) => {
        if (draft.currentUserRole !== undefined) {
          draft.currentUserRole.featuresData.permissions[permission.id].checked =
            permission.checked
        }
      })
    },
    updateUserRoleDetails: (draft, _: PayloadAction<{ tag: string }>) => draft,
    onUpdateUserRoleDetails: (
      draft,
      { payload: { tag } }: PayloadAction<{ tag: string }>,
    ) => {
      if (draft.currentUserRole !== undefined) {
        draft.currentUserRole.tag = tag
      }
    },
    onFetchUserRoles: (draft, { payload }: PayloadAction<FetchUserRoles.Return>) => {
      draft.roles = payload
    },
    deleteUserRole: (draft, _: PayloadAction<number>) => draft,
    onDeleteUserRole: (draft, { payload }: PayloadAction<number>) => {
      draft.roles.splice(
        draft.roles.findIndex((r) => r.id === payload),
        1,
      )
    },

    /* Details */
    fetchUserRole: (draft, _: PayloadAction<number>) => draft,
    onFetchUserRole: (draft, { payload }: PayloadAction<FetchUserRole.Return>) => {
      draft.currentUserRole = payload
    },

    // Vehicles
    updateUserRoleVehicles: (
      draft,
      _: PayloadAction<{
        userRoleId: number
        vehicles: Array<Pick<FetchUserRole.Vehicle, 'id' | 'allowed'>>
      }>,
    ) => draft,
    onUpdateUserRoleVehicles: (
      draft,
      {
        payload: updatedVehicles,
      }: PayloadAction<Array<Pick<FetchUserRole.Vehicle, 'id' | 'allowed'>>>,
    ) => {
      updatedVehicles.forEach((v) => {
        if (draft.currentUserRole !== undefined) {
          draft.currentUserRole.vehiclesData.vehicles[v.id].allowed = v.allowed
        }
      })
    },

    // Drivers
    updateUserRoleDriver: (
      draft,
      _: PayloadAction<{
        userRoleId: number
        allowed: boolean
        driverId: string
      }>,
    ) => draft,
    onUpdateUserRoleDriver: (
      draft,
      {
        payload,
      }: PayloadAction<{
        id: string
        allowed: boolean
      }>,
    ) => {
      if (draft.currentUserRole !== undefined) {
        draft.currentUserRole.driversData.drivers[payload.id].allowed = payload.allowed
      }
    },

    //Reports
    updateUserRoleReport: (
      draft,
      _: PayloadAction<{
        userRoleId: number
        allowed: boolean
        reportId: number
      }>,
    ) => draft,
    onUpdateUserRoleReport: (
      draft,
      { payload }: PayloadAction<Pick<FetchUserRole.Report, 'id' | 'allowed'>>,
    ) => {
      if (draft.currentUserRole !== undefined) {
        const index = draft.currentUserRole.reportsData.findIndex(
          (r) => r.id === payload.id,
        )
        if (index !== -1) {
          draft.currentUserRole.reportsData[index].allowed = payload.allowed
        }
      }
    },
  },
})

const getReducerState = (state: AppState) => state.userRoles as State
const statusSelectors = createStatusSelectors(getReducerState)
const { isLoadingFor } = statusSelectors

const selectors = {
  getUserRoles: (state: AppState) => getReducerState(state).roles,
  getIsLoadingUserRoles: (state: AppState) =>
    isLoadingFor(state, actions.fetchUserRoles),

  getCurrentUserRoleDetails: (state: AppState) =>
    getReducerState(state).currentUserRole,
  getIsLoadingCurrentUserRoleDetails: (state: AppState) =>
    isLoadingFor(state, actions.fetchUserRole),
  getIsCreatingOrUpdatingUserRole: (state: AppState) =>
    isLoadingFor(state, actions.createUserRole) ||
    isLoadingFor(state, actions.updateUserRoleDetails),

  getIsUpdatingVehicles: (state: AppState) =>
    isLoadingFor(state, actions.updateUserRoleVehicles),
  getIsUpdatingReport: (state: AppState) =>
    isLoadingFor(state, actions.updateUserRoleReport),
  getIsUpdatingDriver: (state: AppState) =>
    isLoadingFor(state, actions.updateUserRoleDriver),
  getIsUpdatingFeaturePermissions: (state: AppState) =>
    isLoadingFor(state, actions.updateUserRoleFeaturePermissions),

  ...statusSelectors,
}

export { reducer, actions, selectors }
