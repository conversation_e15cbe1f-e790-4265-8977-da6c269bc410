import { createSlice } from '@reduxjs/toolkit'
import { apiStatusReducers, createStatusSelectors } from 'duxs/utils'

const { actions, reducer } = createSlice({
  name: 'operational',
  initialState: {
    // GROUPED COSTS
    groupedCosts: [],
    // MISC
    misc: [],
    // TOLL FRAUD
    tollFraud: undefined,
    // DASHBOARD
    operationalDashboard: [],
    // FUELLING
    operationalFuel: [],
    operationalFuelDrivers: [],
    operationalFuelCards: [],
    operationalFuelTransactions: [],
    // TOLL
    operationalToll: [],
    // CLEANING
    operationalCleaning: [],
    operationalCleaningTypes: [],
    // MAINTENANCE
    operationalMaintenance: [],
    operationalMaintenanceTypes: [],
    // TOWING
    operationalTowing: [],
    operationalTowingDrivers: [],
    operationalTowingTypes: [],
    // FINES
    operationalFines: [],
    operationalFinesTypes: [],
    operationalFinesDrivers: [],
    // DRIVER COSTS
    operationalDriverCosts: [],
    operationalDriverCostsTypes: [],
    // TIRES
    operationalTires: [],
    operationalTiresLocationTypes: [],
    operationalTiresOperationTypes: [],
    // INCIDENTS
    operationalIncidents: [],
    operationalIncidentsTypes: [],
    operationalIncidentsDriversTypes: [],
    // OIL
    operationalOils: [],
    operationalOilsTypes: [],
    // CONSUMABLES
    operationalConsumables: [],
    operationalConsumablesTypes: [],
    // RENTAL COSTS
    operationalRentalCosts: [],
    operationalRentalCostsTypes: [],
    // LEASING COSTS
    operationalLeasingCosts: [],
    operationalLeasingCostsTypes: [],
  },
  reducers: {
    ...apiStatusReducers,
    // DASHBOARD
    fetchOperationalDashboard: (state, { _payload }) => state,
    onFetchOperationalDashboard: (draft, { payload }) => {
      draft.operationalDashboard = payload
    },
    // MISC
    fetchOperationalMisc: (state, { _payload }) => state,
    onFetchOperationalMisc: (draft, { payload }) => {
      draft.misc = payload.list
    },
    // GROUPED COSTS
    fetchOperationalGrouped: (state, { _payload }) => state,
    onFetchOperationalGrouped: (draft, { payload }) => {
      draft.groupedCosts = payload
    },
    // TOLL FRAUD
    fetchOperationalTollFraud: (state, { _payload }) => state,
    onFetchOperationalTollFraud: (draft, { payload }) => {
      draft.tollFraud = payload
    },
    updateOperationalTollFraud: (state, { _payload }) => state,
    onUpdateOperationalTollFraud: () => {
      //draft.tollFraud = payload
    },
    addNewFraudTollStation: (draft, _payload) => draft,
    onAddNewFraudTollStation: (draft, { payload: data }) => {
      const index = draft.operationalToll.findIndex(
        (item) => item.document_line_id === data.document_line_id,
      )
      const detailsToUpdateEntry = {
        fleet_check_result_id: 5,
        fleet_check: true,
        validation_status: 'Validated',
        toll_validation_status_id: 1,
        possible_toll_stations: [
          {
            toll_station_id: data.toll_station_id,
            toll_station: data.toll_station,
            latitude: data.latitude,
            longitude: data.longitude,
          },
        ],
      }
      const updatedItem = {
        ...draft.operationalToll[index],
        ...detailsToUpdateEntry,
      }
      draft.operationalToll.splice(index, 1, updatedItem)
      draft.tollFraud = {
        ...draft.tollFraud,
        ...detailsToUpdateEntry,
      }
    },
    // FUELLING
    fetchOperationalFuel: (state, { _payload }) => state,
    updateOperationalFuel: (state) => state,
    onFetchOperationalFuel: (draft, { payload }) => {
      draft.operationalFuel = payload.list
      draft.operationalFuelTotals = payload.totals

      draft.operationalFuelDrivers = payload.drivers
      draft.operationalFuelCards = payload.contractFuelCards
      draft.operationalFuelTransactions = payload.fuelTransactionTypes
    },
    onUpdateOperationalFuel: (draft, { payload }) => {
      const index = draft.operationalFuel.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalFuel.splice(index, 1, payload)
      }
    },

    // TOLL
    fetchOperationalToll: (state, { _payload }) => state,
    updateOperationalToll: (state) => state,
    onFetchOperationalToll: (draft, { payload }) => {
      draft.operationalToll = payload.list
      draft.operationalTollTotals = payload.totals
    },
    onUpdateOperationalToll: (draft, { payload }) => {
      const index = draft.operationalToll.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalToll.splice(index, 1, payload)
      }
    },

    // CLEANING
    fetchOperationalCleaning: (state) => state,
    updateOperationalCleaning: (state) => state,
    onFetchOperationalCleaning: (draft, { payload }) => {
      draft.operationalCleaning = payload.list
      draft.operationalCleaningTotals = payload.totals
      draft.operationalCleaningTypes = payload.cleaningTypes
    },
    onUpdateOperationalCleaning: (draft, { payload }) => {
      const index = draft.operationalCleaning.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalCleaning.splice(index, 1, payload)
      }
    },

    // MAINTENANCE
    fetchOperationalMaintenance: (state, { _payload }) => state,
    updateOperationalMaintenance: (state) => state,
    onFetchOperationalMaintenance: (draft, { payload }) => {
      draft.operationalMaintenance = payload.list
      draft.operationalMaintenanceTotals = payload.totals
      draft.operationalMaintenanceTypes = payload.maintenanceTypes
    },
    onUpdateOperationalMaintenance: (draft, { payload }) => {
      const index = draft.operationalMaintenance.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalMaintenance.splice(index, 1, payload)
      }
    },

    // TOWING
    fetchOperationalTowing: (state) => state,
    updateOperationalTowing: (state) => state,
    onFetchOperationalTowing: (draft, { payload }) => {
      draft.operationalTowing = payload.list
      draft.operationalTowingTotals = payload.totals
      draft.operationalTowingDrivers = payload.drivers
      draft.operationalTowingTypes = payload.breakdownTypes
    },
    onUpdateOperationalTowing: (draft, { payload }) => {
      const index = draft.operationalTowing.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalTowing.splice(index, 1, payload)
      }
    },

    // FINES
    fetchOperationalFines: (state, { _payload }) => state,
    updateOperationalFines: (state) => state,
    onFetchOperationalFines: (draft, { payload }) => {
      draft.operationalFines = payload.list
      draft.operationalFinesTotals = payload.totals
      draft.operationalFinesDrivers = payload.drivers
      draft.operationalFinesTypes = payload.fineTypes
    },
    onUpdateOperationalFines: (draft, { payload }) => {
      const index = draft.operationalFines.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalFines.splice(index, 1, payload)
      }
    },

    // DRIVER COSTS
    fetchOperationalDriverCosts: (state) => state,
    updateOperationalDriverCosts: (state) => state,
    onFetchOperationalDriverCosts: (draft, { payload }) => {
      draft.operationalDriverCosts = payload.list
      draft.operationalDriverCostsTotals = payload.totals
      draft.operationalDriverCostsTypes = payload.driverCostTypes
    },
    onUpdateOperationalDriverCosts: (draft, { payload }) => {
      const index = draft.operationalDriverCosts.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalDriverCosts.splice(index, 1, payload)
      }
    },

    // TIRES
    fetchOperationalTires: (state, { _payload }) => state,
    updateOperationalTires: (state) => state,
    onFetchOperationalTires: (draft, { payload }) => {
      draft.operationalTires = payload.list
      draft.operationalTiresTotals = payload.totals
      draft.operationalTiresLocationTypes = payload.tireLocationTypes
      draft.operationalTiresOperationTypes = payload.tireOperationTypes
    },
    onUpdateOperationalTires: (draft, { payload }) => {
      const index = draft.operationalTires.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalTires.splice(index, 1, payload)
      }
    },
    deleteOperationalTiresTire: (state) => state,
    onDeleteOperationalTiresTire: (draft, { payload }) => {
      const index = draft.operationalTires.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        const line = { ...draft.operationalTires[index] }

        const indexTire = line.tires.findIndex(
          (o) => o.tyre_tire_id === payload.tyre_tire_id,
        )

        const lineTires = line.tires

        lineTires.splice(indexTire, 1)
        line.tires = lineTires

        draft.operationalTires.splice(index, 1, line)
      }
    },
    updateOperationalTiresTire: (state) => state,
    onUpdateOperationalTiresTire: (draft, { payload }) => {
      const index = draft.operationalTires.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        const line = { ...draft.operationalTires[index] }

        const indexTire = line.tires.findIndex(
          (o) => o.tyre_tire_id === payload.tyre_tire_id,
        )

        const lineTires = line.tires

        if (indexTire === -1) {
          lineTires.unshift(payload)

          const undIndex = lineTires.findIndex((o) => o.isAdd === true)
          lineTires.splice(undIndex, 1)
        } else {
          lineTires.splice(indexTire, 1, payload)
        }

        line.tires = lineTires

        draft.operationalTires.splice(index, 1, line)
      }
    },
    resetTiresTireListLocal: (draft, { payload }) => {
      const index = draft.operationalTires.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        const line = { ...draft.operationalTires[index] }

        line.tires = Array.isArray(payload.tires) ? payload.tires : payload.tires.tires

        draft.operationalTires.splice(index, 1, line)
      }
    },
    addOperationalTiresTireLocal: (draft, { payload }) => {
      const index = draft.operationalTires.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        const line = { ...draft.operationalTires[index] }

        const lineTires = [...line.tires]

        lineTires.unshift(payload.newTire)
        line.tires = lineTires

        draft.operationalTires.splice(index, 1, line)
      }
    },
    setOperationalTires: (draft, { payload: tires }) => {
      draft.operationalTires = tires
    },

    // INCIDENTS
    fetchOperationalIncidents: (state, { _payload }) => state,
    updateOperationalIncidents: (state) => state,
    onFetchOperationalIncidents: (draft, { payload }) => {
      draft.operationalIncidents = payload.list
      draft.operationalIncidentsTotals = payload.totals
      draft.operationalIncidentsTypes = payload.incidentTypes
      draft.operationalIncidentsDriversTypes = payload.drivers
    },
    onUpdateOperationalIncidents: (draft, { payload }) => {
      const index = draft.operationalIncidents.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalIncidents.splice(index, 1, payload)
      }
    },
    deleteOperationalIncidentsTP: (state) => state,
    onDeleteOperationalIncidentsTP: (draft, { payload }) => {
      const index = draft.operationalIncidents.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        const line = { ...draft.operationalIncidents[index] }

        const indexTP = line.thirdParties.findIndex(
          (o) => o.incident_third_party_id === payload.incident_third_party_id,
        )

        const lineTP = line.thirdParties

        lineTP.splice(indexTP, 1)
        line.thirdParties = lineTP

        draft.operationalIncidents.splice(index, 1, line)
      }
    },
    updateOperationalIncidentsTP: (state) => state,
    onUpdateOperationalIncidentsTP: (draft, { payload }) => {
      const index = draft.operationalIncidents.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        const line = { ...draft.operationalIncidents[index] }

        const indexTP = line.thirdParties.findIndex(
          (o) => o.incident_third_party_id === payload.incident_third_party_id,
        )

        const lineTP = line.thirdParties

        if (indexTP === -1) {
          lineTP.unshift(payload)

          const undIndex = lineTP.findIndex((o) => o.isAdd === true)
          lineTP.splice(undIndex, 1)
        } else {
          lineTP.splice(indexTP, 1, payload)
        }

        line.thirdParties = lineTP

        draft.operationalIncidents.splice(index, 1, line)
      }
    },
    resetIncidentsTPListLocal: (draft, { payload }) => {
      const index = draft.operationalIncidents.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        const line = { ...draft.operationalIncidents[index] }

        line.thirdParties = Array.isArray(payload.thirdParties)
          ? payload.thirdParties
          : payload.thirdParties.thirdParties

        draft.operationalIncidents.splice(index, 1, line)
      }
    },
    addOperationalIncidentsTPLocal: (draft, { payload }) => {
      const index = draft.operationalIncidents.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        const line = { ...draft.operationalIncidents[index] }

        const lineTP = [...line.thirdParties]

        lineTP.unshift(payload.newTP)
        line.thirdParties = lineTP

        draft.operationalIncidents.splice(index, 1, line)
      }
    },
    fetchOperationalOils: (state) => state,
    onFetchOperationalOils: (draft, { payload }) => {
      draft.operationalOils = payload.list
      draft.operationalOilsTotals = payload.totals
      draft.operationalOilsTypes = payload.oilTypes
    },
    updateOperationalOils: (state) => state,
    onUpdateOperationalOils: (draft, { payload }) => {
      const index = draft.operationalOils.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalOils.splice(index, 1, payload)
      }
    },

    fetchOperationalConsumables: (state) => state,
    onFetchOperationalConsumables: (draft, { payload }) => {
      draft.operationalConsumables = payload.list
      draft.operationalConsumablesTotals = payload.totals
      draft.operationalConsumablesTypes = payload.consumableTypes
    },
    updateOperationalConsumables: (state) => state,
    onUpdateOperationalConsumables: (draft, { payload }) => {
      const index = draft.operationalConsumables.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalConsumables.splice(index, 1, payload)
      }
    },
    fetchOperationalRentalCosts: (state) => state,
    onFetchOperationalRentalCosts: (draft, { payload }) => {
      draft.operationalRentalCosts = payload.list
      draft.operationalRentalCostsTotals = payload.totals
      draft.operationalRentalCostsTypes = payload.rentalCostTypes
    },
    updateOperationalRentalCosts: (state) => state,
    onUpdateOperationalRentalCosts: (draft, { payload }) => {
      const index = draft.operationalRentalCosts.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalRentalCosts.splice(index, 1, payload)
      }
    },
    fetchOperationalLeasingCosts: (state) => state,
    onFetchOperationalLeasingCosts: (draft, { payload }) => {
      draft.operationalLeasingCosts = payload.list
      draft.operationalLeasingCostsTotals = payload.totals
      draft.operationalLeasingCostsTypes = payload.leasingCostTypes
    },
    updateOperationalLeasingCosts: (state) => state,
    onUpdateOperationalLeasingCosts: (draft, { payload }) => {
      const index = draft.operationalLeasingCosts.findIndex(
        (o) => o.document_line_id === payload.document_line_id,
      )

      if (index !== -1) {
        draft.operationalLeasingCosts.splice(index, 1, payload)
      }
    },
  },
  extraReducers: (builder) => {
    builder.addCase(
      'costsOverview/onFetchVehicleDetails',
      (draft, { payload: details }) => {
        draft.operationalIncidents = details.incidents
      },
    )
  },
})

const statusSelectors = createStatusSelectors((state) => state.operational)

const shouldNotCheckLoading = [
  // TIRES
  'deleteOperationalTiresTire',
  'onDeleteOperationalTiresTire',
  'updateOperationalTiresTire',
  'onUpdateOperationalTiresTire',
  // INCIDENTS
  'deleteOperationalIncidentsTP',
  'onDeleteOperationalIncidentsTP',
  'updateOperationalIncidentsTP',
  'onUpdateOperationalIncidentsTP',
  'fetchOperationalToll',
  'fetchOperationalIncidents',
  'fetchOperationalFines',
  'fetchOperationalFuel',
  'fetchOperationalMaintenance',
  'fetchOperationalTires',
]

const selectors = {
  ...statusSelectors,
  getOperationalLoading: (state) =>
    statusSelectors.isSomeLoadingForActions(state, shouldNotCheckLoading),

  // TOLL FRAUD
  allTollFraud: (state) => state.operational.tollFraud,
  // MISC
  allMisc: (state) => state.operational.misc,
  // GROUPED COSTS
  allGroupedCosts: (state) => state.operational.groupedCosts,
  // DASHBOARD
  allOperationalDashboard: (state) => state.operational.operationalDashboard,
  // FUELLING
  allOperationalFuel: (state) => state.operational.operationalFuel,
  allOperationalFuelTotals: (state) => state.operational.operationalFuelTotals,
  allOperationalFuelDrivers: (state) => state.operational.operationalFuelDrivers,
  allOperationalFuelCards: (state) => state.operational.operationalFuelCards,
  allOperationalFuelTransactions: (state) =>
    state.operational.operationalFuelTransactions,
  isOperationallFuelLoading: (state) =>
    statusSelectors.isLoadingFor(state, actions.fetchOperationalFuel),

  // TOLL
  allOperationalToll: (state) => state.operational.operationalToll,
  allOperationalTollTotals: (state) => state.operational.operationalTollTotals,
  isOperationalTollLoading: (state) =>
    statusSelectors.isLoadingFor(state, actions.fetchOperationalToll),
  // CLEANING
  allOperationalCleaning: (state) => state.operational.operationalCleaning,
  allOperationalCleaningTypes: (state) => state.operational.operationalCleaningTypes,
  allOperationalCleaningTotals: (state) => state.operational.operationalCleaningTotals,

  // MAINTENANCE
  allOperationalMaintenance: (state) => state.operational.operationalMaintenance,
  allOperationalMaintenanceTypes: (state) =>
    state.operational.operationalMaintenanceTypes,
  allOperationalMaintenanceTotals: (state) =>
    state.operational.operationalMaintenanceTotals,
  isOperationallMaintenanceLoading: (state) =>
    statusSelectors.isLoadingFor(state, actions.fetchOperationalMaintenance),

  // TOWING
  allOperationalTowing: (state) => state.operational.operationalTowing,
  allOperationalTowingDrivers: (state) => state.operational.operationalTowingDrivers,
  allOperationalTowingTypes: (state) => state.operational.operationalTowingTypes,
  allOperationalTowingTotals: (state) => state.operational.operationalTowingTotals,

  // FINES
  allOperationalFines: (state) => state.operational.operationalFines,
  allOperationalFinesDrivers: (state) => state.operational.operationalFinesDrivers,
  allOperationalFinesTypes: (state) => state.operational.operationalFinesTypes,
  allOperationalFinesTotals: (state) => state.operational.operationalFinesTotals,
  isOperationallFinesLoading: (state) =>
    statusSelectors.isLoadingFor(state, actions.fetchOperationalFines),

  // DRIVER COSTS
  allOperationalDriverCosts: (state) => state.operational.operationalDriverCosts,
  allOperationalDriverCostsTypes: (state) =>
    state.operational.operationalDriverCostsTypes,
  allOperationalDriverCostsTotals: (state) =>
    state.operational.operationalDriverCostsTotals,

  // TIRES
  allOperationalTires: (state) => state.operational.operationalTires,
  allOperationalTiresTotals: (state) => state.operational.operationalTiresTotals,
  allOperationalTiresLocationTypes: (state) =>
    state.operational.operationalTiresLocationTypes,
  allOperationalTiresOperationTypes: (state) =>
    state.operational.operationalTiresOperationTypes,
  isOperationallTireLoading: (state) =>
    statusSelectors.isLoadingFor(state, actions.fetchOperationalTires),

  // INCIDENTS
  allOperationalIncidents: (state) => state.operational.operationalIncidents,
  allOperationalIncidentsTotals: (state) =>
    state.operational.operationalIncidentsTotals,
  allOperationalIncidentsTypes: (state) => state.operational.operationalIncidentsTypes,
  allOperationalIncidentsDriversTypes: (state) =>
    state.operational.operationalIncidentsDriversTypes,
  isOperationallIncidentsLoading: (state) =>
    statusSelectors.isLoadingFor(state, actions.fetchOperationalIncidents),

  // OILS
  allOperationalOils: (state) => state.operational.operationalOils,
  allOperationalOilsTypes: (state) => state.operational.operationalOilsTypes,
  allOperationalOilsTotals: (state) => state.operational.operationalOilsTotals,

  // CONSUMABLES
  allOperationalConsumables: (state) => state.operational.operationalConsumables,
  allOperationalConsumablesTypes: (state) =>
    state.operational.operationalConsumablesTypes,
  allOperationalConsumablesTotals: (state) =>
    state.operational.operationalConsumablesTotals,

  // RENTALCOSTS
  allOperationalRentalCosts: (state) => state.operational.operationalRentalCosts,
  allOperationalRentalCostsTypes: (state) =>
    state.operational.operationalRentalCostsTypes,
  allOperationalRentalCostsTotals: (state) =>
    state.operational.operationalRentalCostsTotals,

  // LEASINGCOSTS
  allOperationalLeasingCosts: (state) => state.operational.operationalLeasingCosts,
  allOperationalLeasingCostsTypes: (state) =>
    state.operational.operationalLeasingCostsTypes,
  allOperationalLeasingCostsTotals: (state) =>
    state.operational.operationalLeasingCostsTotals,
}

export { actions, reducer, selectors }
