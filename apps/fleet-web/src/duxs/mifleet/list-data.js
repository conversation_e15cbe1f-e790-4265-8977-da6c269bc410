import { createReducer } from '@reduxjs/toolkit'
import { isNil } from 'lodash'

import { prefixedCreateAction } from 'duxs/utils'

// Action creator
const createActionCreator = prefixedCreateAction('list-data')

// Actions
export const fetchListDataItems = createActionCreator('fetch')
export const onFetchListDataItems = createActionCreator('onFetch')
export const onFetchListDataItemsFailure = createActionCreator('onFetchFailure')
export const createListDataItem = createActionCreator('create')
export const onCreateListDataItem = createActionCreator('onCreate')
export const updateListDataItem = createActionCreator('update')
export const onUpdateListDataItem = createActionCreator('onUpdate')
export const deleteListDataItem = createActionCreator('delete')
export const onDeleteListDataItem = createActionCreator('onDelete')
export const onDeleteListDataFailure = createActionCreator('onDeleteFailure')
export const cancelCreateListDataItem = createActionCreator('cancelCreate')
export const toggleEditListDataItem = createActionCreator('toggleEdit')
export const toggleEditInputItem = createActionCreator('toggleInputEdit')

// Helpers
const itemsBeingCreatedMaxIndex = 0

const setLoadingState = (draft) => {
  draft.loading = true
}

const updateStateWithNewItem = (draft, itemSchema) => {
  const newItemProperties = {}
  const schemaType = itemSchema.type

  const itemsMinIndex = draft.items.reduce((prev, current) => {
    const prevId = parseInt(prev[itemSchema.id], 10)
    const currId = parseInt(current[itemSchema.id], 10)
    return prevId < currId ? prevId : currId
  }, itemsBeingCreatedMaxIndex)

  if (schemaType === 'input') {
    // Type input
    newItemProperties.value = ''
    newItemProperties[itemSchema.value] = ''
    newItemProperties.is_deleted = 'f'
  } else {
    // Type table
    const cols = itemSchema.columns
    for (const key of cols) {
      if (key.field === 'import_model_id') {
        newItemProperties.import_model_id = '1'
      } else if (key.field === 'is_financing_as_service') {
        newItemProperties.is_financing_as_service = 'f'
      }
    }
  }

  const newItemOptions = {
    [itemSchema.id]:
      itemsMinIndex > itemsBeingCreatedMaxIndex
        ? itemsBeingCreatedMaxIndex
        : itemsMinIndex - 1,
    flag: true,
  }
  return { ...newItemOptions, ...newItemProperties }
}

const updateStateWithoutItem = (draft, payload) => {
  const itemToBeDeletedIndex = draft.items.findIndex(
    (item) =>
      item[payload.setting.schema.id] ===
      payload.itemToBeDeleted[payload.setting.schema.id],
  )

  draft.items.splice(itemToBeDeletedIndex, 1)
  draft.loading = false
}

// Initial State
const initialState = {
  isEditing: false,
  isCreating: false,
  items: [],
  loading: true,
  rowId: '',
  setting: {},
}

// Reducer
export default createReducer(initialState, {
  [fetchListDataItems]: setLoadingState,
  [createListDataItem]: setLoadingState,
  [updateListDataItem]: setLoadingState,
  [deleteListDataItem]: setLoadingState,
  [onFetchListDataItems]: (draft, { payload: { items, setting } }) => {
    draft.items = items
    draft.setting = setting
    draft.loading = false
    draft.isEditing = false
    draft.isCreating = false
    draft.rowId = ''
  },
  [onFetchListDataItemsFailure]: (draft) => {
    draft.items = []
    draft.loading = false
    draft.isEditing = false
    draft.isCreating = false
  },
  [onCreateListDataItem]: (
    draft,
    {
      payload: {
        setting: { schema },
      },
    },
  ) => {
    if (!draft.isCreating) {
      const newItem = updateStateWithNewItem(draft, schema)
      if (schema.type === 'input') {
        draft.items.push(newItem)
      } else {
        draft.items.unshift(newItem)
      }

      draft.isEditing = true
      draft.isCreating = true
      draft.rowId = '0'
    }

    draft.loading = false
  },
  [onUpdateListDataItem]: (draft, { payload }) => {
    const { newItem, success, prevId, setting, isCreating, failedItem } = payload
    if (success) {
      const itemIndex = draft.items.findIndex(
        (item) => item[setting.schema.id] === prevId,
      )

      draft.items[itemIndex] = newItem
      draft.items[itemIndex].flag = false
      draft.rowId = ''
    }

    if (!success && isCreating) {
      const itemToBeDeletedIndex = draft.items.findIndex(
        (item) => item[setting.schema.id] === failedItem[setting.schema.id],
      )

      draft.items.splice(itemToBeDeletedIndex, 1)
    }

    draft.loading = false
    draft.isEditing = false
    draft.isCreating = false
  },
  [onDeleteListDataItem]: (draft, { payload }) =>
    updateStateWithoutItem(draft, payload),
  [cancelCreateListDataItem]: (draft, { payload }) => {
    if (draft.isCreating) {
      updateStateWithoutItem(draft, payload)
    }

    draft.isCreating = false
    draft.isEditing = false
    draft.rowId = ''
  },
  [toggleEditListDataItem]: (draft, { payload: { isEditing, rowId } }) => {
    const index = draft.items?.findIndex((c) => c.flag)
    if (index != null && index !== -1) {
      draft.items.splice(index, 1)
    }
    draft.isEditing = isNil(isEditing) ? !draft.isEditing : isEditing
    draft.rowId = draft.isEditing ? rowId : ''
    draft.isCreating = false
  },

  [toggleEditInputItem]: (draft, { payload: { rowId } }) => {
    const index = draft.items?.findIndex((c) => c.flag)
    if (index != null && index !== -1) {
      draft.items.splice(index, 1)
    }
    draft.rowId = rowId
    draft.isEditing = false
    draft.isCreating = false
  },

  [onDeleteListDataFailure]: (draft) => {
    draft.loading = false
  },
})

// Selectors
export const getSetting = (state) => state.listData.setting
export const getLoading = (state) => state.listData.loading
export const getIsEditing = (state) => state.listData.isEditing
export const getItems = (state) => state.listData.items
export const getRowId = (state) => state.listData.rowId
export const getRes = (state) => state.listData.res
