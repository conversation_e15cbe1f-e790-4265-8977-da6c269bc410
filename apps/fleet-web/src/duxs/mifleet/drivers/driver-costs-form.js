// Actions
export const UPDATE_DRV_COST_LIST = 'UPDATE_DRV_COST_LIST'
export const ON_UPDATE_DRV_COST_LIST = 'ON_UPDATE_DRV_COST_LIST'
export const EMPTY_RESPONSE = 'EMPTY_RESPONSE'
export const ON_EMPTY_RESPONSE = 'ON_EMPTY_RESPONSE'
export const CREATE_DRV_COST = 'CREATE_DRV_COST'
export const ON_CREATE_DRV_COST = 'ON_CREATE_DRV_COST'
export const SET_DRIVER_COSTS_FORM_LOADING = 'SET_DRIVER_COSTS_FORM_LOADING'

// Default state
const initialState = {
  drvUpdate: [],
  response: '',
  responseMessage: '',
  responseError: '',
  loading: false,
}

// Reducers
export default function reducer(state = initialState, action) {
  const { payload } = action
  switch (action.type) {
    case UPDATE_DRV_COST_LIST:
      return {
        ...state,
        loading: true,
      }
    case ON_UPDATE_DRV_COST_LIST:
      return {
        ...state,
        drvUpdate: payload.objectList,
        response: payload.success,
        responseMessage: 'Updated Costs!',
        responseError:
          'Error Updating Cost. Check if all required fields are filled or try again later',
      }

    case EMPTY_RESPONSE:
      return {
        ...state,
      }

    case ON_EMPTY_RESPONSE:
      return {
        ...state,
        response: '',
        responseMessage: '',
      }

    case CREATE_DRV_COST:
      return {
        ...state,
        loading: true,
      }

    case ON_CREATE_DRV_COST:
      return {
        ...state,
        response: payload.success,
        responseMessage: 'Cost Added!',
        responseError:
          'Error Inserting new costs. Check if all required fields are filled or try again later',
      }

    case SET_DRIVER_COSTS_FORM_LOADING: {
      return {
        ...state,
        loading: action.payload,
      }
    }

    default:
      return state
  }
}

// Action creators
export function fetchDriverUpdate(json) {
  return {
    type: UPDATE_DRV_COST_LIST,
    json,
  }
}

export function resetResponse() {
  return {
    type: EMPTY_RESPONSE,
  }
}

export function createDriverCost(json) {
  return {
    type: CREATE_DRV_COST,
    json,
  }
}

// Selectors
export const getdrvUpdate = (state) => state.drvUpdateView.drvUpdate
export const getResponse = (state) => state.drvUpdateView.response
export const getLoading = (state) => state.drvUpdateView.loading
export const getResponseMessage = (state) => state.drvUpdateView.responseMessage
export const getResponseError = (state) => state.drvUpdateView.responseError
