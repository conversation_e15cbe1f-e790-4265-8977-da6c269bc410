import { createReducer, createSelector, type PayloadAction } from '@reduxjs/toolkit'
import { differenceBy } from 'lodash'

import { prefixedCreateAction, combineActions } from 'duxs/utils'
import type { FixMeAny } from 'src/types'
import type { FetchDriverAssignVehicles } from 'api/mifleet/assign-vehicles'

// Action creator
const createActionCreator = prefixedCreateAction('driver-assign-vehicles')

// // Actions
export const fetchDriverAssignVehicles = createActionCreator<{
  driverId: string
}>('fetch')
export const onFetchDriverAssignVehicles = createActionCreator<{
  driverId: string
}>('onFetch')
export const fetchDriverAssignVehiclesHistory = createActionCreator<{
  driverId: string
}>('fetchHistory')
export const onFetchDriverAssignVehiclesHistory = createActionCreator('onFetchHistory')
export const createDriverAssignVehicles = createActionCreator<{
  assignedVehicleData: Record<string, FixMeAny>
}>('create')
export const onCreateDriverAssignVehicles = createActionCreator('onCreate')
export const deleteDriverAssignVehicles = createActionCreator<{
  driverId: string
  assignedId: string
}>('delete')
export const onDeleteDriverAssignVehicles = createActionCreator('onDelete')
export const updateDriverAssignVehicles = createActionCreator<{
  assignedVehicleData: Record<string, FixMeAny>
}>('update')
export const onUpdateDriverAssignVehicles = createActionCreator('onUpdate')
export const onDriverAssignVehiclesFailure = createActionCreator('onFailure')
export const setDriverAssignVehiclesLoading = createActionCreator<boolean>('setLoading')

interface State {
  driverAssignVehicles: FetchDriverAssignVehicles.Return
  driverAssignVehiclesHistory: FetchDriverAssignVehicles.Return
  ui: {
    isLoading: boolean
  }
}
// Initial State
const initialState: State = {
  driverAssignVehicles: [],
  driverAssignVehiclesHistory: [],
  ui: {
    isLoading: true,
  },
}

// Helpers
const setLoadingState = (draft: State, isLoading = true) => {
  draft.ui.isLoading = isLoading
}

const loadingActions = {
  ...combineActions(
    [
      fetchDriverAssignVehicles,
      fetchDriverAssignVehiclesHistory,
      createDriverAssignVehicles,
      deleteDriverAssignVehicles,
      updateDriverAssignVehicles,
    ],
    (draft) => setLoadingState(draft),
  ),
  ...combineActions(
    [
      onCreateDriverAssignVehicles,
      onUpdateDriverAssignVehicles,
      onDeleteDriverAssignVehicles,
      onDriverAssignVehiclesFailure,
    ],
    (draft) => setLoadingState(draft, false),
  ),
}

export default createReducer(initialState, {
  ...loadingActions,
  [onFetchDriverAssignVehicles.toString()]: (
    draft: State,
    {
      payload: { driverAssignVehicles },
    }: PayloadAction<{
      driverAssignVehicles: FetchDriverAssignVehicles.Return
    }>,
  ) => {
    draft.driverAssignVehicles = driverAssignVehicles
  },
  [onFetchDriverAssignVehiclesHistory.toString()]: (
    draft: State,
    {
      payload: { driverAssignVehicles },
    }: PayloadAction<{
      driverAssignVehicles: FetchDriverAssignVehicles.Return
    }>,
  ) => {
    draft.driverAssignVehiclesHistory = driverAssignVehicles
  },
  [setDriverAssignVehiclesLoading.toString()]: (
    draft: State,
    { payload: isLoading }: PayloadAction<boolean>,
  ) => {
    setLoadingState(draft, isLoading)
  },
})

// Selectors
export const getDriverAssignVehicles = (state: {
  driverAssignVehicles: { driverAssignVehicles: Array<FixMeAny> }
}) => state.driverAssignVehicles.driverAssignVehicles || []
// Available vehicles to be assigned
export const getDriverAssignAvailableVehicles = createSelector(
  getDriverAssignVehicles,
  (assignVehicles) => assignVehicles.filter((vehicle) => vehicle.assigned === 'f'),
)
// Assigned vehicles
export const getDriverAssignedVehicles = createSelector(
  getDriverAssignVehicles,
  (assignVehicles) => assignVehicles.filter((vehicle: any) => vehicle.assigned === 't'),
)
// Get assigned vehicles history excluding current assigned vehicles
export const getDriverAssignVehiclesHistory = createSelector(
  (state: {
    driverAssignVehicles: {
      driverAssignVehiclesHistory: FetchDriverAssignVehicles.Return
    }
  }) => state.driverAssignVehicles.driverAssignVehiclesHistory || [],
  getDriverAssignedVehicles,
  (assignVehiclesHistory, assignVehicles) =>
    differenceBy(assignVehiclesHistory, assignVehicles, 'assignedId'),
)

export const isDriverAssignVehiclesLoading = (state: {
  driverAssignVehicles: { ui: { isLoading: boolean } }
}) => state.driverAssignVehicles.ui.isLoading || false
