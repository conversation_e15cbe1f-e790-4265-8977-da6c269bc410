import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import { ctIntl } from 'cartrack-ui-kit'
import type { SuppliersDataType } from 'src/modules/mifleet/data/suppliers'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny } from 'src/types'

export type Supplier = {
  supplier_id: string
  supplier_type_id: string
  supplier: string
  address_line1: string | null
  address_line2: string | null
  address_line3: string | null
  postal_code: string | null
  contact_person: string | null
  email: string
  telephone: string
  telefax: string | null
  company_id: string
  is_deleted: string
  vat_number: string
}

export type Category = {
  assigned: boolean
  supplier_category_id: string
  supplier_id: string
  supplier_supplier_category_id: string
  tag: string
}

export type SupplierWithCategories = Supplier & { categories: Array<Category> }

export type SupplierType = {
  supplier_type_id: string
  supplier_type: string
  company_id: string
  is_deleted: string
}

type State = {
  loading: boolean
  supplierLoading: boolean
  editing: boolean
  suppliers: Array<Supplier>
  supplier: SupplierWithCategories | Record<string, never>
  supplierTypes: Array<SupplierType>
  rowId: string
  response: Record<string, unknown>
}

const getInitialState = (): State => ({
  loading: false,
  supplierLoading: false,
  editing: false,
  suppliers: [],
  supplier: {},
  supplierTypes: [],
  rowId: '',
  response: {},
})

const formatNotificationMessage = (message: string) =>
  message
    ? ctIntl.formatMessage({ id: message })
    : ctIntl.formatMessage({
        id: 'It seems there was an error, please try again.',
      })

// Reducers
const { reducer, actions } = createSlice({
  name: 'suppliers',
  initialState: getInitialState(),
  reducers: {
    fetchSuppliers: (draft, _: PayloadAction<{ data: { setting: FixMeAny } }>) => {
      draft.loading = true
    },
    onFetchSuppliers: (draft, { payload: { suppliers, supplierTypes } }) => {
      draft.suppliers = suppliers
      draft.supplierTypes = supplierTypes
      draft.loading = false
    },
    onFetchSuppliersFailure: (draft) => {
      draft.loading = false
    },
    fetchSupplier: (
      draft,
      _: PayloadAction<{
        data: { setting: SuppliersDataType; supplier: FixMeAny }
      }>,
    ) => {
      draft.supplierLoading = true
    },
    onFetchSupplier: (draft, { payload: { supplier } }) => {
      draft.supplier = supplier
      draft.supplierLoading = false
    },
    onFetchSupplierFailure: (draft) => {
      draft.loading = false
      draft.supplierLoading = false
    },
    updateSupplier: (
      draft,
      _: PayloadAction<{
        data: { setting: SuppliersDataType; supplier: SupplierWithCategories }
      }>,
    ) => {
      draft.loading = false
      draft.supplierLoading = true
    },
    deleteSupplier: (
      draft,
      _: PayloadAction<{
        data: { setting: SuppliersDataType; supplier: Supplier }
      }>,
    ) => {
      draft.loading = true
    },
    onDeleteSupplier: (draft, { payload: { setting, deletedSupplier, response } }) => {
      const supplierIndex = draft.suppliers.findIndex(
        (supplier) =>
          supplier[setting.schema.id as keyof typeof supplier] ===
          deletedSupplier[setting.schema.id],
      )
      draft.suppliers.splice(supplierIndex, 1)
      draft.loading = false
      draft.response = {
        ...draft.response,
        ...response,
        message: formatNotificationMessage('List Type Deleted'),
      }
    },
    onDeleteSupplierFailure: (draft) => {
      draft.loading = false
    },
  },
})

// Selectors
const selectors = {
  getLoading: (state: AppState) => state.suppliers.loading,
  getEditing: (state: AppState) => state.suppliers.editing,
  getSuppliers: (state: AppState) => state.suppliers.suppliers,
  getSupplier: (state: AppState) => state.suppliers.supplier,
  getSupplierLoading: (state: AppState) => state.suppliers.supplierLoading,
  getSupplierTypes: (state: AppState) => state.suppliers.supplierTypes,
  getRowId: (state: AppState) => state.suppliers.rowId,
  getResponse: (state: AppState) => state.suppliers.response,
}

export { reducer, actions, selectors }
