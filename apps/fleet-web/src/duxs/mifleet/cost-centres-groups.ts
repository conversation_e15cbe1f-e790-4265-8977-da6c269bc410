import { isNil } from 'lodash'
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'

export type CostCentreGroup = {
  cost_centre_family_id: string
  cost_centre_family: string
  parent_id?: CostCentreGroup['cost_centre_family_id']
  objectList: Array<CostCentreGroup>
  isNew?: boolean
}

export type CostCentreGroupId = CostCentreGroup['cost_centre_family_id']

type State = {
  groups: Array<CostCentreGroup>
  loading: boolean
  openGroup?: CostCentreGroup
  status: { ok: boolean; message: string }
}

// Default state
const initialState: State = {
  groups: [],
  loading: false,
  openGroup: undefined,
  status: { ok: true, message: '' },
}

/*
  USAGE: key attribute for tree item components that have been added by the user but have not been created/persisted yet
  It is managed with negative values, as user adds more items, to avoid collision with real ids(cost_centre_family_id).
 */
const groupsBeingCreatedMaxIndex = 0

const formatNotificationMessage = (res: FixMeAny, st: FixMeAny) =>
  res.success ? st : 'It seems there was an error, please try again.'

const isRootGroup = (group: CostCentreGroup) => isNil(group.parent_id)

const removeGroup = (groups: Array<CostCentreGroup>, groupId: string) =>
  groups.reduce((array, group) => {
    if (group.cost_centre_family_id !== groupId) {
      array.push({
        ...group,
        objectList: removeGroup(group.objectList, groupId),
      })
    }

    return array
  }, [] as Array<CostCentreGroup>)

/*
  If groupToAddParentId is Nil, groupToAdd is added to root of tree
  Note: Updates the parent_id of groupToAdd for you
 */
const addGroup = (
  groups: Array<CostCentreGroup>,
  groupToAdd: CostCentreGroup,
  groupToAddParentId?: CostCentreGroupId,
): Array<CostCentreGroup> => {
  const groupWithNewParent = { ...groupToAdd, parent_id: groupToAddParentId }
  if (isRootGroup(groupWithNewParent)) {
    return [{ ...groupWithNewParent }, ...groups]
  }

  return groups.map((g) => {
    if (g.cost_centre_family_id === groupToAddParentId) {
      return {
        ...g,
        objectList: [{ ...groupWithNewParent }, ...g.objectList],
      }
    }

    if (g.objectList.length > 0) {
      return {
        ...g,
        objectList: addGroup(g.objectList, groupToAdd, groupToAddParentId),
      }
    }

    return g
  })
}

const getGroupsMinId = (
  groups: Array<CostCentreGroup>,
  min = groupsBeingCreatedMaxIndex,
): number =>
  groups.reduce((currMin: number, group: CostCentreGroup) => {
    const currId = parseInt(group.cost_centre_family_id, 10)
    const minValue = !isNaN(currId) && currId < currMin ? currId : currMin
    return getGroupsMinId(group.objectList, minValue)
  }, min)

const { reducer, actions } = createSlice({
  name: 'costCentresGroups',
  initialState,
  reducers: {
    fetchCostCenterGroups: (draft) => {
      draft.loading = true
    },
    onFetchCostCenterGroups: (
      draft,
      { payload: { groups } }: PayloadAction<{ groups: Array<CostCentreGroup> }>,
    ) => {
      draft.groups = groups
      draft.openGroup = groups[0]
      draft.loading = false
    },
    createCostCenterGroup: (
      draft,
      { payload: { group } }: PayloadAction<{ group?: CostCentreGroup }>,
    ) => {
      const groupToCreateParentId = group?.cost_centre_family_id

      const minIndex = getGroupsMinId(draft.groups)

      const groupToCreate = {
        ...group,
        cost_centre_family_id: (minIndex > groupsBeingCreatedMaxIndex
          ? groupsBeingCreatedMaxIndex
          : minIndex - 1
        ).toString(),
        cost_centre_family: '',
        isNew: true,
        objectList: [],
      }

      draft.openGroup = group
      draft.groups = addGroup(draft.groups, groupToCreate, groupToCreateParentId)
    },
    fetchCreateCostCenterGroup: (draft) => {
      draft.loading = true
    },
    onFetchCreateCostCenterGroup: (
      draft,
      {
        payload: { newGroup, res, prevId },
      }: PayloadAction<{
        newGroup: CostCentreGroup
        res: FixMeAny
        prevId: CostCentreGroupId
      }>,
    ) => {
      if (res.success) {
        const updateGroup = (
          groups: Array<CostCentreGroup>,
          id: CostCentreGroupId,
        ): Array<CostCentreGroup> =>
          groups.map((g) => {
            if (g.cost_centre_family_id === id) {
              return {
                ...newGroup,
                isNew: false,
              }
            }

            if (g.objectList.length > 0) {
              return { ...g, objectList: updateGroup(g.objectList, id) }
            }

            return g
          })

        draft.groups = updateGroup(draft.groups, prevId)
      }

      draft.loading = false
      draft.status = {
        ok: res.success,
        message: formatNotificationMessage(res, 'Folder Created'),
      }
    },
    updateCostCenterGroup: (draft, _) => {
      draft.loading = false
    },
    onUpdateCostCenterGroup: (
      draft,
      {
        payload: { newGroup, res, prevId },
      }: PayloadAction<{
        newGroup: CostCentreGroup
        res: FixMeAny
        prevId: CostCentreGroupId
      }>,
    ) => {
      const updateGroup = (
        groups: Array<CostCentreGroup>,
        id: CostCentreGroupId,
      ): Array<CostCentreGroup> =>
        groups.map((g) => {
          if (g.cost_centre_family_id === id) {
            draft.openGroup = newGroup
            return {
              ...newGroup,
              isNew: false,
            }
          }

          if (g.objectList.length > 0) {
            return { ...g, objectList: updateGroup(g.objectList, id) }
          }

          return g
        })

      draft.groups = res.success ? updateGroup(draft.groups, prevId) : draft.groups
      draft.loading = false
      draft.status = {
        ok: res.success,
        message: formatNotificationMessage(res, 'Group Updated'),
      }
    },
    deleteCostCenterGroup: (draft) => {
      draft.loading = true
    },
    onDeleteCostCenterGroup: (
      draft,
      {
        payload: { group, res },
      }: PayloadAction<{ group: CostCentreGroup; res: FixMeAny }>,
    ) => {
      const lastId = group.cost_centre_family_id
      const parentId = group.parent_id

      const removeGroupFn = (groups: Array<CostCentreGroup>): Array<CostCentreGroup> =>
        groups.reduce((groups, currentGroup) => {
          if (currentGroup.cost_centre_family_id !== lastId) {
            if (currentGroup.cost_centre_family_id === parentId) {
              draft.openGroup = currentGroup
            }

            groups.push({
              ...currentGroup,
              objectList: removeGroupFn(currentGroup.objectList),
            })
          }

          return groups
        }, [] as Array<CostCentreGroup>)

      draft.groups = res.success ? removeGroupFn(draft.groups) : draft.groups
      if (draft.groups.length === 0) {
        draft.openGroup = undefined
      } else {
        draft.openGroup = draft.groups[0]
      }
      draft.loading = false
    },
    sortCostCenterGroup: (draft) => {
      draft.loading = true
    },
    onSortCostCenterGroup: (
      draft,
      {
        payload: { draggedGroup, targetGroup, res },
      }: PayloadAction<{
        draggedGroup: CostCentreGroup
        targetGroup: CostCentreGroup
        res: FixMeAny
      }>,
    ) => {
      const draggedId = draggedGroup.cost_centre_family_id
      const targetId = targetGroup.cost_centre_family_id

      const sortGroups = (groups: Array<CostCentreGroup>) => {
        const groupsTree = removeGroup(groups, draggedId)
        return isNil(targetId)
          ? [...groupsTree, draggedGroup]
          : addGroup(groupsTree, draggedGroup, targetId)
      }

      draft.groups = res.success ? sortGroups(draft.groups) : draft.groups
      draft.loading = false
      draft.status = {
        ok: res.success,
        message: formatNotificationMessage(res, 'Group Moved'),
      }
    },
    updateCostCenterGroupStatus: (
      draft,
      { payload: { message, ok } }: PayloadAction<{ message: FixMeAny; ok: FixMeAny }>,
    ) => {
      draft.status = { ok, message }
    },
    onCancelCreateGroup: (draft, { payload: item }: PayloadAction<CostCentreGroup>) => {
      draft.groups = removeGroup(draft.groups, item.cost_centre_family_id)
    },
  },
})

// Action creators
export function updateGroupIfValid(data: { group: CostCentreGroup }) {
  const value = data.group.cost_centre_family
  if (isNil(value) || value === '') {
    return actions.updateCostCenterGroupStatus({
      ok: false,
      message: 'Field can not be empty',
    })
  }

  return actions.updateCostCenterGroup(data)
}

export { reducer, actions }

const getReducerState = (state: AppState): State =>
  (state as FixMeAny).costCentresGroups

// Selectors
export const getGroups = (state: AppState) => getReducerState(state).groups
export const getGroupLoading = (state: AppState) => getReducerState(state).loading
export const getGroupStatus = (state: AppState) => getReducerState(state).status
export const getOpenGroup = (state: AppState) => getReducerState(state).openGroup
