import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { FetchTaskTypes } from 'api/tasks/task-types'
import type { AppState } from 'src/root-reducer'

type State = {
  taskTypes: FetchTaskTypes['taskTypes']
  loading: boolean
  response: {
    status: string
    type: string
  } | null
}

// Reducer
const initialState: State = {
  taskTypes: [],
  loading: true,
  response: null,
}

const slice = createSlice({
  name: 'task-types',
  initialState,
  reducers: {
    fetchTaskTypes(draft) {
      draft.loading = true
    },
    createTaskType(draft, _: PayloadAction<{ description: string }>) {
      draft.loading = true
    },
    updateTaskType(draft, _: PayloadAction<{ id: string; description: string }>) {
      draft.loading = true
    },
    deleteTaskType(draft, _: PayloadAction) {
      draft.loading = true
    },
    receiveTaskTypes(
      draft,
      { payload }: PayloadAction<{ taskTypes: State['taskTypes'] }>,
    ) {
      draft.taskTypes = payload.taskTypes
      draft.loading = false
    },
  },
})

export default slice.reducer

export const {
  createTaskType,
  deleteTaskType,
  fetchTaskTypes,
  receiveTaskTypes,
  updateTaskType,
} = slice.actions

// Selectors
export const getTaskTypes = (state: AppState) => state.taskTypes.taskTypes
export const hasLoadedTaskTypes = (state: AppState) => !state.taskTypes.loading
