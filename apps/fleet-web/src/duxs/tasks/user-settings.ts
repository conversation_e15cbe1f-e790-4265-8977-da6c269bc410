import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { FetchUserSettings } from 'api/tasks/user-settings'
import type { AppState } from 'src/root-reducer'
import type { UpdateUserTheme } from 'api/tasks/user-settings/types'

type State = {
  userSettings: FetchUserSettings['userSettings']
  loading: boolean
}

// Reducer
const initialState: State = {
  userSettings: {},
  loading: true,
}

const slice = createSlice({
  name: 'user-settings',
  initialState,
  reducers: {
    fetchUserSettings(draft) {
      draft.loading = true
    },
    updateTheme(draft, _: PayloadAction<UpdateUserTheme.ApiInput>) {
      draft.loading = true
    },
    receiveUserSettings(
      draft,
      { payload }: PayloadAction<{ userSettings: State['userSettings'] }>,
    ) {
      draft.userSettings = payload.userSettings
      draft.loading = false
    },
  },
})

export default slice.reducer

export const { fetchUserSettings, updateTheme, receiveUserSettings } = slice.actions

// Selectors
export const getUserSettings = (state: AppState) => state.userSettings.userSettings
export const hasLoadedUserSettings = (state: AppState) => !state.userSettings.loading
