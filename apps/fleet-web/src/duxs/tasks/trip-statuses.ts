import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { FetchTripStatusesTypes } from 'api/tasks/trip-statuses-types'
import type { AppState } from 'src/root-reducer'

type State = {
  tripStatusesTypes: FetchTripStatusesTypes['tripStatusesTypes']
  loading: boolean
  response: {
    status: string
    type: string
  } | null
}

// Reducer
const initialState: State = {
  tripStatusesTypes: [],
  loading: true,
  response: null,
}

const slice = createSlice({
  name: 'trip-statuses-types',
  initialState,
  reducers: {
    fetchTripStatusesTypes(draft) {
      draft.loading = true
    },
    receiveTripStatusesTypes(
      draft,
      { payload }: PayloadAction<{ tripStatusesTypes: State['tripStatusesTypes'] }>,
    ) {
      draft.tripStatusesTypes = payload.tripStatusesTypes
      draft.loading = false
    },
    createTripStatusesType(draft, _: PayloadAction<{ description: string }>) {
      draft.loading = true
    },
    updateTripStatusesType(
      draft,
      _: PayloadAction<{ id: string; description: string }>,
    ) {
      draft.loading = true
    },
    deleteTripStatusesType(draft, _: PayloadAction) {
      draft.loading = true
    },
  },
})

export default slice.reducer

export const {
  fetchTripStatusesTypes,
  receiveTripStatusesTypes,
  createTripStatusesType,
  updateTripStatusesType,
  deleteTripStatusesType,
} = slice.actions

// Selectors
export const getTripStatusesTypes = (state: AppState) =>
  state.tripStatusesTypes.tripStatusesTypes
export const hasLoadedTripStatusesTypes = (state: AppState) =>
  !state.tripStatusesTypes.loading
