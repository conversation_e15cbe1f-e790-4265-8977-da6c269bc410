import { createSelector } from '@reduxjs/toolkit'
import moment from 'moment-timezone'
import { isEmpty } from 'lodash'

import { TaskTypeId, TaskAssignedViewType } from 'src/modules/tasks/utils/constants'
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'
import { loginSucceeded, tokenLoginSucceeded } from 'duxs/user'

// Actions
export const ON_TABLE_VIEW_CHANGE = 'ON_TABLE_VIEW_CHANGE'
export const SET_TIMELINE_VIEW = 'SET_TIMELINE_VIEW'
export const SET_FROM_HOUR = 'SET_FROM_HOUR'
export const SET_TO_HOUR = 'SET_TO_HOUR'

// Create
export const CREATE_TASK = 'CREATE_TASK'
export const DOWNLOAD_TASK_SHEET = 'DOWNLOAD_TASK_SHEET'

// Read/Fetch
export const RECEIVE_TASKS = 'RECEIVE_TASKS'
export const RECEIVE_TASKS_TIMELINE = 'RECEIVE_TASKS_TIMELINE'
export const RECEIVE_TASK_DETAILS = 'RECEIVE_TASK_DETAILS'
export const RECEIVE_TASK_REPEATS = 'RECEIVE_TASK_REPEATS'
export const RECEIVE_DELETE_RESPONSE = 'RECEIVE_DELETE_RESPONSE'
export const RECEIVE_TASKS_TIMELINE_WORKING_HOURS =
  'RECEIVE_TASKS_TIMELINE_WORKING_HOURS'

export const FETCH_TASKS = 'FETCH_TASKS'
export const FETCH_TASKS_TIMELINE = 'FETCH_TASKS_TIMELINE'
export const FETCH_TASK_DETAILS = 'FETCH_TASK_DETAILS'
export const FETCH_TASK_REPEATS = 'FETCH_TASK_REPEATS'
export const FETCH_DOWNLOAD_TASK_AVAILABILITY = 'FETCH_DOWNLOAD_TASK_AVAILABILITY'
export const FETCH_TASKS_TIMELINE_WORKING_HOURS = 'FETCH_TASKS_TIMELINE_WORKING_HOURS'
// Stop Worker Actions
export const STOP_TASK_POLL = 'STOP_TASK_POLL'
export const STOP_TASK_TIMELINE_POLL = 'STOP_TASK_TIMELINE_POLL'

// Update
export const UPDATE_TASK = 'UPDATE_TASK'
export const UPDATE_TASK_USER = 'UPDATE_TASK_USER'
export const UPDATE_TASK_TIMELINE_WORKING_HOURS = 'UPDATE_TASK_TIMELINE_WORKING_HOURS'
export const ADD_SELECTED_TASK = 'ADD_SELECTED_TASK'
export const REMOVE_SELECTED_TASK = 'REMOVE_SELECTED_TASK'
export const SELECT_ALL_TASK = 'SELECT_ALL_TASK'
export const DESELECT_ALL_TASK = 'DESELECT_ALL_TASK'

// DELETE
export const DELETE_TASK = 'DELETE_TASK'
export const DELETE_TASK_GROUP = 'DELETE_TASK_GROUP'
export const DELETE_TASK_STEP = 'DELETE_TASK_STEP'
export const CLEAR_CURRENT_TASK = 'CLEAR_CURRENT_TASK'
export const CLEAR_TASK_LIST = 'CLEAR_TASK_LIST'

export const REASSIGN_TASKS = 'REASSIGN_TASKS'
export const REASSIGNING_TASK = 'REASSIGNING_TASK'
export const RESET_REASSIGNING_TASK = 'RESET_REASSIGNING_TASK'
export const TOGGLE_REASSIGNING_TASK = 'TOGGLE_REASSIGNING_TASK'

export const DATE_FORMAT = 'YYYY-MM-DD'

type State = {
  allTaskID: Array<FixMeAny>
  loading: {
    downloadReference: boolean
    taskDetails: boolean
    tasks: boolean
    tasksTimeline: boolean
    misc: boolean
    reassignTask: boolean
  }

  params: {
    filters: {
      startDate: FixMeAny
      endDate: FixMeAny
      status: Array<FixMeAny>
      assigned: Array<FixMeAny>
    }
    query: string
    page: number
    pageSize: number
    sorted: Array<FixMeAny>
    type: string
    viewType: string
  }
  modal: {
    isReassignOpen: boolean
  }

  response: FixMeAny
  selectedTasks: Array<FixMeAny>
  task: Record<string, any>
  tasks: Array<FixMeAny>
  totalRejectedTasks: number
  taskRepeats: Array<FixMeAny>
  taskSteps: Array<FixMeAny>
  timelineUsers: Array<FixMeAny>
  timelineViewState: {
    view: string
    from: number
    to: number
  }
  totalPages: number
  totalTasksCount: number
  taskDataSize: number
  timelineWorkingHours: {
    tasksTimelineStartTime: string
    tasksTimelineClosingTime: string
  }
}

// Reducer
const initialState: State = {
  allTaskID: [],
  loading: {
    downloadReference: false,
    misc: false,
    reassignTask: false,
    taskDetails: false,
    tasks: false,
    tasksTimeline: false,
  },
  modal: {
    isReassignOpen: false,
  },
  params: {
    filters: {
      startDate: moment().format(DATE_FORMAT),
      endDate: moment().add(6, 'days').format(DATE_FORMAT),
      status: [],
      assigned: [],
    },
    query: '',
    page: 0,
    pageSize: 25,
    sorted: [{ id: 'scheduledStartTime', desc: false }],
    type: TaskTypeId.ASSIGNED,
    viewType: TaskAssignedViewType.Table,
  },
  response: null,
  selectedTasks: [],
  task: {},
  tasks: [],
  totalRejectedTasks: 0,
  taskRepeats: [],
  taskSteps: [],
  timelineUsers: [],
  timelineViewState: {
    view: 'work',
    from: 8,
    to: 17,
  },
  totalPages: 1,
  totalTasksCount: 0,
  taskDataSize: -1,
  timelineWorkingHours: {
    tasksTimelineStartTime: '',
    tasksTimelineClosingTime: '',
  },
}

const generateTimelineViewStateUponLogin = (
  state: State,
  settings: Record<string, FixMeAny>,
): State['timelineViewState'] => {
  const { communicatorWorkEndHour, communicatorWorkStartHour } = settings
  return {
    ...state.timelineViewState,
    from: communicatorWorkStartHour,
    to: communicatorWorkEndHour,
  }
}

export default function reducer(state = initialState, action: FixMeAny) {
  if (tokenLoginSucceeded.match(action)) {
    return {
      ...state,
      timelineViewState: generateTimelineViewStateUponLogin(
        state,
        action.payload.settings,
      ),
    }
  }
  if (loginSucceeded.match(action)) {
    const { payload } = action
    const settings = payload.apiData.settings

    return {
      ...state,
      timelineViewState: generateTimelineViewStateUponLogin(state, settings),
    }
  }

  switch (action.type) {
    // API Calls
    case FETCH_TASKS:
      return { ...state, loading: { ...state.loading, tasks: true } }
    case FETCH_TASKS_TIMELINE:
      return { ...state, loading: { ...state.loading, tasksTimeline: true } }
    case FETCH_TASKS_TIMELINE_WORKING_HOURS:
    case FETCH_TASK_DETAILS:
      return { ...state, loading: { ...state.loading, taskDetails: true } }
    case FETCH_TASK_REPEATS:
      return { ...state, loading: { ...state.loading, taskRepeats: true } }
    case CREATE_TASK:
    case UPDATE_TASK:
    case UPDATE_TASK_USER:
    case UPDATE_TASK_TIMELINE_WORKING_HOURS:
    case DELETE_TASK:
    case DELETE_TASK_GROUP:
    case DELETE_TASK_STEP:
      return { ...state, loading: { ...state.loading, misc: true } }
    case FETCH_DOWNLOAD_TASK_AVAILABILITY:
      return {
        ...state,
        loading: { ...state.loading, downloadReference: true },
      }
    case `${FETCH_DOWNLOAD_TASK_AVAILABILITY}_SUCCESS`:
      return {
        ...state,
        loading: { ...state.loading, downloadReference: false },
      }
    // API Responses
    case RECEIVE_TASK_DETAILS:
      return {
        ...state,
        task: action.payload.task,
        taskSteps: action.payload.taskSteps,
        loading: { ...state.loading, taskDetails: false, misc: false },
      }
    case RECEIVE_TASK_REPEATS:
      return {
        ...state,
        taskRepeats: action.payload.taskRepeats,
        loading: { ...state.loading, taskRepeats: false, misc: false },
      }
    case RECEIVE_TASKS:
      return {
        ...state,
        allTaskID: action.payload.allTaskID,
        taskDataSize: action.payload.overallTaskCount,
        loading: { ...state.loading, tasks: false, misc: false },
        recentlyUpdatedTaskSteps: action.payload.recentlyUpdatedTaskSteps,
        recentTaskStepUploads: action.payload.recentTaskStepUploads,
        tasks: action.payload.tasks ? action.payload.tasks : state.tasks,
        totalPages: action.payload.totalPages,
        totalRejectedTasks: action.payload.totalRejectedTasks,
        totalTasksCount: action.payload.totalTasksCount,
      }
    case RECEIVE_TASKS_TIMELINE:
      return {
        ...state,
        loading: { ...state.loading, tasksTimeline: false, misc: false },
        timelineUsers: action.payload.users,
        totalTasksCount: action.payload.totalTasksCount,
      }
    case RECEIVE_TASKS_TIMELINE_WORKING_HOURS:
      return {
        ...state,
        timelineWorkingHours: action.payload.tasksTimelineWorkingHours,
      }
    case RECEIVE_DELETE_RESPONSE:
      return {
        ...state,
        response: action.payload.response,
        loading: { ...state.loading },
      }
    // Clear
    case CLEAR_CURRENT_TASK:
      return { ...state, task: {}, taskSteps: [] }
    case ON_TABLE_VIEW_CHANGE:
      return {
        ...state,
        params: action.payload,
      }
    case SET_TIMELINE_VIEW:
      return {
        ...state,
        timelineViewState: {
          ...state.timelineViewState,
          view: action.payload,
        },
      }
    case SET_FROM_HOUR:
      return {
        ...state,
        timelineWorkingHours: {
          ...state.timelineWorkingHours,
          tasksTimelineStartTime: action.payload,
        },
      }
    case SET_TO_HOUR:
      return {
        ...state,
        timelineWorkingHours: {
          ...state.timelineWorkingHours,
          tasksTimelineClosingTime: action.payload,
        },
      }
    case CLEAR_TASK_LIST:
      return { ...state, tasks: [], selectedTasks: [] }
    case SELECT_ALL_TASK: {
      return {
        ...state,
        selectedTasks: state.allTaskID.map((id) => id),
      }
    }
    case DESELECT_ALL_TASK: {
      return {
        ...state,
        selectedTasks: [],
      }
    }
    case ADD_SELECTED_TASK: {
      return {
        ...state,
        selectedTasks: [...state.selectedTasks, action.payload.id],
      }
    }
    case REMOVE_SELECTED_TASK: {
      return {
        ...state,
        selectedTasks: state.selectedTasks.filter((id) => id !== action.payload.id),
      }
    }
    case REASSIGNING_TASK: {
      return {
        ...state,
        loading: {
          ...state.loading,
          reassignTask: true,
        },
      }
    }
    case RESET_REASSIGNING_TASK:
    case `${REASSIGNING_TASK}_SUCCESS`: {
      return {
        ...state,
        loading: {
          ...state.loading,
          reassignTask: false,
        },
        modal: {
          isReassignOpen: false,
        },
        selectedTasks: [],
      }
    }
    case TOGGLE_REASSIGNING_TASK: {
      return {
        ...state,
        loading: {
          ...state.loading,
          reassignTask: false,
        },
        modal: {
          isReassignOpen: !state.modal.isReassignOpen,
        },
      }
    }

    default:
      return state
  }
}

// Action Creators
export function onTableViewChange(params: FixMeAny) {
  return {
    type: ON_TABLE_VIEW_CHANGE,
    payload: { ...params },
  }
}

export function setTimelineView(view: FixMeAny) {
  return {
    type: SET_TIMELINE_VIEW,
    payload: view,
  }
}

export function setFromHour(hour: FixMeAny) {
  return {
    type: SET_FROM_HOUR,
    payload: hour,
  }
}

export function setToHour(hour: FixMeAny) {
  return {
    type: SET_TO_HOUR,
    payload: hour,
  }
}

// Create
export function createTask(task: FixMeAny, taskSteps: FixMeAny) {
  return {
    type: CREATE_TASK,
    payload: { task, taskSteps },
  }
}

// Read/Fetch
export function fetchTasks(options: FixMeAny) {
  return {
    type: FETCH_TASKS,
    payload: { ...options },
  }
}

export function fetchTasksTimeline(options: FixMeAny) {
  return {
    type: FETCH_TASKS_TIMELINE,
    payload: { ...options },
  }
}

export function fetchTasksTimelineWorkingHours() {
  return {
    type: FETCH_TASKS_TIMELINE_WORKING_HOURS,
  }
}

export function fetchTaskDetails(taskId: FixMeAny) {
  return {
    type: FETCH_TASK_DETAILS,
    payload: { taskId },
  }
}

export function fetchTaskRepeats() {
  return {
    type: FETCH_TASK_REPEATS,
  }
}

// Stop Worker Actions
export function stopFetchTasks() {
  return {
    type: STOP_TASK_POLL,
  }
}

export function stopFetchTasksTimeline() {
  return {
    type: STOP_TASK_TIMELINE_POLL,
  }
}

// Update
export function updateTask(task: FixMeAny, taskSteps: FixMeAny) {
  return {
    type: UPDATE_TASK,
    payload: { task, taskSteps },
  }
}

export function updateTaskUser(taskId: FixMeAny, communicatorUser: FixMeAny) {
  return {
    type: UPDATE_TASK_USER,
    payload: { taskId, communicatorUser },
  }
}

export function updateTasksTimelineWorkingHours(taskTimelineWorkingHours: FixMeAny) {
  return {
    type: UPDATE_TASK_TIMELINE_WORKING_HOURS,
    payload: { taskTimelineWorkingHours },
  }
}

export function toggleSelectAllTaskDetails(isSelected: boolean) {
  if (isSelected) {
    return {
      type: SELECT_ALL_TASK,
    }
  }

  return {
    type: DESELECT_ALL_TASK,
  }
}

export function toggleTaskSelection(id: FixMeAny, isSelected: boolean) {
  if (isSelected) {
    return {
      type: ADD_SELECTED_TASK,
      payload: { id },
    }
  }

  return {
    type: REMOVE_SELECTED_TASK,
    payload: { id },
  }
}

// Delete
export function clearCurrentTask() {
  return {
    type: CLEAR_CURRENT_TASK,
  }
}

export function deleteTask(taskId: string | number) {
  return {
    type: DELETE_TASK,
    payload: { taskId },
  }
}

export function deleteTaskGroup(params: Record<string, any>) {
  return {
    type: DELETE_TASK_GROUP,
    payload: { params },
  }
}

export function deleteTaskStep(taskStep: FixMeAny) {
  return {
    type: DELETE_TASK_STEP,
    payload: { taskStep },
  }
}

export function fetchDownloadTaskAvailability(filter: FixMeAny) {
  return {
    type: FETCH_DOWNLOAD_TASK_AVAILABILITY,
    payload: { filter },
  }
}

export function downloadTaskSheet(filter: FixMeAny) {
  return {
    type: DOWNLOAD_TASK_SHEET,
    payload: { filter },
  }
}

export function clearTaskList() {
  return {
    type: CLEAR_TASK_LIST,
  }
}

export function reassignTasks({ params }: FixMeAny) {
  return {
    type: REASSIGN_TASKS,
    payload: { params },
  }
}

export function toggleReassignModal() {
  return {
    type: TOGGLE_REASSIGNING_TASK,
  }
}

export function resetReassignTask() {
  return {
    type: RESET_REASSIGNING_TASK,
  }
}

// Selectors
export const getDeleteResponse = (state: AppState) => state.tasks.response
export const getSelectedTask = (state: AppState) => state.tasks.selectedTasks
export const getTask = (state: AppState) => state.tasks.task
export const getTasks = (state: AppState) => state.tasks.tasks
export const getTaskRepeats = (state: AppState) => state.tasks.taskRepeats
export const getTaskSteps = (state: AppState) => state.tasks.taskSteps
export const getTaskTableParams = (state: AppState) => state.tasks.params
export const getTotalPages = (state: AppState) => state.tasks.totalPages
export const getTotalRejectedTasks = (state: AppState) => state.tasks.totalRejectedTasks
export const getTotalTasksCount = (state: AppState) => state.tasks.totalTasksCount
export const getTaskTimelineViewState = (state: AppState) =>
  state.tasks.timelineViewState
export const getDownloadReferenceState = (state: AppState) =>
  state.tasks.loading.downloadReference

export const hasFinishedAPICall = (state: AppState) => !state.tasks.loading.misc
export const hasFinishedReassignTask = (state: AppState) =>
  !state.tasks.loading.reassignTask
export const hasLoadedTasks = createSelector(
  hasFinishedAPICall,
  (state: AppState) => !state.tasks.loading.tasks,
  (apiCallFinished, tasksLoaded) => apiCallFinished && tasksLoaded,
)
export const hasLoadedTaskDetails = createSelector(
  hasFinishedAPICall,
  (state: AppState) => !state.tasks.loading.taskDetails,
  (apiCallFinished, taskDetailsLoaded) => apiCallFinished && taskDetailsLoaded,
)

export const getReassignModalStatus = (state: AppState) =>
  state.tasks.modal.isReassignOpen

/**
 * @returns {{ loading: boolean users: any[] }}
 */
export const getTaskTimelineData = createSelector(
  (state: AppState) => state.tasks.loading.tasksTimeline,
  (state: AppState) => state.tasks.timelineUsers,
  getTaskTableParams,
  (loading, users, params) => {
    let parsedUsers = users
    const { filters } = params

    if (!isEmpty(filters.assigned) && filters.assigned.length < 2) {
      const [assign] = filters.assigned

      if (assign.value.toLowerCase() === 'assigned') {
        parsedUsers = users.filter((user: FixMeAny) => user.tasks.length)
      } else if (assign.value.toLowerCase() === 'unassigned') {
        parsedUsers = users.filter((user: FixMeAny) => !user.tasks.length)
      }
    }

    return {
      loading,
      users: parsedUsers,
    }
  },
)
export const getTaskDataSize = (state: AppState) => state.tasks.taskDataSize
export const getTaskTimelineWorkingHours = (state: AppState) =>
  state.tasks.timelineWorkingHours
