import type { AppState } from 'src/root-reducer'
import type { FixMeAny } from 'src/types'

//Read || Fetch
export const FETCH_COMMUNICATOR_VEHICLES = 'FETCH_COMMUNICATOR_VEHICLES'

export const RECEIVE_COMMUNICATOR_VEHICLES = 'RECEIVE_COMMUNICATOR_VEHICLES'

//State type
type State = {
  loading: boolean
  communicatorVehicles: Array<FixMeAny>
}

//Initial states
const initialState: State = {
  loading: false,
  communicatorVehicles: [],
}
export default function reducer(state = initialState, action: Record<string, any>) {
  switch (action.type) {
    //API CALLS
    case FETCH_COMMUNICATOR_VEHICLES:
      return { ...state, loading: true }

    //API RESPONSE
    case RECEIVE_COMMUNICATOR_VEHICLES:
      return {
        ...state,
        communicatorVehicles: action.payload.communicatorVehicles,
        loading: false,
      }
    default:
      return state
  }
}

//Action Creators
export function fetchCommunicatorVehicles() {
  return {
    type: FETCH_COMMUNICATOR_VEHICLES,
    loading: true,
  }
}

//Selectors
export const getCommunicatorVehicles = (state: AppState) =>
  state.communicatorVehicles.communicatorVehicles
export const hasLoadedCommunicatorVehicles = (state: AppState) =>
  !state.communicatorVehicles.loading
