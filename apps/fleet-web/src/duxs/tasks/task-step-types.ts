import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { AppState } from 'src/root-reducer'
import type { FetchTaskStepTypes } from 'api/tasks/task-step-types'

type State = {
  taskStepTypes: FetchTaskStepTypes['taskStepTypes']
  loading: boolean
  response: {
    status: string
    type: string
  } | null
}

// Reducer
const initialState: State = {
  taskStepTypes: [],
  loading: true,
  response: null,
}

const slice = createSlice({
  name: 'task-step-types',
  initialState,
  reducers: {
    fetchTaskStepTypes(draft) {
      draft.loading = true
    },
    createTaskStepType(draft, _: PayloadAction<{ description: string }>) {
      draft.loading = true
    },
    updateTaskStepType(draft, _: PayloadAction<{ id: string; description: string }>) {
      draft.loading = true
    },
    deleteTaskStepType(draft, _: PayloadAction) {
      draft.loading = true
    },
    receiveTaskStepTypes(
      draft,
      { payload }: PayloadAction<{ taskStepTypes: State['taskStepTypes'] }>,
    ) {
      draft.taskStepTypes = payload.taskStepTypes
      draft.loading = false
    },
  },
})

export default slice.reducer

export const {
  createTaskStepType,
  deleteTaskStepType,
  fetchTaskStepTypes,
  receiveTaskStepTypes,
  updateTaskStepType,
} = slice.actions

// Selectors
export const getTaskStepTypes = (state: AppState) => state.taskStepTypes.taskStepTypes
export const hasLoadedTaskStepTypes = (state: AppState) => !state.taskStepTypes.loading
