// Actions
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'

export const FETCH_TASK_SUSPEND_REASONS = 'FETCH_TASK_SUSPEND_REASONS'
export const RECEIVE_TASK_SUSPEND_REASONS = 'RECEIVE_TASK_SUSPEND_REASONS'
export const CREATE_TASK_SUSPEND_REASON = 'CREATE_TASK_SUSPEND_REASON'
export const UPDATE_TASK_SUSPEND_REASON = 'UPDATE_TASK_SUSPEND_REASON'
export const UPDATE_TASK_SUSPEND_REASON_STATUS = 'UPDATE_TASK_SUSPEND_REASON_STATUS'
export const DELETE_TASK_SUSPEND_REASON = 'DELETE_TASK_SUSPEND_REASON'
export const RECEIVE_TASK_SUSPEND_RESPONSE = 'RECEIVE_TASK_SUSPEND_RESPONSE'
export const RECEIVE_TASK_SUSPEND_DELETE_RESPONSE =
  'RECEIVE_TASK_SUSPEND_DELETE_RESPONSE'

type State = {
  taskSuspendReasons: Array<FixMeAny>
  loading: boolean
  response: FixMeAny
}

// Reducer
const initialState: State = {
  taskSuspendReasons: [],
  loading: true,
  response: null,
}

export default function reducer(state = initialState, action: FixMeAny) {
  switch (action.type) {
    // API Calls
    case FETCH_TASK_SUSPEND_REASONS:
    case CREATE_TASK_SUSPEND_REASON:
    case UPDATE_TASK_SUSPEND_REASON:
    case DELETE_TASK_SUSPEND_REASON:
      return { ...state, loading: true }
    // API Responses
    case RECEIVE_TASK_SUSPEND_RESPONSE:
      return {
        ...state,
        taskSuspendReasons: action.payload.taskSuspendReasons,
        loading: false,
      }
    case RECEIVE_TASK_SUSPEND_DELETE_RESPONSE:
      return {
        ...state,
        response: action.payload.response,
        loading: false,
      }
    default:
      return state
  }
}

// Action Creators
export function fetchTaskSuspendReasons() {
  return {
    type: FETCH_TASK_SUSPEND_REASONS,
  }
}

export function createTaskSuspendReason(taskSuspendReason: FixMeAny) {
  return {
    type: CREATE_TASK_SUSPEND_REASON,
    payload: { taskSuspendReason },
  }
}

export function deleteTaskSuspendReason(taskSuspendReasonId: string | number) {
  return {
    type: DELETE_TASK_SUSPEND_REASON,
    payload: { taskSuspendReasonId },
  }
}

export function updateTaskSuspendReason(taskSuspendReason: FixMeAny) {
  return {
    type: taskSuspendReason.isStatusChange
      ? UPDATE_TASK_SUSPEND_REASON_STATUS
      : UPDATE_TASK_SUSPEND_REASON,
    payload: { taskSuspendReason },
  }
}

// Selectors
export const getTaskSuspendReasons = (state: AppState) =>
  state.taskSuspendReasons.taskSuspendReasons
export const hasLoadedTaskSuspendReasons = (state: AppState) =>
  !state.taskSuspendReasons.loading
