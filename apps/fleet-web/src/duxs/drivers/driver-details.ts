import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import { isEmpty } from 'lodash'

import availableCountryStates from '../../temp-fixes/available-states'

import { apiStatusReducers, createStatusSelectors } from 'duxs/utils'
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'
import { getELDStatus } from 'duxs/user-sensitive-selectors'

type State = {
  driverDetails: Record<string, FixMeAny>
  availableCountryStates: FixMeAny
  driverLicenseClasses: Array<Record<string, FixMeAny>>
  driverSpecialLicenseTypes: Array<Record<string, FixMeAny>>
}

const initialState: State = {
  driverDetails: {},
  availableCountryStates: [],
  driverLicenseClasses: [],
  driverSpecialLicenseTypes: [],
}

const { reducer, actions } = createSlice({
  name: 'driverDetails',
  initialState,
  reducers: {
    ...apiStatusReducers,
    fetchDriverDetails: (_draft, _: PayloadAction<string>) => {},
    createDriver: (_draft, _: PayloadAction<FixMeAny>) => {},
    updateDriverDetails: (_draft, _: PayloadAction<FixMeAny>) => {},
    toggleDriverStatus: (_draft, _: PayloadAction<Record<string, FixMeAny>>) => {},

    clearDriverDetails: (draft) => {
      draft.driverDetails = {}
    },

    onFetchDriverDetails: (
      draft,
      {
        payload: {
          driverDetails,
          countryStates,
          licenseClasses,
          driverSpecialLicenseTypes,
        },
      },
    ) => {
      draft.driverDetails = driverDetails
      draft.availableCountryStates = countryStates
      draft.driverLicenseClasses = licenseClasses
      draft.driverSpecialLicenseTypes = driverSpecialLicenseTypes
    },
    onCreateDriver: (state) => state,
    onUpdateDriverDetails: (
      draft,
      { payload: { updatedDriverDetails, countryStates } },
    ) => {
      draft.driverDetails = updatedDriverDetails
      draft.availableCountryStates = countryStates
    },
    onToggleDriverStatus: (draft) => {
      draft.driverDetails.active = !draft.driverDetails.active
    },
  },
})

const statusSelectors = createStatusSelectors((state) => state.driverDetails)

const selectors = {
  ...statusSelectors,
  getDriverDetails: (state: AppState) => state.driverDetails.driverDetails || {},
  getAvailableCountryStates: (state: AppState) => {
    const hasELD = getELDStatus(state)
    return isEmpty(state.driverDetails.availableCountryStates) && hasELD
      ? availableCountryStates
      : state.driverDetails.availableCountryStates
  },
  getHasAvailableCountryStates: (state: AppState) => {
    const hasELD = getELDStatus(state)
    return (
      (hasELD
        ? availableCountryStates
        : state.driverDetails.availableCountryStates || []
      ).length > 0
    )
  },
  isDriverDetailsLoading: statusSelectors.isSomeLoading,
  getDriverLicenseClasses: (state: AppState) =>
    state.driverDetails.driverLicenseClasses || [],
  getDriverSpecialLicenseTypes: (state: AppState) =>
    state.driverDetails.driverSpecialLicenseTypes || [],
}

export { reducer, actions, selectors }
