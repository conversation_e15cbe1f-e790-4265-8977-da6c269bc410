export const FETCH_WEB_ELD = 'FETCH_WEB_ELD'
export const RECEIVE_WEB_ELD = 'RECEIVE_WEB_ELD'
export const FETCH_WEB_DRIVER_ELD = 'FETCH_WEB_DRIVER_ELD'
export const RECEIVE_WEB_DRIVER_ELD = 'RECEIVE_WEB_DRIVER_ELD'
export const FETCH_WEB_TERMINAL_ELD = 'FETCH_WEB_TERMINAL_ELD'
export const RECEIVE_WEB_TERMINAL_ELD = 'RECEIVE_WEB_TERMINAL_ELD'

const initialState = {
  eldLogs: [],
  driverEldLogs: {},
  terminalEldLogs: [],
  loading: false,
}

// ELD reducer

const eld = (state = initialState, action) => {
  switch (action.type) {
    case FETCH_WEB_ELD:
    case FETCH_WEB_DRIVER_ELD:
    case FETCH_WEB_TERMINAL_ELD:
      return {
        ...state,
        eldLogs: [],
        driverEldLogs: {},
        terminalEldLogs: [],
        loading: true,
      }

    case RECEIVE_WEB_ELD:
      return {
        ...state,
        eldLogs: action.payload,
        loading: false,
      }

    case RECEIVE_WEB_DRIVER_ELD:
      return {
        ...state,
        driverEldLogs: action.payload,
        loading: false,
      }

    case RECEIVE_WEB_TERMINAL_ELD:
      return {
        ...state,
        terminalEldLogs: action.payload,
        loading: false,
      }

    default:
      return state
  }
}

// Actions
export const fetchWebEld = (startDateTime, endDateTime, limit, unassignedOnly) => ({
  type: FETCH_WEB_ELD,
  payload: {
    startDateTime,
    endDateTime,
    limit,
    unassignedOnly,
  },
})

export const fetchDriverWebEld = (startDateTime, endDateTime, driverId) => ({
  type: FETCH_WEB_DRIVER_ELD,
  payload: {
    startDateTime,
    endDateTime,
    driverId,
  },
})

export const eldIsLoading = ({ eld: { loading } }) => loading
export const getEldLogs = ({ eld: { eldLogs = [] } }) => eldLogs
export const getDriverEld = ({ eld: { driverEldLogs = {} } }) => driverEldLogs
export const getTerminalTimelineEld = ({ eld: { terminalEldLogs = [] } }) =>
  terminalEldLogs

export default eld
