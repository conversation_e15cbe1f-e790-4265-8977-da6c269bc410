import { createSelector, createReducer, createAction } from '@reduxjs/toolkit'
import {
  flatMap,
  mapValues,
  pick,
  groupBy,
  pickBy,
  map,
  uniqBy,
  partition,
} from 'lodash'
import {
  buildELDTrips,
  buildELDManualTrips,
  buildELDCurrentEditedELDTrips,
  groupEldEventsByDay,
  buildTimelineData,
} from '../../process-data/eld-events'
import { getDriversMap } from 'duxs/drivers'
import { getCachedVehiclesByTerminal, RECEIVE_DRIVER_ELD_EVENTS } from 'duxs/shared'
import { getUserTimeZoneIANA } from 'duxs/user-sensitive-selectors'
import {
  dateStringToDateTime,
  getTimeZoneOffset,
  accumUniq,
  addReduce,
  TIME,
} from 'cartrack-utils'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny } from 'src/types'

const actionPrefix = 'eld-events'

export const cancelDriverEditRequest = createAction<{
  driverId: FixMeAny
  editId: FixMeAny
}>(actionPrefix + 'cancelDriverEditRequest')

export const fetchDriverEditRequests = createAction(
  actionPrefix + 'fetchDriverEditRequests',
)
export const updateDriverELDEvents = createAction<{
  driverId: FixMeAny
  updatedEvents: FixMeAny
  startTime: FixMeAny
  endTime: FixMeAny
}>(actionPrefix + 'updateDriverELDEvents')

export const sendELDReport = createAction<Record<string, any>>(
  actionPrefix + 'sendELDReport',
)

export const transferELDLogs = createAction<Record<string, any>>(
  actionPrefix + 'transferELDLogs',
)

// Actions
export const FETCH_ELD_EVENTS = 'FETCH_ELD_EVENTS'
export const RECEIVE_ELD_EVENTS = 'RECEIVE_ELD_EVENTS'
export const FETCH_DRIVER_ELD_EVENTS = 'FETCH_DRIVER_ELD_EVENTS'
export const RECEIVE_DRIVER_EDIT_REQUESTS = 'RECEIVE_DRIVER_EDIT_REQUESTS'
export const ON_CANCEL_EDIT_REQUEST = 'ON_CANCEL_EDIT_REQUEST'
export const REASSIGN_TRIPS = 'REASSIGN_TRIPS'
export const POLL_ELD_EVENTS = 'POLL_ELD_EVENTS'
export const RECEIVE_POLLED_ELD_EVENTS = 'RECEIVE_POLLED_ELD_EVENTS'
export const UNASSIGN_TRIP = 'UNASSIGN_TRIP'
export const RECEIVE_UNASSIGNED_EVENTS = 'RECEIVE_UNASSIGNED_EVENTS'

type State = {
  events: Array<Record<string, FixMeAny>>
  editRequests: Array<Record<string, FixMeAny>> | undefined
  loading: boolean
  loadingDriverELDEvents: boolean
  polledEventsByDriver: Record<string, FixMeAny>
  isUnassigning: boolean | undefined
}

export const initialState: State = {
  events: [],
  editRequests: undefined,
  loading: false,
  loadingDriverELDEvents: false,
  polledEventsByDriver: {},
  isUnassigning: undefined,
}

export default createReducer(initialState, {
  [FETCH_ELD_EVENTS]: (draft, { meta }) => {
    if (meta.clearEvents) {
      draft.events = []
    }

    draft.loading = true
  },
  [RECEIVE_ELD_EVENTS]: (draft, { payload }) => {
    draft.loading = false
    draft.events = uniqBy([...payload.ELDEvents, ...draft.events], 'id')
  },
  [RECEIVE_DRIVER_ELD_EVENTS]: (draft, { payload }) => {
    draft.loadingDriverELDEvents = false
    draft.events = uniqBy([...payload.ELDEvents, ...draft.events], 'id')
  },
  [RECEIVE_UNASSIGNED_EVENTS]: (draft, { payload }) => {
    draft.isUnassigning = false
    draft.events = uniqBy([...payload.ELDEvents, ...draft.events], 'id')
  },
  [FETCH_DRIVER_ELD_EVENTS]: (draft) => {
    draft.events = []
    draft.loadingDriverELDEvents = true
  },
  [RECEIVE_POLLED_ELD_EVENTS]: (draft, { payload }) => {
    draft.polledEventsByDriver = pickBy(
      groupBy(payload.ELDEvents, 'clientDriverId'),
      (_v, k) => k !== 'null',
    )
  },
  [UNASSIGN_TRIP]: (draft, { payload }) => {
    draft.isUnassigning = payload.eventId
  },
  [RECEIVE_DRIVER_EDIT_REQUESTS]: (draft, { payload }) => {
    draft.editRequests = payload.requests
  },
  [ON_CANCEL_EDIT_REQUEST]: (draft, { payload }) => {
    if (draft.editRequests !== undefined) {
      const requestIndex = draft.editRequests.findIndex(
        (e) => e.editId === payload.editId,
      )
      if (requestIndex !== -1) {
        draft.editRequests.splice(requestIndex, 1)
      }
    }
  },
})

// Action Creators
export function fetchELDEvents({
  startDateTime,
  endDateTime,
  limit,
  unassignedOnly,
  terminalSerials,
}: {
  startDateTime: FixMeAny
  endDateTime: FixMeAny
  limit: FixMeAny
  unassignedOnly: FixMeAny
  terminalSerials: FixMeAny
}) {
  return {
    type: FETCH_ELD_EVENTS,
    payload: {
      startDateTime,
      endDateTime,
      limit,
      unassignedOnly,
      terminalSerials,
    },
    meta: {
      return: RECEIVE_ELD_EVENTS,
      clearEvents: !terminalSerials,
    },
  }
}

export function fetchDriverELDEvents({
  driverId,
  startDateTime,
  endDateTime,
  limit,
}: {
  driverId: FixMeAny
  startDateTime: FixMeAny
  endDateTime: FixMeAny
  limit?: FixMeAny
}) {
  return {
    type: FETCH_DRIVER_ELD_EVENTS,
    payload: {
      driverId,
      startDateTime,
      endDateTime,
      limit,
    },
    meta: {
      return: RECEIVE_DRIVER_ELD_EVENTS,
      isDriver: true,
    },
  }
}

export function pollELDEvents() {
  const utcMidnight =
    new Date().setHours(0, 0, 0, 0) - new Date().getTimezoneOffset() * 6e4
  // Start boundry: EST (UTC-4)
  const startDateTime = utcMidnight + 3.6e6 * 4
  // End boundry: Hawaii (UTC-10)
  const endDateTime = utcMidnight + TIME.HOURS_24 + 3.6e6 * 10 - 1

  return {
    type: POLL_ELD_EVENTS,
    payload: {
      startDateTime,
      endDateTime,
      limit: 100000,
    },
    meta: {
      return: RECEIVE_POLLED_ELD_EVENTS,
    },
  }
}

export function reassignTrips(trips: Array<Record<string, FixMeAny>>) {
  return {
    type: REASSIGN_TRIPS,
    payload: {
      trips,
    },
  }
}

export function unassignTrip(
  terminalSerial: FixMeAny,
  driverId: FixMeAny,
  eventId: FixMeAny,
) {
  return {
    type: UNASSIGN_TRIP,
    payload: {
      terminalSerial,
      driverId,
      eventId,
    },
  }
}

// Utilities
const getELDEvents = (state: AppState) => state.ELDEvents.events
export const getEditRequests = createSelector(
  (state: AppState) => state.ELDEvents.editRequests || [],
  (requests) => {
    const [active, historic] = partition(requests, (r) => r.status === 0)
    return {
      active,
      historic,
    }
  },
)

function reconciliateItems(
  events:
    | Array<Record<string, FixMeAny>>
    | Record<string, Array<Record<string, FixMeAny>>>,
  drivers: Record<string, FixMeAny>,
  vehicles: Record<string, FixMeAny>,
) {
  if (Array.isArray(events))
    return events.map((e) => ({
      ...e,
      lastDriver: e.lastClientDriverId && drivers[e.lastClientDriverId],
      driver: e.clientDriverId && drivers[e.clientDriverId],
      claimer: e.claimerId && drivers[e.claimerId],
      vehicle: vehicles[e.terminalSerial],
    }))

  return mapValues(events, (eArr) =>
    eArr.map((e) => ({
      ...e,
      lastDriver: e.lastClientDriverId && drivers[e.lastClientDriverId],
      driver: e.clientDriverId && drivers[e.clientDriverId],
      claimer: e.claimerId && drivers[e.claimerId],
      vehicle: vehicles[e.terminalSerial],
    })),
  )
}

const getELDEventsByTerminal = createSelector(getELDEvents, (events) =>
  groupBy(events, 'terminalSerial'),
)
const getELDEventsByDriver = createSelector(getELDEvents, (events) =>
  groupBy(events, 'clientDriverId'),
)
export const getLoading = (state: AppState) => state.ELDEvents.loading
export const getLoadingDriverELDEvents = (state: AppState) =>
  state.ELDEvents.loadingDriverELDEvents
export const getIsUnassigning = (state: AppState) => state.ELDEvents.isUnassigning

export const getDriverELDEvents = (state: AppState, id: FixMeAny) =>
  getELDEventsByDriver(state)[id] || []

const getDriverELDEventsWithDrivers = createSelector(
  getDriverELDEvents,
  getDriversMap,
  getCachedVehiclesByTerminal,
  reconciliateItems,
)
const getELDEventsByTerminalWithDrivers = createSelector(
  getELDEventsByTerminal,
  getDriversMap,
  getCachedVehiclesByTerminal,
  reconciliateItems,
)

export const getGroupedELDEventsForDriver = createSelector(
  (state: AppState, driverId: FixMeAny) => getDriverELDEvents(state, driverId || null),
  groupEldEventsByDay,
)

export const getELDTrips = createSelector(
  getELDEventsByTerminalWithDrivers,
  getUserTimeZoneIANA,
  (events, defaultTimeZone) =>
    flatMap(events, (events) =>
      buildELDTrips(
        [...(events as Array<FixMeAny>).sort((a, b) => (a.time > b.time ? 1 : -1))],
        defaultTimeZone,
      ),
    ),
)

export const getELDTripsByTerminal = createSelector(
  getELDEventsByTerminalWithDrivers,
  (_: FixMeAny, terminalSerials: FixMeAny) => terminalSerials,
  getUserTimeZoneIANA,
  (events, terminals, defaultTimeZone) => {
    const forTerminals = pick(events, terminals)
    return flatMap(forTerminals, (events: Array<FixMeAny>) =>
      buildELDTrips(
        [...events.sort((a, b) => (a.time > b.time ? 1 : -1))],
        defaultTimeZone,
      ),
    )
  },
)

const getDriverELDTrips = createSelector(
  getDriverELDEventsWithDrivers,
  (state: AppState, driverId: string | number) => getDriversMap(state)[driverId],
  (_: AppState, _2: FixMeAny, useDriverTimeZone: boolean) => useDriverTimeZone,
  (events, driver, useDriverTimeZone) =>
    buildELDTrips(
      events,
      useDriverTimeZone ? driver && driver.currentTimeZoneId : undefined,
    ),
)

const getDriverELDOnDutyTrips = createSelector(getDriverELDEvents, buildELDManualTrips)

export const getCurrentEditedELDTrips = createSelector(
  (state: AppState, driverId: string | number) =>
    getDriverELDTrips(state, driverId, true),
  (_: AppState, _2: FixMeAny, editedTrips: FixMeAny) => editedTrips,
  getDriverELDOnDutyTrips,
  getUserTimeZoneIANA,
  buildELDCurrentEditedELDTrips,
)

const addTerminalStats = (day: Array<Record<string, FixMeAny>>) => ({
  drivers: accumUniq(day, (d: FixMeAny) => (d.driver && d.driver.name) || undefined),
  totalTrips: day.filter((e) => e.ELDEventType === 8 && e.ELDEventCode === 1).length,
  totalUnassignedTrips: day.filter(
    (e) => !e.clientDriverId && e.ELDEventType === 8 && e.ELDEventCode === 1,
  ).length,
  unassignedTime:
    addReduce(
      day,
      'pctWidth',
      (e: FixMeAny) =>
        !e.clientDriverId && e.ELDEventType === 8 && e.ELDEventCode === 1,
    ) * 864000,
})

const calcTimelineStartTime = (dateString: FixMeAny, timeZoneName: FixMeAny) =>
  (dateStringToDateTime(dateString, {
    utc: false,
  }) as FixMeAny) - getTimeZoneOffset(timeZoneName, dateString)

export const getELDTimelinesForAllTerminals = createSelector(
  getELDEventsByTerminalWithDrivers,
  getUserTimeZoneIANA,
  getCachedVehiclesByTerminal,
  (eventsByTerminal, timeZoneName, vehiclesByTerminal) =>
    flatMap(eventsByTerminal, (events = [], terminalSerial) => {
      const eventsWithoutManualDrive = (events as Array<FixMeAny>).filter(
        (e) => !(e.ELDEventType === 1 && e.ELDEventCode === 3),
      )
      return map(
        groupEldEventsByDay(eventsWithoutManualDrive),
        (events, dateString) => {
          const { events: timelineEvents, stats } = buildTimelineData(
            events,
            calcTimelineStartTime(dateString, timeZoneName),
            { extraStatsTransform: addTerminalStats } as FixMeAny,
          )

          const eventsWithAddedStatus = timelineEvents.map((e: FixMeAny) =>
            e.statusClassName === 'driving' && !e.clientDriverId
              ? {
                  ...e,
                  statusClassName: 'driving-unassigned',
                }
              : e,
          )

          return {
            events: eventsWithAddedStatus,
            stats,
            dateString,
            terminalSerial,
            registration: (vehiclesByTerminal[terminalSerial] || {}).registration,
          }
        },
      )
    }),
)

export const getELDTimelinesForAllDays = createSelector(
  getGroupedELDEventsForDriver,
  getUserTimeZoneIANA,
  (days, timeZoneName) =>
    mapValues(days, (events: Array<any> = [], dateString) =>
      buildTimelineData(events, calcTimelineStartTime(dateString, timeZoneName)),
    ),
)

export const getELDEditedTimelineForDay = createSelector(
  getGroupedELDEventsForDriver,
  getEditRequests,
  (_: AppState, _2: FixMeAny, dateString: FixMeAny) => dateString,
  getUserTimeZoneIANA,
  (eventsByDay, editRequests, dateString, timeZoneName) => {
    if (!editRequests.active[0] || !eventsByDay[dateString]) return null

    const { editStartTs, editEndTs, events } = editRequests.active[0]

    const filteredEvents = eventsByDay[dateString]
      .filter(
        (e) =>
          (e.ELDEventType === 8 && e.ELDEventCode === 1) ||
          e.time <= editStartTs ||
          e.time >= editEndTs,
      )
      .concat(...events)

    return {
      ...buildTimelineData(
        filteredEvents,
        calcTimelineStartTime(dateString, timeZoneName),
      ),
      original: editRequests.active[0],
    }
  },
)
