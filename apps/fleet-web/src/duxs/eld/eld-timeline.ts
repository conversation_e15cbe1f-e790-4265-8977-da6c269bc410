import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { GetDriverDayEventsResolved } from 'api/timeline'
import type { AppState } from 'src/root-reducer'

type State = {
  loadingDayEvents: boolean
  driverDayRaw: GetDriverDayEventsResolved | undefined
}

const initialState: State = {
  loadingDayEvents: false,
  driverDayRaw: undefined,
}

const slice = createSlice({
  name: 'eld-timeline',
  initialState,
  reducers: {
    fetchDriverDayEvents(draft, _: PayloadAction<Record<string, any>>) {
      draft.loadingDayEvents = true
    },
    gotDriverDayEvents(draft, { payload }: PayloadAction<GetDriverDayEventsResolved>) {
      draft.loadingDayEvents = false
      draft.driverDayRaw = payload
    },
  },
})

export const { fetchDriverDayEvents, gotDriverDayEvents } = slice.actions

export default slice.reducer

const getState = (state: AppState) => state.eldTimeline

export const getDriverDayEvents = (state: AppState) =>
  getState(state).driverDayRaw?.chart

export const getDriverDayRaw = (state: AppState) => getState(state).driverDayRaw

export const getLoadingDayEvents = (state: AppState) => getState(state).loadingDayEvents
