import type { GlobalQueryMeta } from 'src/RouterRoot'
import {
  useSnackbarWithCloseAction,
  type UseSnackbarWithCloseActionReturnType,
} from 'src/components/Snackbar/Notistack/utils'
import { ctToast } from 'src/util-components/ctToast'

export function makeMutationErrorHandlerWithToast(params?: {
  customErrorMessage?: string
  nonStandardErrorHandler?: (nonStandardError: unknown) => Promise<unknown> | void
}) {
  return {
    onError(error: unknown | Error): Promise<unknown> | void {
      if (error instanceof Error) {
        ctToast.fire('error', params?.customErrorMessage ?? error.message)
        if (ENV.NODE_ENV === 'development') {
          // So that we can debug errors properly in localhost
          console.error(error)
        }
        return
      }

      return params?.nonStandardErrorHandler?.(error)
    },
  }
}

export function makeQueryErrorHandlerWithToast<
  ExtraMeta extends Record<string, unknown>,
>(params?: {
  customErrorMessage?: string
  nonStandardErrorHandler?: (nonStandardError: unknown) => void
  extraMeta?: ExtraMeta
}) {
  const baseMeta = {
    metaErrorHandler: (error: unknown | Error): void => {
      if (error instanceof Error) {
        ctToast.fire('error', params?.customErrorMessage ?? error.message)
        if (ENV.NODE_ENV === 'development') {
          // So that we can debug errors properly in localhost
          console.error(error)
        }
        return
      }

      return params?.nonStandardErrorHandler?.(error)
    },
  } satisfies Pick<GlobalQueryMeta, 'metaErrorHandler'>

  return {
    meta: {
      ...baseMeta,
      ...params?.extraMeta,
    },
  }
}

export function useMutationErrorHandlerWithSnackbar(params?: {
  customErrorMessage?: string
  nonStandardErrorHandler?: (
    nonStandardError: unknown,
    { enqueueSnackbarWithCloseAction }: UseSnackbarWithCloseActionReturnType,
  ) => Promise<unknown> | void
}) {
  const { enqueueSnackbarWithCloseAction } = useSnackbarWithCloseAction()

  return {
    onError(error: unknown | Error): Promise<unknown> | void {
      if (error instanceof Error) {
        enqueueSnackbarWithCloseAction(params?.customErrorMessage ?? error.message, {
          variant: 'error',
        })
        if (ENV.NODE_ENV === 'development') {
          // So that we can debug errors properly in localhost
          console.error(error)
        }
        return
      }

      return params?.nonStandardErrorHandler?.(error, {
        enqueueSnackbarWithCloseAction,
      })
    },
  }
}

export function useQueryErrorHandlerWithToast<
  ExtraMeta extends Record<string, unknown>,
>(params?: {
  customErrorMessage?: string
  nonStandardErrorHandler?: (
    nonStandardError: unknown,
    { enqueueSnackbarWithCloseAction }: ReturnType<typeof useSnackbarWithCloseAction>,
  ) => void
  extraMeta?: ExtraMeta
}) {
  const { enqueueSnackbarWithCloseAction } = useSnackbarWithCloseAction()

  const baseMeta: Pick<GlobalQueryMeta, 'metaErrorHandler'> = {
    metaErrorHandler: (error: unknown | Error): void => {
      if (error instanceof Error) {
        enqueueSnackbarWithCloseAction(params?.customErrorMessage ?? error.message, {
          variant: 'error',
        })
        return
      }

      return params?.nonStandardErrorHandler?.(error, {
        enqueueSnackbarWithCloseAction,
      })
    },
  }

  return {
    meta: {
      ...baseMeta,
      ...params?.extraMeta,
    },
  }
}
