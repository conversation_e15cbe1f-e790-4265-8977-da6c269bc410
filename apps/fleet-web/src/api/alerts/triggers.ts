export enum AlertTrigger {
  ENTER_GEOFENCE = '1',
  LEAVE_GEOFENCE = '2',
  SPEED_OVER = '3',
  IGNITION_ON = '4',
  INVALID_ID = '5',
  IGNITION_OFF = '6',
  ID_DETECTED = '7',
  MAX_SPEED_EXCEEDED = '8',
  HARSH_BRAKING = '9',
  HARSH_CORNERING_TURNING = '10',
  HARSH_ACCELERATION = '11',
  ID_NOT_DETECTED = '12',
  UNSCHEDULED_USAGE = '13',
  EXCESSIVE_IDLING = '14',
  ENTER_SYSTEM_ZONE = '15',
  LEAVE_SYSTEM_ZONE = '16',
  POWER_ON = '21',
  POWER_OFF = '22',
  TRIP_SUMMARY = '23',
  OVER_REV = '24',
  OVER_RPM = '25',
  EXCESSIVE_DRIVE_TIME = '26',
  STATIONARY_VEHICLE_ON_OFF = '27',
  STATIONARY_VEHICLE_ON = '28',
  MOTION_DETECTED = '29',
  SENSOR_ENGINE_HIGH = '32',
  SENSOR_ENGINE_LOW = '33',
  SENSOR_COOLANT_HIGH = '34',
  SENSOR_COOLANT_LOW = '35',
  SENSOR_TEMP1_HIGH = '36',
  SENSOR_TEMP1_LOW = '37',
  SENSOR_TEMP2_HIGH = '38',
  SENSOR_TEMP2_LOW = '39',
  SENSOR_TEMP3_HIGH = '40',
  SENSOR_TEMP3_LOW = '41',
  SENSOR_TEMP4_HIGH = '42',
  SENSOR_TEMP4_LOW = '43',
  SENSOR_UP = '44',
  SENSOR_DOWN = '45',
  FUEL_LOSS = '46',
  FUEL_GAIN = '47',
  ROUTE_START = '50',
  ROUTE_END = '51',
  ROUTE_DEVIATION = '52',
  ROUTE_RETURN_AFTER_DEVIATION = '53',
  ROUTE_CANCELLED_BY_DEVIATION = '54',
  ROUTE_SLOW_PROGRESS = '55',
  ROUTE_NOT_STARTED_WITHIN_TIME = '56',
  GEOFENCE_ARRIVAL_TIME = '57',
  STATIC_BIT_ON = '60',
  STATIC_BIT_OFF = '61',
  TOLL_GATE = '67',
  OVER_ROAD_SPEED = '68',
  ENGINE_BREAKDOWN = '69',
  ENGINE_DIAGNOSTICS = '80',
  TEMPERATURE_DIAGNOSTICS = '81',
  // VISION VEHICLES
  DISTRACTION = '82',
  YAWN = '83',
  FATIGUE = '84',
  PEDESTRIAN_COLLISION = '85',
  FORWARD_COLLISION = '86',
  SMOKE = '87',
  PHONE = '88',
  CAMERA_COVERED = '89',
  HEADWAY_MONITORING = '90',
  LANE_DEPARTURE = '91',
  EYES_CLOSED = '92',
  EYE_DELTA = '95',
  URB_FORWARD_COLLISION = '96',
  BUTTON = '97',
  PASSENGER = '98',
  NO_SEATBELT = '99',
  // VISION FACILITIES
  MULTI_OBJECT = '200',
  LINE_CROSS_OBJECT = '201',
  FACEMASK = '202',
  SOCIAL_DISTANCE = '203',
  ACTION_DETECTION = '204',
  FACIAL_RECOGNITION = '205',
  LICENSE_PLATE_RECOGNITION = '206',
  SAFETY_HELMET_DETECTION = '207',
  CAMERA_STATUS = '208',
  CUSTOM = '209',
  HIGH_PRIORITY_LINE_CROSS = '210',
  ABSENCE_LOITERING = '211',
  SUSPICIOUS_BEHAVIOR = '212',
  CUSTOM_OBJECT_DETECTION = '213',
}

export enum AlertGroupType {
  DRIVER_ID = 'Driver ID',
  EVENTS = 'Events',
  GEOFENCE = 'Geofence',
  ROUTE = 'Route',
  SYSTEM_ZONES = 'System Zones',
  SENSOR_UP = 'Sensor Up',
  SENSOR_DOWN = 'Sensor Down',
  VISION_VEHICLES = 'Vision Vehicles',
  VISION_FACILITIES = 'Vision Facilities',
  BREAKDOWN = 'alert.breakdownNotification',
}

export type Trigger = {
  id: AlertTrigger
  description: string
  descriptionTermTranslationId: string | null | undefined
  group: string
  hasValueField: boolean
  index: string
  isRouteAlert: boolean
}

type AlertType = {
  name: string
  description: string
  triggers?: Array<AlertTrigger>
  groupType?: AlertGroupType
}

export const VisionAlertVehiclesTriggers = [
  AlertTrigger.DISTRACTION,
  AlertTrigger.YAWN,
  AlertTrigger.FATIGUE,
  AlertTrigger.PEDESTRIAN_COLLISION,
  AlertTrigger.FORWARD_COLLISION,
  AlertTrigger.SMOKE,
  AlertTrigger.PHONE,
  AlertTrigger.CAMERA_COVERED,
  AlertTrigger.HEADWAY_MONITORING,
  AlertTrigger.LANE_DEPARTURE,
  AlertTrigger.EYES_CLOSED,
  AlertTrigger.EYE_DELTA,
  AlertTrigger.URB_FORWARD_COLLISION,
  AlertTrigger.BUTTON,
  AlertTrigger.PASSENGER,
  AlertTrigger.NO_SEATBELT,
]

export const VisionAlertFacilitiesTriggers = [
  AlertTrigger.MULTI_OBJECT,
  AlertTrigger.LINE_CROSS_OBJECT,
  AlertTrigger.FACEMASK,
  AlertTrigger.SOCIAL_DISTANCE,
  AlertTrigger.ACTION_DETECTION,
  AlertTrigger.FACIAL_RECOGNITION,
  AlertTrigger.LICENSE_PLATE_RECOGNITION,
  AlertTrigger.SAFETY_HELMET_DETECTION,
  AlertTrigger.CAMERA_STATUS,
  AlertTrigger.CUSTOM,
  AlertTrigger.HIGH_PRIORITY_LINE_CROSS,
  AlertTrigger.ABSENCE_LOITERING,
  AlertTrigger.SUSPICIOUS_BEHAVIOR,
  AlertTrigger.CUSTOM_OBJECT_DETECTION,
]

export const alertsTypes: Array<AlertType> = [
  {
    name: 'Over Rev',
    description: 'Excessive revving is detected',
    triggers: [AlertTrigger.OVER_REV],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Over RPM',
    description: 'Excessive revving is detected',
    triggers: [AlertTrigger.OVER_RPM],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Harsh Acceleration',
    description: 'Harsh acceleration is noted',
    triggers: [AlertTrigger.HARSH_ACCELERATION],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Harsh Braking',
    description: 'Harsh braking is noted',
    triggers: [AlertTrigger.HARSH_BRAKING],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'alert.Harsh Cornering/Turning',
    description: 'Harsh turning is noted',
    triggers: [AlertTrigger.HARSH_CORNERING_TURNING],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Motion Detected',
    description: 'Selected vehicle is in motion with ignition on or off.',
    triggers: [AlertTrigger.MOTION_DETECTED],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Sensors',
    description: 'Installed sensors for vehicle parts are detected',
    triggers: [
      AlertTrigger.SENSOR_ENGINE_HIGH,
      AlertTrigger.SENSOR_ENGINE_LOW,
      AlertTrigger.SENSOR_COOLANT_HIGH,
      AlertTrigger.SENSOR_COOLANT_LOW,
      AlertTrigger.SENSOR_TEMP1_HIGH,
      AlertTrigger.SENSOR_TEMP1_LOW,
      AlertTrigger.SENSOR_TEMP2_HIGH,
      AlertTrigger.SENSOR_TEMP2_LOW,
      AlertTrigger.SENSOR_TEMP3_HIGH,
      AlertTrigger.SENSOR_TEMP3_LOW,
      AlertTrigger.SENSOR_TEMP4_HIGH,
      AlertTrigger.SENSOR_TEMP4_LOW,
      AlertTrigger.SENSOR_UP,
      AlertTrigger.SENSOR_DOWN,
    ],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Speeding',
    description: 'Pre-set maximum speed is exceeded.',
    triggers: [AlertTrigger.SPEED_OVER],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'alert.Static Bit Event',
    description: 'CANBUS installed units are opened or used.',
    triggers: [AlertTrigger.STATIC_BIT_ON, AlertTrigger.STATIC_BIT_OFF],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'alert.Unscheduled Usage',
    description: 'Vehicle is in use outside of designated operation times.',
    triggers: [AlertTrigger.UNSCHEDULED_USAGE],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Entering/Leaving Geofence',
    description: 'Vehicle enters or leaves a geofence',
    triggers: [AlertTrigger.ENTER_GEOFENCE, AlertTrigger.LEAVE_GEOFENCE],
    groupType: AlertGroupType.GEOFENCE,
  },
  {
    name: 'Excessive Drive Time',
    description: 'Limit for drive time is reached or exceeded',
    triggers: [AlertTrigger.EXCESSIVE_DRIVE_TIME],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Excessive Idling',
    description: 'Vehicle exceeds the preset idling duration.',
    triggers: [AlertTrigger.EXCESSIVE_IDLING],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'System Zone',
    description: 'Vehicle or driver crosses an system zone border.',
    triggers: [AlertTrigger.ENTER_SYSTEM_ZONE, AlertTrigger.LEAVE_SYSTEM_ZONE],
    groupType: AlertGroupType.SYSTEM_ZONES,
  },
  {
    name: 'alert.Toll Gate',
    description: 'Vehicle or driver passes through a toll location.',
    triggers: [AlertTrigger.TOLL_GATE],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Trip Summary',
    description: 'Vehicle does not reach selected geofence by specified date and time',
    triggers: [AlertTrigger.TRIP_SUMMARY],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Driver ID',
    description: 'Driver ID tags are detected, not detected, or invalid',
    triggers: [
      AlertTrigger.INVALID_ID,
      AlertTrigger.ID_DETECTED,
      AlertTrigger.ID_NOT_DETECTED,
    ],
    groupType: AlertGroupType.DRIVER_ID,
  },
  {
    name: 'Fuel',
    description: 'There is an overall fuel loss or gain',
    triggers: [AlertTrigger.FUEL_LOSS, AlertTrigger.FUEL_GAIN],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Distance Driven Reached',
    description: 'A specified distance is reached or exceeded',
  },
  {
    name: 'Operating Time',
    description: 'A designated amount of time has passed',
  },
  {
    name: 'alert.Ignition On/Off',
    description: 'Vehicle ignition is turned on or off',
    triggers: [AlertTrigger.IGNITION_ON, AlertTrigger.IGNITION_OFF],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'alert.Power On/Off',
    description: 'Power of unit is turned on or off',
    triggers: [AlertTrigger.POWER_ON, AlertTrigger.POWER_OFF],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'alert.Max Speed Exceeded',
    description: "Vehicle's individual maximum speed is exceeded.",
    triggers: [AlertTrigger.MAX_SPEED_EXCEEDED],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Over the Road Speed',
    description: 'Vehicle exceeds the speed limit by a specified amount.',
    triggers: [AlertTrigger.OVER_ROAD_SPEED],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'alert.Stationary Vehicle',
    description: 'Vehicle is stationary for a specified amount of time.',
    triggers: [
      AlertTrigger.STATIONARY_VEHICLE_ON_OFF,
      AlertTrigger.STATIONARY_VEHICLE_ON,
    ],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Route',
    description: 'Route Plan.',
    triggers: [
      AlertTrigger.ROUTE_START,
      AlertTrigger.ROUTE_END,
      AlertTrigger.ROUTE_DEVIATION,
      AlertTrigger.ROUTE_RETURN_AFTER_DEVIATION,
      AlertTrigger.ROUTE_CANCELLED_BY_DEVIATION,
      AlertTrigger.ROUTE_SLOW_PROGRESS,
      AlertTrigger.ROUTE_NOT_STARTED_WITHIN_TIME,
    ],
    groupType: AlertGroupType.ROUTE,
  },
  {
    name: 'alert.Failure to Arrive On Time',
    description: `Vehicle doesn't arrive at a geofence by a specific date/time.`,
    triggers: [AlertTrigger.GEOFENCE_ARRIVAL_TIME],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Engine Diagnostics',
    description: 'Vehicle engine diagnostics data.',
    triggers: [AlertTrigger.ENGINE_DIAGNOSTICS],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Temperature Diagnostics',
    description: 'Vehicle temperature diagnostics data.',
    triggers: [AlertTrigger.TEMPERATURE_DIAGNOSTICS],
    groupType: AlertGroupType.EVENTS,
  },
  {
    name: 'Sensor Up',
    description: 'Sensor Up',
    triggers: [AlertTrigger.SENSOR_UP],
    groupType: AlertGroupType.SENSOR_UP,
  },
  {
    name: 'Sensor Down',
    description: 'Sensor Down',
    triggers: [AlertTrigger.SENSOR_DOWN],
    groupType: AlertGroupType.SENSOR_DOWN,
  },
  // {
  //   name: 'Fraud Alert',
  //   description: 'Fraud Alert',
  //   triggers: [AlertTrigger.FRAUD_ALERT_1, AlertTrigger.FRAUD_ALERT_2],
  //   groupType: AlertGroupType.GEOFENCE,
  // },
  {
    name: 'Vision Vehicles',
    description: 'Vision Vehicles',
    triggers: VisionAlertVehiclesTriggers,
    groupType: AlertGroupType.VISION_VEHICLES,
  },
  {
    name: 'Vision Facilities',
    description: 'Vision Facilities',
    triggers: VisionAlertFacilitiesTriggers,
    groupType: AlertGroupType.VISION_FACILITIES,
  },
  {
    name: 'alert.breakdownNotification',
    description: 'alert.breakdownNotification.description',
    triggers: [AlertTrigger.ENGINE_BREAKDOWN],
    groupType: AlertGroupType.BREAKDOWN,
  },
]
