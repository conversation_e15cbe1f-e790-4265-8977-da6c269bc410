import apiCaller from './api-caller'

export default {
  // DRIVERS PRIVACY
  fetchDriversControlledPrivacy(hasELD) {
    return apiCaller('gdpr_get_drivers_conduct_privacy_state', {
      hasELD: hasELD.toString(),
    }).then((res) => res)
  },

  validatePassword(password) {
    return apiCaller(
      'gdpr_validate_login',
      {
        password,
      },
      { noParse: true },
    )
      .then((response) => response.json())
      .then((responseData) => responseData)
  },

  driverHistory(driverID) {
    return apiCaller('gdpr_show_driver_privacy_history', { driverID }).then(
      (res) => res,
    )
  },

  idTagHistory(idtag) {
    return apiCaller('gdpr_identity_tag_history', { idtag }).then((res) => res)
  },

  setDriversPrivacyMode(driverInputs, hasELD) {
    return apiCaller('gdpr_dynamic_set_privacy_mode', {
      hasELD: hasELD.toString(),
      driverInputs,
    }).then((res) => res)
  },

  getDriversIdTags() {
    return apiCaller('gdpr_get_driver_id_tags').then((res) => res)
  },

  setDriversIdTagsPrivacyMode(tagInputs) {
    return apiCaller('gdpr_mass_update_id_tag_status', {
      tagInputs,
    }).then((res) => res)
  },

  // VEHICLES PRIVACY

  fetchVehiclesPrivacy() {
    return apiCaller('gdpr_get_vehicle_privacy_list', { type: 'total' }).then(
      (res) => res,
    )
  },

  fetchVehicleLocationPrivacy() {
    return apiCaller('gdpr_get_vehicle_privacy_list', {
      type: 'location',
    }).then((res) => res)
  },

  fetchVehicleWorkSchedule(vehicleID) {
    return apiCaller('gdpr_get_vehicle_work_schedule', {
      vehicleID,
      type: 'total',
    }).then((res) => res)
  },

  fetchVehicleLocationWorkSchedule(vehicleID) {
    return apiCaller('gdpr_get_vehicle_work_schedule', {
      vehicleID,
      type: 'location',
    }).then((res) => res)
  },

  saveVehiclePrivacySchedule(vehicleID, matrix, scheduleID) {
    return apiCaller('gdpr_add_privacy_with_schedule', {
      matrix,
      vehicleID,
      type: 'total',
      scheduleID,
    }).then((res) => res)
  },

  saveVehiclePrivacyScheduleMultiple(vehicleIDS, matrix, table) {
    return apiCaller('gdpr_add_privacy_with_schedule_multiple', {
      matrix,
      vehicleIDS,
      type: table,
    }).then((res) => res)
  },

  saveVehicleLocationPrivacySchedule(vehicleID, matrix) {
    return apiCaller('gdpr_add_privacy_with_schedule', {
      matrix,
      vehicleID,
      type: 'location',
    }).then((res) => res)
  },

  enableVehicleLocationPrivacy(vehicleID, matrix) {
    return apiCaller('gdpr_add_privacy_with_schedule', {
      matrix,
      vehicleID,
      type: 'location',
    }).then((res) => res)
  },

  disableVehicleLocationPrivacy(vehicleID) {
    return apiCaller('gdpr_delete_vehicle_privacy', {
      vehicleID,
      type: 'location',
    }).then((res) => res)
  },

  updateVehiclePrivacySchedule(scheduleID, matrix) {
    return apiCaller('gdpr_update_work_schedule', {
      scheduleID,
      matrix,
    }).then((res) => res)
  },

  updateDaysToKeepVehicleData(vehicleID, days, scheduleID) {
    return apiCaller('gdpr_update_privacy_delete_date', {
      vehicleID,
      days,
      scheduleID,
    }).then((res) => res)
  },

  getManualDeleteHistory(vehicleID, latest_entry = null) {
    const params = { latest_entry }

    if (vehicleID) {
      params.vehicleID = vehicleID
    }

    return apiCaller('gdpr_get_privacy_manual_vehicle_info', params).then((res) => res)
  },

  getDriverManualDeleteHistory(clientDriverID, latest_entry = null) {
    const params = { latest_entry }

    if (clientDriverID) {
      params.clientDriverID = clientDriverID
    }

    return apiCaller('gdpr_get_privacy_manual_driver_info', params).then((res) => res)
  },

  insertManualVehicleInterval(vehicle_id, start_date, end_date) {
    return apiCaller('gdpr_insert_vehicle_manual_privacy', {
      vehicle_id,
      start_date,
      end_date,
    }).then((res) => res)
  },

  insertManualDriverInterval(clientDriverID, start_date, end_date) {
    return apiCaller('gdpr_insert_driver_manual_privacy', {
      clientDriverID,
      start_date,
      end_date,
    }).then((res) => res)
  },

  getAllProcessStatus() {
    return apiCaller('gdpr_get_privacy_process_status').then((res) => res)
  },
}
