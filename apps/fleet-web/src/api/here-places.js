export const hereFindPlaces = (
  query = '',
  countryCode,
  successCB,
  errorCB,
  hereApiCode,
) => {
  const urlParams = `https://places.sit.ls.hereapi.com/places/v1/discover/search?apiKey=${hereApiCode}&at=0,0${
    countryCode ? `&addressFilter=countryCode=${countryCode}` : ''
  }&size=5&q=${query}`
  return fetch(urlParams)
    .then((response) => response.json())
    .then((res) => {
      if (res && res.results) {
        const {
          results: { items },
        } = res
        const data = items.map((item) => ({
          type: 'place',
          id: item.id,
          name: item.title,
          address: (item.vicinity || '').replace(/<br\/>/g, ' '),
          photo: '',
          icon: item.icon,
          lat: item.position[0],
          lng: item.position[1],
        }))

        return successCB(data)
      } else {
        return errorCB([])
      }
    })
    .catch(() => errorCB([]))
}
