import apiCaller from 'src/api/api-caller'

function parseVehicle(rawCommunicatorVehicles: Record<string, any>) {
  return (
    rawCommunicatorVehicles &&
    rawCommunicatorVehicles.map((vehicle: Record<string, any>) => ({
      vehicleId: vehicle.vehicle_id,
      vehicleName: vehicle.vehicle_name,
    }))
  )
}

export default {
  fetchCommunicatorVehicles() {
    return apiCaller('communicator_get_user_vehicles', {}, { noX: true }).then((res) =>
      parseVehicle(res),
    )
  },
}
