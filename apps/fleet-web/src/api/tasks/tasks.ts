import {
  clone,
  flatMap,
  isEqual,
  isNil,
  keyBy,
  maxBy,
  minBy,
  values,
  isEmpty,
} from 'lodash'
import moment from 'moment'
import download from 'downloadjs'

import apiCaller from '../api-caller'
import { getTaskRepeatDates } from './task-repeat'
import { parseServiceCosts } from './service-cost-types'
import { parseStockItems } from './stock-item-types'
import { parseContact, parseContactEmails, parseContactNumbers } from './customers'
import { dateTimeToLocalTimestamp } from 'cartrack-utils'
import { formatDate } from 'cartrack-moment-utils'
import type { FixMeAny } from 'src/types'

function parseDownloadCount(rawData: FixMeAny) {
  if (rawData.communicator_get_tasks_count) {
    return rawData.communicator_get_tasks_count
  }
  return null
}

function parseTask(rawTask: FixMeAny) {
  // Communicator user name
  const firstName = rawTask.communicator_user_first_name ?? ''
  const lastName = rawTask.communicator_user_last_name ?? ''
  const name = `${firstName} ${lastName}`.trim()
  const userName = rawTask.communicator_user_name

  return {
    allowRejection: rawTask.allow_rejection === 't',
    assignmentType: rawTask.assignment_type ?? 'user',
    communicatorUser: {
      id: rawTask.communicator_user_id,
      currentLocation: rawTask.communicator_user_current_location,
      email: rawTask.communicator_user_email_address,
      mobileDeviceName: rawTask.communicator_user_mobile_device_name,
      mobileDeviceBattery: rawTask.communicator_user_mobile_device_battery,
      mobileNumber: rawTask.communicator_user_mobile_number,
      name: name || userName,
      vehicleId: rawTask.communicator_user_vehicle_id,
      vehicleRegistration: rawTask.communicator_user_vehicle_registration,
    },
    id: rawTask.task_id,
    orderId: rawTask.order_id,
    publishAt: rawTask.published_at,
    publishedUsers: rawTask.task_publish_total_users,
    repeatCounter: rawTask.repeat_counter,
    repeatDescription: rawTask.repeat_description,
    repeatEndDate: rawTask.repeat_end_date
      ? dateTimeToLocalTimestamp(rawTask.repeat_end_date, {
          time: false,
        })
      : null,
    rejectDescription: rawTask.reject_description,
    rejectedDate: rawTask.rejected_at
      ? formatDate(rawTask.rejected_at, 'YYYY-MM-DD HH:mm')
      : null,
    rejectedRate: rawTask.task_publish_reject_rate_text,
    rejectedUsers: rawTask.task_publish_rejected_users,
    rejectReason: rawTask.reject_reason,
    scheduledArrivalTs: rawTask.scheduled_arrival_ts
      ? formatDate(rawTask.scheduled_arrival_ts, 'YYYY-MM-DD HH:mm')
      : null,
    scheduledStartTime: rawTask.scheduled_start_time
      ? moment(dateTimeToLocalTimestamp(rawTask.scheduled_start_time))
      : null,
    scheduledEndTime: rawTask.scheduled_end_time
      ? moment(dateTimeToLocalTimestamp(rawTask.scheduled_end_time))
      : null,
    status: rawTask.task_state_text,
    statusId: Number(rawTask.task_state_id),
    steps: rawTask.steps,
    stepsInOrder: rawTask.steps_in_order === 't',
    taskStepsOrder: rawTask.steps_in_order === 't' ? 'Succession' : 'Sporadic',
    taskDescription: rawTask.task_description,
    taskGroupId: rawTask.task_group_id,
    taskText: rawTask.task_text,
    taskTypeId: rawTask.task_type_id,
    taskTypeName: rawTask.task_type_text,
    taskAlertStatus: Number(rawTask.alert_status),
  }
}

function parseTaskDetails(rawData: FixMeAny) {
  const rawTask = rawData.communicator_get_task_details
  const rawTaskSteps = rawData.communicator_get_task_step_list
  const rawServiceCosts = rawData.communicator_get_task_step_service_costs
  const rawStockItems = rawData.communicator_get_task_step_stock_items
  const rawSubTasks = rawData.communicator_get_task_subtasks
  const rawTaskPublishUsers = rawData.communicator_get_task_publish_users

  const task = Object.assign(parseTask(rawTask), {
    acceptedTs: rawTask.task_accepted_at
      ? dateTimeToLocalTimestamp(rawTask.task_accepted_at)
      : null,
    closedTs: rawTask.task_closed_at
      ? dateTimeToLocalTimestamp(rawTask.task_closed_at)
      : null,
    createdTs: rawTask.create_ts ? dateTimeToLocalTimestamp(rawTask.create_ts) : null,
    repeatId: Number(rawTask.task_repeat_id),
    repeatCounter: rawTask.repeat_counter ? Number(rawTask.repeat_counter) : 0,
    repeatEndType: rawTask.repeat_counter > 0 ? 'after' : 'by',
    repeatEndDate: rawTask.repeat_end_date
      ? dateTimeToLocalTimestamp(rawTask.repeat_end_date, {
          time: false,
        })
      : null,
    repeatStartDate: rawTask.repeat_start_date
      ? dateTimeToLocalTimestamp(rawTask.repeat_start_date, {
          time: false,
        })
      : null,
    startedTs: rawTask.task_started_at
      ? dateTimeToLocalTimestamp(rawTask.task_started_at)
      : null,
    ...(rawSubTasks && {
      subTasks: rawSubTasks.map((subtask: Record<string, any>) => ({
        id: subtask.task_id,
        startDate: subtask.scheduled_arrival_ts,
        taskStatus: subtask.task_state_id,
      })),
    }),
    ...(rawTaskPublishUsers && {
      publishUsers: {
        publishDate: rawTaskPublishUsers.publish_date,
        rejectedBy: rawTaskPublishUsers.rejected_by,
        rejectionPercentage: rawTaskPublishUsers.percent_rejected,
        totalUsers: rawTaskPublishUsers.total_published,
      },
    }),
  })

  const taskSteps =
    rawTaskSteps &&
    rawTaskSteps.map((rawTaskStep: FixMeAny) => {
      const photos = rawTaskStep.photos

      const serviceCosts = parseServiceCosts(
        rawServiceCosts.filter(
          (cost: FixMeAny) => cost.task_step_id === rawTaskStep.task_step,
        ),
      )

      const stockItems = parseStockItems(
        rawStockItems.filter(
          (item: FixMeAny) => item.task_step_id === rawTaskStep.task_step,
        ),
      )

      return {
        addressLine: rawTaskStep.address_line,
        allowRejection: rawTaskStep.allow_rejection === 't',
        assignmentType: rawTask.assignment_type ? rawTask.assignment_type : 'user',
        country: rawTaskStep.country,
        customerAddress: rawTaskStep.customer_address,
        contactEmail: rawTaskStep.contact_email,
        // eslint-disable-next-line no-nested-ternary
        contactEmails: !isEmpty(rawTaskStep.contact_emails)
          ? parseContactEmails(rawTaskStep.contact_emails)
          : !isEmpty(rawTaskStep.contact_email)
          ? parseContact('email', rawTaskStep.contact_email)
          : null,
        contactNumber: rawTaskStep.contact_number,
        // eslint-disable-next-line no-nested-ternary
        contactNumbers: !isEmpty(rawTaskStep.contact_numbers)
          ? parseContactNumbers(rawTaskStep.contact_numbers)
          : !isEmpty(rawTaskStep.contact_number)
          ? parseContact('phone', rawTaskStep.contact_number)
          : null,
        contactPerson: rawTaskStep.contact_person,
        customerId: rawTaskStep.customer_id,
        customerName: rawTaskStep.customer_description,
        customerType: 'existing',
        customerRefId: rawTaskStep.customer_reference_id,
        customerIsModified: false,
        createdByDriver: rawTaskStep.created_by_driver === 't',
        driverNote: rawTaskStep.driver_note,
        lat: rawTaskStep.lat || undefined,
        lon: rawTaskStep.lon || undefined,
        notificationGroupId: rawTaskStep.customer_group_notification_id,
        orderId: rawTaskStep.order_id,
        photos,
        postalCode: rawTaskStep.postal_code,
        province: rawTaskStep.province,
        rejectDescription: rawTaskStep.reject_description,
        rejectReason: rawTaskStep.reject_reason,
        serviceCost: values(serviceCosts).length !== 0,
        stockTake: values(stockItems).length !== 0,
        serviceCosts: keyBy([...serviceCosts], ({ id }) => `serviceCost${id}`),
        suspendReason: rawTaskStep.suspend_reason,
        suspendDescription: rawTaskStep.suspend_reason_description,
        stockItems: keyBy([...stockItems], ({ id }) => `stockItem${id}`),
        suburb: rawTaskStep.suburb,
        unitNumber: rawTaskStep.unit_number,
        requiredPicture: rawTaskStep.required_picture === 't',
        requiredPictureNo: rawTaskStep.required_picture_no,
        requiredSignature: rawTaskStep.required_signature === 't',
        taskStepDateTime: rawTaskStep.scheduled_arrival_ts
          ? dateTimeToLocalTimestamp(rawTaskStep.scheduled_arrival_ts)
          : null,
        taskStepEndTime: rawTaskStep.scheduled_end_time
          ? new Date(
              dateTimeToLocalTimestamp(rawTaskStep.scheduled_end_time),
            ).toISOString()
          : null,
        taskStepStartTime: rawTaskStep.scheduled_start_time
          ? new Date(
              dateTimeToLocalTimestamp(rawTaskStep.scheduled_start_time),
            ).toISOString()
          : null,
        taskStepDescription: rawTaskStep.task_step_description,
        taskStepNumber: isNil(rawTaskStep.task_step)
          ? undefined
          : Number(rawTaskStep.task_step),
        taskStepStatus: rawTaskStep.task_step_state_text,
        taskStepStatusId: Number(rawTaskStep.task_step_state_id),
        taskStepText: rawTaskStep.task_step_text,
        taskStepTypeId: rawTaskStep.task_step_type_id,
        taskStepType: rawTaskStep.task_step_type_text,
        isSameTaskOrderId: rawTaskStep.order_id === task.orderId,
      }
    })

  return { task, taskSteps }
}

function parseTasks(rawTasks: FixMeAny) {
  const tasks = rawTasks.map((rawTask: FixMeAny) =>
    Object.assign(parseTask(rawTask), {
      lastUpdatedTs: rawTask.last_updated_at
        ? dateTimeToLocalTimestamp(rawTask.last_updated_at)
        : null,
    }),
  )

  return tasks
}

function normalizeRepeatObject(task: FixMeAny) {
  return task.repeatId === 0
    ? null
    : {
        repeatId: task.repeatId,
        repeatStartDate: task.repeatStartDate,
        repeatEndType: task.repeatEndType,
        repeatEndDate: task.repeatEndType === 'by' ? task.repeatEndDate : null,
        repeatCounter: task.repeatEndType === 'after' ? task.repeatCounter : 0,
        existingRepeatDates: task.isSameRecurrence ? task.subTasks : [],
      }
}

function normalizeRepeatingTime(time: FixMeAny, date: FixMeAny) {
  const hours = moment(time).hours()
  const minutes = moment(time).minutes()
  const repeatingTime = moment(date).set('hours', hours).set('minutes', minutes)

  return repeatingTime
}

function normalizeTask(originalTask: FixMeAny, taskSteps: FixMeAny) {
  const task = clone(originalTask)
  task.taskTypeId = task.taskTypeId ? Number(task.taskTypeId) : null

  const flatTaskSteps = flatMap(taskSteps)
  const firstStep: FixMeAny = minBy(
    values(flatTaskSteps),
    (step: FixMeAny) => step.taskStepNumber,
  )
  const lastStep: FixMeAny = maxBy(
    values(flatTaskSteps),
    (step: FixMeAny) => step.taskStepNumber,
  )

  // Start Time
  task.scheduledStartTime = task.stepsInOrder
    ? firstStep.taskStepStartTime
    : task.scheduledStartTime

  // End Time
  task.scheduledEndTime = task.stepsInOrder
    ? lastStep.taskStepEndTime
    : task.scheduledEndTime

  return task
}

function normalizeTaskSteps(task: FixMeAny, originalTaskSteps: FixMeAny) {
  const taskSteps: Array<FixMeAny> = []

  originalTaskSteps.forEach((originalTaskStep: FixMeAny) => {
    const taskStep = clone(originalTaskStep)
    taskStep.taskStepTypeId = taskStep.taskStepTypeId
      ? Number(taskStep.taskStepTypeId)
      : null

    // Customer
    taskStep.lat = taskStep.lat || null
    taskStep.lon = taskStep.lon || null
    taskStep.country = taskStep.country || ''
    taskStep.province = taskStep.province || ''
    taskStep.postalCode = taskStep.postalCode || ''
    taskStep.suburb = taskStep.suburb || ''
    taskStep.unitNumber = taskStep.unitNumber || ''

    const customerDetails = {
      contactEmail: taskStep.contactEmail,
      contactEmails: taskStep.contactEmails,
      contactNumber: taskStep.contactNumber,
      contactNumbers: taskStep.contactNumbers,
      contactPerson: taskStep.contactPerson,
      country: taskStep.country,
      customerName: taskStep.customerName,
      customerRefId: taskStep.customerRefId,
      lat: taskStep.lat,
      lon: taskStep.lon,
      notificationGroupId: taskStep.notificationGroupId,
      province: taskStep.province,
      postalCode: taskStep.postalCode,
      suburb: taskStep.suburb,
      unitNumber: taskStep.unitNumber,
    }

    const currentCustomer = {
      ...taskStep.originalCustomer,
      ...customerDetails,
    }

    taskStep.isCustomerModified = !isEqual(taskStep.originalCustomer, currentCustomer)

    delete taskStep.originalCustomer
    const taskStepCustomer = customerDetails

    // Task Step Proof
    taskStep.requiredSignature =
      taskStep.requiredSignature === undefined ? false : taskStep.requiredSignature
    taskStep.requiredPicture =
      taskStep.requiredPicture === undefined ? false : taskStep.requiredPicture

    taskStep.requiredPictureNo = taskStep.requiredPicture
      ? Number(taskStep.requiredPictureNo) || 1
      : null

    // Task Step Schedule
    taskStep.taskStepDateTime = task.scheduledArrivalTs

    // Start Time
    taskStep.taskStepStartTime = task.stepsInOrder
      ? taskStep.taskStepStartTime
      : task.scheduledStartTime

    // End Time
    taskStep.taskStepEndTime = task.stepsInOrder
      ? taskStep.taskStepEndTime
      : task.scheduledEndTime

    // Service Costs
    const originalServiceCosts = values(taskStep.serviceCosts).filter(
      (cost) => !cost.isEditing && !cost.isTemp,
    )
    taskStep.serviceCosts = {
      new: originalServiceCosts
        ? originalServiceCosts.filter((cost) => cost.id.includes('new'))
        : [],
      existing: originalServiceCosts
        ? originalServiceCosts.filter((cost) => !cost.id.includes('new'))
        : [],
    }

    delete taskStep.photos

    taskSteps.push({ ...taskStep, ...taskStepCustomer })
  })

  return taskSteps
}

function parseTasksTimeline(rawTasks: FixMeAny) {
  const { users, tasks, steps } = rawTasks

  const parsedTasks = tasks.map((task: FixMeAny) => {
    const step = steps.filter((step: FixMeAny) => step.taskId === task.taskId)
    return {
      ...task,
      taskSteps: step,
    }
  })

  const parsedUsers = users
    .map((user: FixMeAny) => {
      const tasks = parsedTasks.filter(
        (task: FixMeAny) => task.communicatorUserId === user.communicatorUserId,
      )

      return {
        ...user,
        tasks,
      }
    })
    .sort((a: FixMeAny, b: FixMeAny) => b.tasks.length - a.tasks.length)

  return {
    users: parsedUsers,
    totalTasksCount: rawTasks['total_tasks_count'],
  }
}

export default {
  fetchTasks(params: FixMeAny) {
    return apiCaller(
      'communicator_get_tasks',
      { data: params },
      {
        noX: true,
      },
    ).then((res) => ({
      allTaskID: res.communicator_all_task_id,
      tasks: parseTasks(res.communicator_get_tasks),
      lateStartTaskSteps: res.communicator_get_step_notifications,
      recentlyUpdatedTaskSteps: res.communicator_get_recently_updated_task_step_state,
      recentTaskStepUploads: res.communicator_get_recent_task_step_uploads,
      totalPages: res.communicator_total_page_count,
      totalRejectedTasks: res.communicator_total_rejected_tasks_count,
      totalTasksCount: res.communicator_total_tasks_count,
      overallTaskCount: res.communicator_overall_tasks_count,
    }))
  },

  fetchTasksTimeline(params: FixMeAny) {
    return apiCaller(
      'communicator_get_tasks_timeline',
      { data: params },
      { noX: true },
    ).then((res) => parseTasksTimeline(res.communicator_get_tasks_timeline))
  },

  fetchTaskDetails(taskId: string | number) {
    return apiCaller('communicator_get_task_details', { taskId }, { noX: true }).then(
      (res) => parseTaskDetails(res),
    )
  },

  fetchTaskRepeats() {
    return apiCaller('communicator_get_task_repeat_types').then((res) => {
      const taskRepeats = res.communicator_get_task_repeat_types.map(
        (repeat: Record<string, any>) => ({
          id: Number(repeat.task_repeat_id),
          description: repeat.description,
        }),
      )
      return { taskRepeats }
    })
  },

  createTask(task: FixMeAny, taskSteps: FixMeAny) {
    const normalizedTask = normalizeTask(task, taskSteps)
    const normalizedTaskSteps = normalizeTaskSteps(task, taskSteps)

    return apiCaller(
      'communicator_create_task',
      {
        task: normalizedTask,
        taskSteps: normalizedTaskSteps,
      },
      {
        noX: true,
      },
    )
  },

  createTaskGroup(task: FixMeAny, taskSteps: FixMeAny) {
    const repeatObj = normalizeRepeatObject(task)
    const normalizedTaskGroup = {
      ...normalizeTask(task, taskSteps),
      ...repeatObj,
    }
    const normalizedTaskSteps = normalizeTaskSteps(task, taskSteps)
    const taskRepeatDates = getTaskRepeatDates(repeatObj).map((date: FixMeAny) => ({
      scheduledArrivalTs: date,
      scheduledStartTime: normalizeRepeatingTime(
        normalizedTaskGroup.scheduledStartTime,
        date,
      ),
      scheduledEndTime: normalizeRepeatingTime(
        normalizedTaskGroup.scheduledEndTime,
        date,
      ),
      taskStepStartTime: normalizedTaskSteps.map((taskStep) =>
        normalizeRepeatingTime(taskStep.taskStepStartTime, date),
      ),
      taskStepEndTime: normalizedTaskSteps.map((taskStep) =>
        normalizeRepeatingTime(taskStep.taskStepEndTime, date),
      ),
    }))

    return apiCaller(
      'communicator_create_task_group',
      {
        normalizedTaskGroup,
        normalizedTaskSteps,
        taskRepeatDates,
      },
      { noX: true },
    )
  },

  updateTask(task: FixMeAny, taskSteps: FixMeAny) {
    const normalizedTask = normalizeTask(task, taskSteps)
    const normalizedTaskSteps = {
      added: normalizeTaskSteps(task, taskSteps.added),
      updated: normalizeTaskSteps(task, taskSteps.updated),
      deleted: taskSteps.deleted,
    }

    return apiCaller(
      'communicator_update_task',
      {
        task: normalizedTask,
        taskSteps: normalizedTaskSteps,
      },
      { noX: true },
    )
  },

  updateTaskGroup(task: FixMeAny, taskSteps: FixMeAny) {
    const repeatObj = normalizeRepeatObject(task)
    const normalizedTaskGroup = {
      ...normalizeTask(task, taskSteps),
      ...repeatObj,
    }
    const normalizedTaskSteps = {
      added: normalizeTaskSteps(task, taskSteps.added),
      updated: normalizeTaskSteps(task, taskSteps.updated),
      deleted: taskSteps.deleted,
    }
    const taskRepeatDates = getTaskRepeatDates(repeatObj).map((date: FixMeAny) => ({
      scheduledArrivalTs: date,
      scheduledStartTime: normalizeRepeatingTime(
        normalizedTaskGroup.scheduledStartTime,
        date,
      ),
      scheduledEndTime: normalizeRepeatingTime(
        normalizedTaskGroup.scheduledEndTime,
        date,
      ),
      added: {
        taskStepStartTime: normalizedTaskSteps.added.map((taskStep) =>
          normalizeRepeatingTime(taskStep.taskStepStartTime, date),
        ),
        taskStepEndTime: normalizedTaskSteps.added.map((taskStep) =>
          normalizeRepeatingTime(taskStep.taskStepEndTime, date),
        ),
      },
      updated: {
        taskStepStartTime: normalizedTaskSteps.updated.map((taskStep) =>
          normalizeRepeatingTime(taskStep.taskStepStartTime, date),
        ),
        taskStepEndTime: normalizedTaskSteps.updated.map((taskStep) =>
          normalizeRepeatingTime(taskStep.taskStepEndTime, date),
        ),
      },
    }))

    return apiCaller(
      'communicator_update_task_group',
      {
        normalizedTaskGroup,
        normalizedTaskSteps,
        taskRepeatDates,
      },
      {
        noX: true,
      },
    )
  },

  updateTaskStepNotification(taskSteps: FixMeAny) {
    return apiCaller('communicator_update_task_step_notification', {
      taskSteps,
    })
  },

  updateTaskUser(taskId: string | number, communicatorUser: FixMeAny) {
    return apiCaller('communicator_update_task_user', {
      taskId,
      communicatorUser,
    })
  },

  deleteTask(taskId: string | number) {
    return apiCaller('communicator_delete_task', { taskId }, { noX: true })
  },

  deleteTaskGroup(data: Record<string, any>) {
    const { taskGroupId, taskId } = data
    return apiCaller(
      'communicator_delete_task_group',
      {
        taskId,
        taskGroupId,
      },
      { noX: true },
    )
  },

  fetchDownloadTaskAvailability(data: FixMeAny) {
    return apiCaller('communicator_get_tasks_count', { data }).then((res) =>
      parseDownloadCount(res),
    )
  },

  downloadTaskSheet(data: FixMeAny) {
    const {
      filters: { endDate, startDate },
      type,
    } = data

    apiCaller('communicator_download_tasks', { data }, { noParse: true })
      .then((res) => res.blob())
      .then((result) =>
        download(result, `${type}-tasks-${startDate}-to-${endDate}.xls`),
      )
  },

  reassignTasks(data: FixMeAny) {
    return apiCaller('communicator_reassign_tasks', { data })
  },
}
