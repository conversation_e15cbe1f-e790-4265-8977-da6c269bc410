import moment from 'moment-timezone'
import { defaults, identity, isEmpty } from 'lodash'

import type { FixMeAny } from 'src/types'

export function getTaskRepeatDates(repeatObj: FixMeAny) {
  if (repeatObj.existingRepeatDates && repeatObj.existingRepeatDates.length > 0) {
    return repeatObj.existingRepeatDates.map((date: FixMeAny) => date.startDate)
  }

  let repeatOptions
  switch (repeatObj.repeatId) {
    case 1: {
      repeatOptions = {
        increment: 1,
        incrementType: 'weeks',
        includeCurrentDay: true,
      }
      break
    }

    case 2: {
      repeatOptions = {
        increment: 1,
        incrementType: 'months',
        includeCurrentDay: true,
      }
      break
    }

    case 3: {
      repeatOptions = {
        increment: 1,
        incrementType: 'years',
        includeCurrentDay: true,
      }
      break
    }

    case 4: {
      repeatOptions = {
        increment: 1,
        incrementType: 'days',
        excludeWeekends: true,
        includeCurrentDay: true,
      }
      break
    }

    case 5: {
      repeatOptions = {
        increment: 1,
        incrementType: 'days',
        includeCurrentDay: true,
      }
      break
    }

    default: {
      repeatOptions = {}
    }
  }

  return getAllDates(repeatObj, repeatOptions)
}

function getAllDates(repeatObj: FixMeAny, repeatOptions: FixMeAny) {
  const { repeatStartDate, repeatEndDate, repeatCounter } = repeatObj

  let allDatesInRange: FixMeAny = []
  if (isEmpty(repeatOptions)) {
    allDatesInRange.push(new Date(repeatStartDate))
  } else if (!isEmpty(repeatOptions) && repeatEndDate) {
    allDatesInRange = getAllDatesInRange({
      begin: moment(repeatStartDate),
      end: moment(repeatEndDate),
      options: repeatOptions,
    })
  } else if (!isEmpty(repeatOptions) && repeatCounter) {
    allDatesInRange = getAllDatesByCount({
      begin: moment(repeatStartDate),
      count: repeatCounter,
      options: repeatOptions,
    })
  }

  return allDatesInRange
}

function isWeekend(date: FixMeAny) {
  return date.isoWeekday() === 6 || date.isoWeekday() === 7
}

function getAllDatesInRange(props: FixMeAny) {
  let { begin, end, options } = props
  begin = options.includeCurrentDay
    ? begin
    : begin.add(options.increment, options.incrementType)

  options = defaults(options, {
    transformPreAdd: identity,
    onIncrement: (date: FixMeAny) => date.add(options.increment, options.incrementType),
  })

  const dates = []
  while (begin.diff(end) <= 0) {
    if (!(options.excludeWeekends && isWeekend(begin))) {
      dates.push(new Date(begin))
    }

    begin = options.onIncrement(begin)
  }

  return dates
}

function getAllDatesByCount(props: FixMeAny) {
  let { begin, count, options } = props
  begin = options.includeCurrentDay
    ? begin
    : begin.add(options.increment, options.incrementType)

  options = defaults(options, {
    transformPreAdd: identity,
    onIncrement: (date: FixMeAny) => date.add(options.increment, options.incrementType),
  })

  const dates = []
  let i = 0
  while (i < count) {
    if (!(options.excludeWeekends && isWeekend(begin))) {
      dates.push(new Date(begin))
      i++
    }

    begin = options.onIncrement(begin)
  }

  return dates
}
