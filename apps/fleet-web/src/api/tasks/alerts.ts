import apiCaller from '../api-caller'

import { formatDate } from 'cartrack-moment-utils'
import type { FixMeAny } from 'src/types'

function parseTaskAlerts(rawTaskAlerts: FixMeAny) {
  return {
    allTaskActivityId: rawTaskAlerts.all_task_activity_id,
    taskNotifications: rawTaskAlerts.task_notifications.map((task: FixMeAny) => ({
      activityId: task.task_activity_id,
      date: formatDate(task.created_at, 'YYYY-MM-DD HH:mm'),
      description: task.task_description,
      isDeleted: task.is_task_deleted === '1',
      isRead: task.is_read === '1',
      taskId: task.task_id,
      taskType: task.type,
      users: task.users,
    })),
    totalCount: rawTaskAlerts.total_count,
    totalPageCount: rawTaskAlerts.total_page_count,
  }
}

export default {
  fetchTaskAlerts(data: FixMeAny) {
    return apiCaller('communicator_get_task_notifications', {
      data,
    }).then((res) => parseTaskAlerts(res))
  },

  updateTaskAlertsAsRead(taskActivityIds: FixMeAny) {
    return apiCaller('communicator_mark_read_task_notifications', {
      taskActivityIds,
    }).then((res) => res)
  },

  updateTaskAlertsAsDismissed(taskActivityIds: FixMeAny) {
    return apiCaller('communicator_dismiss_task_notifications', {
      taskActivityIds,
    }).then((res) => res)
  },
}
