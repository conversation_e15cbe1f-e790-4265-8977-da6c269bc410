import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'

export default {
  fetchTaskSuspendReasons() {
    return apiCaller('communicator_get_task_suspend_reason', {
      data: {
        sorted: {
          id: 'id',
          desc: true,
        },
      },
    }).then((res) => res)
  },

  createTaskSuspendReason(rejectReason: FixMeAny) {
    const { description, name, status, validStatuses } = rejectReason
    return apiCaller('communicator_create_task_suspend_reason', {
      reason: {
        description,
        name,
        status,
        validStatuses,
      },
    })
  },

  updateTaskSuspendReason(rejectReason: FixMeAny) {
    return apiCaller('communicator_update_task_suspend_reason', {
      reason: {
        ...rejectReason,
      },
    })
  },

  deleteTaskSuspendReason(suspendReasonId: string | number) {
    return apiCaller('communicator_delete_task_suspend_reason', {
      reason: {
        suspendReasonId,
      },
    })
  },
}
