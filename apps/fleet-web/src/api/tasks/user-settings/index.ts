import { normalizeKarooUiThemeSetting, parseKarooUiThemeSetting } from 'api/user/utils'
import apiCaller from 'src/api/api-caller'
import type { PromiseResolvedType } from 'src/types'
import type { FixMeAny } from 'src/types'

import type { CommunicatorGetFleetUserSettingsTypes, UpdateUserTheme } from './types'

type userSettingsType =
  CommunicatorGetFleetUserSettingsTypes.ApiOutput['communicator_get_fleet_user_settings']

function parseUserSettings(rawSettings: Array<userSettingsType>) {
  const userSettings: FixMeAny = {}
  rawSettings.forEach((s: userSettingsType) => {
    userSettings[s.settings_key] = parseValue(s)
  })
  return { userSettings }
}

function parseValue(s: userSettingsType) {
  const type = s.settings_type.toLowerCase()
  switch (type) {
    case 'boolean':
      return s.settings_value.toLowerCase() === 'true'
    case 'number':
      return Number(s.settings_value)
    default:
      return s.settings_value
  }
}

const api = {
  fetchUserSettings() {
    return apiCaller('communicator_get_fleet_user_settings').then((res) =>
      parseUserSettings(res.communicator_get_fleet_user_settings),
    )
  },
  updateUserSetting(setting: FixMeAny) {
    return apiCaller('communicator_update_fleet_user_setting', { setting })
  },
  updateTheme(input: UpdateUserTheme.ApiInput) {
    const normalizedInput: UpdateUserTheme.NormalizedApiInput = {
      ...input,
      data: {
        ...input.data,
        karooUiTheme: normalizeKarooUiThemeSetting(input.data.karooUiTheme),
      },
    }
    return apiCaller('ct_style_changer', normalizedInput, { noX: true }).then(
      (res: UpdateUserTheme.ApiOutput) => ({
        ...res,
        karooUiTheme: parseKarooUiThemeSetting(res.karooUiTheme),
      }),
    )
  },
}

export default api

export type FetchUserSettings = PromiseResolvedType<typeof api.fetchUserSettings>
