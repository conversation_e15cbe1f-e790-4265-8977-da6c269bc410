import type { KarooUiCustomizableTheme } from '@karoo-ui/core'
import type { StringifiedKarooUiCustomizableTheme } from 'api/user/utils'

export declare namespace CommunicatorGetFleetUserSettingsTypes {
  type ApiOutput = {
    communicator_get_fleet_user_settings: {
      settings_key: string
      settings_type: string
      settings_value: string
    }
  }
}

export declare namespace UpdateUserTheme {
  type ApiInput = {
    data: Record<string, any> & {
      karooUiTheme: KarooUiCustomizableTheme | 'DEFAULT'
    }
    password: string
  }

  type NormalizedApiInput = {
    data: Record<string, any> & {
      karooUiTheme: StringifiedKarooUiCustomizableTheme
    }
    password: string
  }

  type ApiOutput = Record<string, any> & {
    karooUiTheme: StringifiedKarooUiCustomizableTheme | null | undefined
  }
}
