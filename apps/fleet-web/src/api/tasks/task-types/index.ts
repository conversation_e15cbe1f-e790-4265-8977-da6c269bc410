import apiCaller from 'src/api/api-caller'
import type { FixMeAny, PromiseResolvedType } from 'src/types'
import type { CommunicatorGetTaskTypes } from './types'
import { isTrue } from 'cartrack-utils'

function parseTaskTypes(
  rawTaskTypes: CommunicatorGetTaskTypes.ApiOutput['communicator_get_task_types'],
) {
  const taskTypes = rawTaskTypes.map((type) => ({
    id: type.task_type_id,
    description: type.description,
    isProtected: isTrue(type.is_protected),
  }))
  return { taskTypes }
}

const api = {
  fetchTaskTypes() {
    return apiCaller('communicator_get_task_types', {}, { noX: true }).then(
      (res: CommunicatorGetTaskTypes.ApiOutput) =>
        parseTaskTypes(res.communicator_get_task_types),
    )
  },

  createTaskType(description: string) {
    return apiCaller(
      'communicator_add_task_type',
      {
        taskType: {
          description,
        },
      },
      { noX: true },
    )
  },

  updateTaskType(description: string, id: string) {
    return apiCaller(
      'communicator_update_task_type',
      {
        taskType: { description, id },
      },
      { noX: true },
    )
  },

  deleteTaskType(taskType: FixMeAny) {
    return apiCaller('communicator_delete_task_type', { taskType }, { noX: true })
  },
}

export default api

export type FetchTaskTypes = PromiseResolvedType<typeof api.fetchTaskTypes>
