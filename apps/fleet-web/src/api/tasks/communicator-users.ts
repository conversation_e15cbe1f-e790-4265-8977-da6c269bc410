import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'

function parseCommunicatorUsers(rawCommunicatorUsers: FixMeAny) {
  const communicatorUsers = rawCommunicatorUsers.map((user: FixMeAny) => {
    const firstName = user.out_first_name ? user.out_first_name : ''
    const lastName = user.out_last_name ? user.out_last_name : ''
    const name = `${firstName} ${lastName}`.trim()
    const userName = user.out_user_name

    return {
      id: user.out_communicator_user_id,
      driverId: user.out_client_driver_id,
      driverName: user.out_client_driver_name,
      enabled: user.out_enabled === 't',
      email: user.out_email_address,
      firstName,
      lastName,
      mobileDeviceId: user.out_mobile_device_id,
      mobileDeviceImei: user.out_mobile_device_imei,
      mobileDeviceName: user.out_mobile_device_description,
      mobileNumber: user.out_mobile_number,
      name: name === '' ? userName : name,
      userName,
      vehicleId: user.out_vehicle_id,
      vehicleRegistration: user.out_vehicle_registration,
      currentPasswordHash: user.out_password_hash,
    }
  })

  return { communicatorUsers }
}

export default {
  fetchCommunicatorUsers() {
    return apiCaller('communicator_get_communicator_users').then((res) =>
      parseCommunicatorUsers(res.communicator_get_communicator_users),
    )
  },

  createCommunicatorUser(communicatorUser: FixMeAny) {
    return apiCaller('communicator_create_communicator_user', {
      communicatorUser,
    }).then((res) => res)
  },

  updateCommunicatorUser(communicatorUser: FixMeAny) {
    return apiCaller('communicator_update_communicator_user', {
      communicatorUser,
    }).then((res) => res.communicator_update_communicator_user.out_message)
  },

  deleteCommunicatorUser(communicatorUserId: number | string) {
    return apiCaller('communicator_delete_communicator_user', {
      communicatorUserId,
    })
  },
}
