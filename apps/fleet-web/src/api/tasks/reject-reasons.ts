import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'

export default {
  fetchRejectReasons() {
    return apiCaller('communicator_get_task_reject_reason', {
      data: {
        sorted: {
          id: 'id',
          desc: true,
        },
      },
    }).then((res) => res)
  },

  createRejectReason(rejectReason: FixMeAny) {
    const { description, name, status, validStatuses } = rejectReason
    return apiCaller('communicator_create_task_reject_reason', {
      reason: {
        description,
        name,
        status,
        validStatuses,
      },
    })
  },

  updateRejectReason(rejectReason: FixMeAny) {
    const { id, name, description, status, statuses, statusesObject, validStatuses } =
      rejectReason
    return apiCaller('communicator_update_task_reject_reason', {
      reason: {
        id,
        name,
        description,
        status,
        statuses,
        validStatuses: validStatuses
          ? validStatuses
          : statusesObject &&
            statusesObject.map((status: FixMeAny) => status.task_state_id),
      },
    })
  },

  deleteRejectReason(rejectReasonId: FixMeAny) {
    return apiCaller('communicator_delete_task_reject_reason', {
      reason: {
        rejectReasonId,
      },
    })
  },
}
