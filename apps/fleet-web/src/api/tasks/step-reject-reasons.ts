import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'

export default {
  fetchStepRejectReasons() {
    return apiCaller('communicator_get_task_step_reject_reason', {
      data: {
        sorted: {
          id: 'id',
          desc: true,
        },
      },
    }).then((res) => res)
  },

  createStepRejectReason(rejectReason: FixMeAny) {
    const { description, name, status, validTaskStepStatuses, validTaskStepTypes } =
      rejectReason

    return apiCaller('communicator_create_task_step_reject_reason', {
      reason: {
        name,
        description,
        validStatuses: validTaskStepStatuses,
        validStepTypes: validTaskStepTypes,
        status,
      },
    })
  },

  updateStepRejectReason(rejectReason: FixMeAny) {
    const { description, name, status, validTaskStepStatuses, validTaskStepTypes, id } =
      rejectReason

    return apiCaller('communicator_update_task_step_reject_reason', {
      reason: {
        id,
        name,
        description,
        validStatuses: validTaskStepStatuses,
        validStepTypes: validTaskStepTypes,
        status,
      },
    })
  },

  deleteStepRejectReason(rejectReasonId: FixMeAny) {
    return apiCaller('communicator_delete_task_step_reject_reason', {
      reason: {
        rejectReasonId,
      },
    })
  },
}
