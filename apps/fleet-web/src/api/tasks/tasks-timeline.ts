import apiCaller from 'src/api/api-caller'
import moment from 'moment-timezone'

function parseTasksTimelineWorkingHours(
  rawTasksTimelineWorkingHours: Record<string, any>,
) {
  let formattedClosingTime = moment(
    rawTasksTimelineWorkingHours.closing_time,
    'HH:mm:ss',
  ).hours()
  formattedClosingTime = formattedClosingTime <= 0 ? 24 : formattedClosingTime
  return {
    tasksTimelineStartTime: moment(
      rawTasksTimelineWorkingHours.opening_time,
      'HH:mm:ss',
    ).hours(),
    tasksTimelineClosingTime: formattedClosingTime,
  }
}

export default {
  fetchTasksTimelineWorkingHours() {
    return apiCaller('communicator_get_working_hours', {}, { noX: true }).then(
      (res) => ({
        tasksTimelineWorkingHours: parseTasksTimelineWorkingHours(res),
      }),
    )
  },

  updateTasksTimelineWorkingHours(taskTimelineWorkingHours: Array<string>) {
    return apiCaller(
      'communicator_set_working_hours',
      { ...taskTimelineWorkingHours },
      { noX: true },
    )
  },
}
