import apiCaller from 'src/api/api-caller'
import type { FixMeAny, PromiseResolvedType } from 'src/types'
import type { CommunicatorGetTaskStepTypes } from './types'
import { isTrue } from 'cartrack-utils'

function parseTaskStepTypes(
  rawTaskStepTypes: CommunicatorGetTaskStepTypes.ApiOutput['communicator_get_task_step_types'],
) {
  const taskStepTypes = rawTaskStepTypes.map((type) => ({
    id: type.task_step_type_id,
    description: type.description,
    isProtected: isTrue(type.is_protected),
  }))
  return { taskStepTypes }
}

const api = {
  fetchTaskStepTypes() {
    return apiCaller('communicator_get_task_step_types', {}, { noX: true }).then(
      (res) => parseTaskStepTypes(res.communicator_get_task_step_types),
    )
  },

  createTaskStepType(description: string) {
    return apiCaller(
      'communicator_add_task_step_type',
      {
        taskStepType: { description },
      },
      { noX: true },
    )
  },

  updateTaskStepType(description: string, id: string) {
    return apiCaller(
      'communicator_update_task_step_type',
      {
        taskStepType: { description, id },
      },
      { noX: true },
    )
  },

  deleteTaskStepType(taskType: FixMeAny) {
    return apiCaller('communicator_delete_task_step_type', { taskType }, { noX: true })
  },
}

export default api

export type FetchTaskStepTypes = PromiseResolvedType<typeof api.fetchTaskStepTypes>
