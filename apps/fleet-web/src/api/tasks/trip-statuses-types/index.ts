import apiCaller from 'src/api/api-caller'
import type { PromiseResolvedType } from 'src/types'
import type { CommunicatorGetTripStatusesTypes } from './types'
import { isTrue } from 'cartrack-utils'

function parseTripStatusesTypes(
  rawTripStatusesTypes: CommunicatorGetTripStatusesTypes.ApiOutput['client_trip_types'],
) {
  const tripStatusesTypes = rawTripStatusesTypes.map((type) => ({
    id: type.client_trip_type_custom_id,
    description: type.trip_description,
    isProtected: isTrue(type.is_protected),
  }))
  return { tripStatusesTypes }
}

const api = {
  fetchTripStatusesTypes() {
    return apiCaller(
      'communicator_get_client_trip_type',
      { data: {} },
      { noX: true },
    ).then((res: CommunicatorGetTripStatusesTypes.ApiOutput) =>
      parseTripStatusesTypes(res.client_trip_types),
    )
  },
  createTripStatusesType(description: string) {
    return apiCaller(
      'communicator_add_client_trip_type',
      {
        tripStatusesTypes: {
          description,
        },
      },
      { noX: true },
    )
  },
  updateTripStatusesType(description: string, id: string) {
    return apiCaller(
      'communicator_update_client_trip_type',
      {
        tripStatusesTypes: { description, client_trip_type_custom_id: id },
      },
      { noX: true },
    )
  },
  deleteTripStatusesType(client_trip_type_custom_id: string) {
    return apiCaller(
      'communicator_delete_client_trip_type',
      {
        tripStatusesTypes: { client_trip_type_custom_id },
      },
      { noX: true },
    )
  },
}

export default api

export type FetchTripStatusesTypes = PromiseResolvedType<
  typeof api.fetchTripStatusesTypes
>
