import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'

export default {
  fetchStepSuspendReasons() {
    return apiCaller('communicator_get_task_step_suspend_reason', {
      data: {
        sorted: {
          id: 'id',
          desc: true,
        },
      },
    }).then((res) => res)
  },

  createStepSuspendReason(stepSuspendReason: FixMeAny) {
    const { description, name, status, validTaskStepStatuses, validTaskStepTypes } =
      stepSuspendReason

    return apiCaller('communicator_create_task_step_suspend_reason', {
      reason: {
        name,
        description,
        validStatuses: validTaskStepStatuses,
        validStepTypes: validTaskStepTypes,
        status,
      },
    })
  },

  updateStepSuspendReason(stepSuspendReason: FixMeAny) {
    const { description, name, status, validTaskStepStatuses, validTaskStepTypes, id } =
      stepSuspendReason

    return apiCaller('communicator_update_task_step_suspend_reason', {
      reason: {
        id,
        name,
        description,
        validStatuses: validTaskStepStatuses,
        validStepTypes: validTaskStepTypes,
        status,
      },
    })
  },

  deleteStepSuspendReason(suspendReasonId: number | string) {
    return apiCaller('communicator_delete_task_step_suspend_reason', {
      reason: {
        suspendReasonId,
      },
    })
  },
}
