import download from 'downloadjs'
import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'
import { isTrue } from 'cartrack-utils'

export function parseStockItems(rawStockItems: FixMeAny) {
  const stockItems = rawStockItems.map((stockItem: FixMeAny) => ({
    id: stockItem.stock_item_id,
    barcode: stockItem.stock_code,
    description: stockItem.description,
    state: {
      id: stockItem.item_state_id,
      name: stockItem.item_status,
    },
    type: {
      id: stockItem.item_type_id,
      name: stockItem.item_type,
    },
    quantity: Number(stockItem.quantity),
    taskStepId: stockItem.task_step_id,
  }))

  return stockItems ? stockItems : null
}

function parseStockItemStates(rawStockItemStates: FixMeAny) {
  const stockItemStates = rawStockItemStates.map((stockItemState: FixMeAny) => ({
    id: stockItemState.stock_item_state_id,
    description: stockItemState.description,
    defaultCode: stockItemState.default_code,
  }))

  return { stockItemStates }
}

function parseStockItemTypes(rawStockItemTypes: FixMeAny) {
  const stockItemTypes = rawStockItemTypes.map((stockItemType: FixMeAny) => ({
    id: stockItemType.stock_item_type_id,
    description: stockItemType.description,
    defaultCode: stockItemType.default_code,
    isProtected: isTrue(stockItemType.is_protected),
  }))

  return { stockItemTypes }
}

export default {
  fetchStockItemStates() {
    return apiCaller('communicator_get_stock_item_states', {}, { noX: true }).then(
      (res) => parseStockItemStates(res.communicator_get_stock_item_states),
    )
  },

  fetchStockItemTypes() {
    return apiCaller(
      'communicator_get_stock_item_types',
      { data: {} },
      { noX: true },
    ).then((res) => parseStockItemTypes(res.communicator_get_stock_item_types))
  },

  createStockItemType(stockItemType: FixMeAny) {
    return apiCaller(
      'communicator_add_stock_item_type',
      { stockItemType },
      { noX: true },
    )
  },

  updateStockItemType(stockItemType: FixMeAny) {
    return apiCaller(
      'communicator_update_stock_item_type',
      { stockItemType },
      { noX: true },
    )
  },

  deleteStockItemType(stockItemType: FixMeAny) {
    return apiCaller(
      'communicator_delete_stock_item_type',
      { stockItemType },
      { noX: true },
    )
  },

  exportCodeItemTypes() {
    apiCaller(
      'communicator_download_stock_item_types',
      {},
      { noParse: true, noX: true },
    )
      .then((res) => res.blob())
      .then((result) => download(result, 'code-item-types.xls'))
  },
}
