import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'
import { isTrue } from 'cartrack-utils'

export function parseServiceCost(serviceCost: FixMeAny) {
  return {
    id: serviceCost.step_service_cost_id,
    description: serviceCost.description,
    isDeleted: serviceCost.is_deleted === 't',
    isFromMobile: serviceCost.from_mobile === 't',
    paid: serviceCost.paid === 't',
    serviceCost: Number(serviceCost.service_cost),
    serviceCostTypeId: serviceCost.service_cost_type_id,
    serviceCostTypeName: serviceCost.service_cost_type_name,
    taskStepId: serviceCost.task_step_id,
  }
}

export function parseServiceCosts(rawServiceCosts: FixMeAny) {
  const serviceCosts = rawServiceCosts.map((serviceCost: FixMeAny) =>
    parseServiceCost(serviceCost),
  )

  return serviceCosts || null
}

function parseServiceCostTypes(rawServiceCostTypes: FixMeAny) {
  const serviceCostTypes =
    rawServiceCostTypes?.map((serviceCostType: FixMeAny) => ({
      id: serviceCostType.service_cost_type_id,
      description: serviceCostType.description,
      name: serviceCostType.cost_name,
      serviceCost: serviceCostType.service_cost,
      serviceCostTax: serviceCostType.service_cost_tax,
      isProtected: isTrue(serviceCostType.is_protected),
    })) ?? null

  return { serviceCostTypes }
}

export default {
  fetchServiceCostTypes() {
    return apiCaller('communicator_get_service_cost_types', {}, { noX: true }).then(
      (res) => parseServiceCostTypes(res.communicator_get_service_cost_types),
    )
  },

  createServiceCostType(serviceCostType: FixMeAny) {
    return apiCaller(
      'communicator_add_service_cost_type',
      { serviceCostType },
      { noX: true },
    )
  },

  updateServiceCostType(serviceCostType: FixMeAny) {
    return apiCaller(
      'communicator_update_service_cost_type',
      {
        serviceCostType,
      },
      { noX: true },
    )
  },

  deleteServiceCostType(serviceCostType: FixMeAny) {
    return apiCaller(
      'communicator_delete_service_cost_type',
      {
        serviceCostType,
      },
      { noX: true },
    )
  },
}
