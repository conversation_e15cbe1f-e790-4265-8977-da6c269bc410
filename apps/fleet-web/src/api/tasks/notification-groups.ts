import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'

export default {
  fetchNotificationGroups() {
    return apiCaller(
      'communicator_get_notification_groups',
      {},
      {
        noX: true,
      },
    ).then((res) => res.communicator_get_notification_groups)
  },

  createNotificationGroup(notificationGroup: FixMeAny) {
    return apiCaller(
      'communicator_add_notification_group',
      {
        notificationGroup,
      },
      {
        noX: true,
      },
    )
  },

  updateNotificationGroup(notificationGroup: FixMeAny) {
    return apiCaller(
      'communicator_update_notification_group',
      {
        notificationGroup,
      },
      {
        noX: true,
      },
    )
  },

  deleteNotificationGroup(notificationGroupId: string | number) {
    return apiCaller(
      'communicator_delete_notification_group',
      {
        notificationGroupId,
      },
      {
        noX: true,
      },
    )
  },
}
