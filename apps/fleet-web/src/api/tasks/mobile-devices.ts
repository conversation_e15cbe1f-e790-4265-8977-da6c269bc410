import apiCaller from '../api-caller'
import moment from 'moment-timezone'
import download from 'downloadjs'
import type { FixMeAny } from 'src/types'

function parseMobileDevices(rawMobileDevices: FixMeAny) {
  const mobileDevices = rawMobileDevices.map((mobile: FixMeAny) => ({
    id: mobile.mobile_device_id,
    androidId: mobile.android_id,
    deviceDescription: mobile.device_description,
    deviceDescription2: mobile.device_description_2,
    homeGeofence: mobile.home_geofence,
    imei: mobile.imei,
    isLoggedIn: mobile.logged_in === 't',
    deviceName: mobile.device_name,
    deviceBattery: mobile.device_battery,
    trackDevice: mobile.track_device === '1',
    locations: mobile.device_locations,
  }))

  return { mobileDevices }
}

export default {
  fetchMobileDevices(params: Record<string, any>) {
    return apiCaller(
      'communicator_get_mobile_devices',
      { data: params || {} },
      {
        noX: true,
      },
    ).then((res) => ({
      ...parseMobileDevices(res.communicator_get_mobile_devices),
      totalPages: res.communicator_total_pages,
      totalMobileDevicesCount: res.communicator_total_mobile_device_count,
    }))
  },

  createMobileDevice(mobileDevice: Record<string, any>) {
    return apiCaller('communicator_add_mobile_device', { mobileDevice })
  },

  updateMobileDevice(mobileDevice: Record<string, any>) {
    return apiCaller('communicator_update_mobile_device', { mobileDevice })
  },

  updateTrackingMobileDevices(isAllTrackingEnabled: FixMeAny) {
    return apiCaller('communicator_update_all_mobile_device_tracking', {
      trackMobileDevice: isAllTrackingEnabled,
    })
  },

  deleteMobileDevice(mobileDeviceId: string | number) {
    return apiCaller('communicator_delete_mobile_device', { mobileDeviceId })
  },
  downloadMobileDevice(data: FixMeAny) {
    const date = moment().format('YYYY-MM-DD')

    apiCaller(
      'communicator_download_mobile_devices',
      { data },
      { noX: true, noParse: true },
    )
      .then((res) => res.blob())
      .then((result) => download(result, `mobile-devices-${date}`))
  },
}
