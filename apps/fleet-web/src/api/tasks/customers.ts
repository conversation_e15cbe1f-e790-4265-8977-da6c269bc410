import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'
import { isEmpty } from 'lodash'
import download from 'downloadjs'
import moment from 'moment-timezone'

function parseCustomer(customer: FixMeAny) {
  return {
    id: customer.customer_id,
    address: customer.address,
    addressLine: customer.address_line,
    contactEmail: customer.contact_email,
    // eslint-disable-next-line no-nested-ternary
    contactEmails: !isEmpty(customer.contact_emails)
      ? parseContactEmails(customer.contact_emails)
      : !isEmpty(customer.contact_email)
      ? parseContact('email', customer.contact_email)
      : null,
    emails: customer.emails ? customer.emails.replace(/,/g, ', ') : null,
    contactNumber: customer.contact_number,
    // eslint-disable-next-line no-nested-ternary
    contactNumbers: !isEmpty(customer.contact_numbers)
      ? parseContactNumbers(customer.contact_numbers)
      : !isEmpty(customer.contact_number)
      ? parseContact('phone', customer.contact_number)
      : null,
    numbers: customer.numbers ? customer.numbers.replace(/,/g, ', ') : null,
    contactPerson: customer.contact_person,
    country: customer.country,
    customerName: customer.description,
    description: customer.description, //Will use for list header display to support api attribute
    customerReferenceId: customer.customer_reference_id,
    lat: customer.lat,
    lon: customer.lon,
    notificationGroupId: customer.customer_group_notification_id,
    notificationGroupName: customer.customer_group_notification_name,
    onceOff: customer.once_off === 't',
    postalCode: customer.postal_code,
    province: customer.province,
    suburb: customer.suburb,
    unitNumber: customer.unit_number,
  }
}

export function parseContact(type: 'email' | 'phone', data: Record<string, any>) {
  let parsedData: FixMeAny = {
    isPrimary: true,
  }

  if (type === 'email') {
    parsedData = [{ ...parsedData, ...{ contactEmail: data } }]
  }

  if (type === 'phone') {
    parsedData = [{ ...parsedData, ...{ contactNumber: data } }]
  }

  return parsedData
}

export function parseContactEmails(contactEmails: Record<string, any>) {
  const parsedContactEmails = contactEmails.map(
    (contactEmail: Record<string, any>) => ({
      contactEmail: contactEmail.contact_email,
      isPrimary: contactEmail.is_primary === 't',
    }),
  )
  return parsedContactEmails
}

export function parseContactNumbers(contactNumbers: Record<string, any>) {
  const parsedContactNumbers = contactNumbers.map(
    (contactNumber: Record<string, any>) => ({
      contactNumber: contactNumber.contact_number,
      isPrimary: contactNumber.is_primary === 't',
    }),
  )
  return parsedContactNumbers
}

function parseCustomers(rawCustomers: FixMeAny) {
  const customers = rawCustomers.map((customer: FixMeAny) => ({
    ...parseCustomer(customer),
  }))

  return { customers }
}

export default {
  fetchCustomers(params: Record<string, any>) {
    return apiCaller(
      'communicator_get_all_customers',
      { data: params || {} },
      { noX: true },
    ).then((res) => ({
      ...parseCustomers(res.communicator_get_customers),
      totalPages: res.communicator_total_pages,
      totalCustomerCount: res.communicator_total_customers_count,
    }))
  },

  createCustomer(customer: Record<string, any>) {
    return apiCaller(
      'communicator_add_customer',
      {
        customer,
      },
      { noX: true },
    ).then((res) => parseCustomer(res.communicator_add_customer))
  },

  updateCustomer(customer: Record<string, any>) {
    return apiCaller(
      'communicator_update_customer',
      {
        customer,
      },
      { noX: true },
    )
  },

  deleteCustomer(customerId: number | string) {
    return apiCaller('communicator_delete_customer', { customerId }, { noX: true })
  },

  updateCustomersNotificationGroups(
    customerId: number | string,
    notificationGroupId: number | string,
  ) {
    return apiCaller('communicator_update_customer_notification_group', {
      customerId,
      notificationGroupId,
    })
  },

  validateCustomerAddress(addressType: number | string, address: string) {
    return apiCaller('communicator_get_address_details', {
      addressType,
      address,
    }).then((res) => res.communicator_get_address_details)
  },
  downloadCustomer(data: Record<string, any>) {
    const date = moment().format('YYYY-MM-DD')

    apiCaller('communicator_download_customers', { data }, { noX: true, noParse: true })
      .then((res) => res.blob())
      .then((result) => download(result, `customers-${date}`))
  },
}
