import apiCaller from '../api-caller'

export default {
  fetchTasksLookupData() {
    return apiCaller(
      'communicator_get_tasks_lookup_data',
      {},
      {
        noX: true,
      },
    ).then((res) => res.communicator_get_tasks_lookup_data)
  },

  fetchSettingsLookupData() {
    return apiCaller('communicator_get_settings_lookup_data').then(
      (res) => res.communicator_get_settings_lookup_data,
    )
  },
  fetchCommunicatorDefaultCountry() {
    return apiCaller('communicator_get_default_country', {}, { noX: true }).then(
      (res) => res.default_country,
    )
  },
}
