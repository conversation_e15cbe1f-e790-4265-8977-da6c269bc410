import apiCaller from '../api-caller'
import type { FixMeAny } from 'src/types'
import { normalizeCoordinate } from 'src/util-functions/map-utils'

function parseCustomers(rawUsers: FixMeAny) {
  return rawUsers.map((customer: FixMeAny) => ({
    contactNumber: customer.mobile_number,
    driverId: customer.client_driver_id,
    driverName: customer.driver_name,
    email: customer.email_address,
    id: customer.communicator_user_id,
    location: {
      address: customer.user_location,
      lat: normalizeCoordinate(customer.user_lat),
      lng: normalizeCoordinate(customer.user_lon),
    },
    mobileDeviceId: customer.mobile_device_id,
    mobileDeviceName: customer.mobile_device_name,
    mobileDeviceBattery: customer.mobile_device_battery,
    name: customer.first_name + ' ' + customer.last_name,
    stars: Number(customer.rating) || 0,
    taskStatus: (customer.task_state && customer.task_state.toUpperCase()) || '',
    taskCount: Number(customer.task_count),
    username: customer.user_name,
    vehicleId: customer.vehicle_id,
    vehicleRegistration: customer.vehicle_registration,
  }))
}

export default {
  fetchMapTasks(startTs: Date, endTs: Date) {
    return apiCaller('communicator_get_map_tasks', { startTs, endTs }).then(
      (res) => res.communicator_get_map_tasks,
    )
  },

  fetchMapTaskSteps(taskId: string | number) {
    return apiCaller('communicator_get_map_task_steps', { taskId }).then(
      (res) => res.communicator_get_map_task_steps,
    )
  },

  fetchMapUsers(filter: { startTs: Date; endTs: Date }) {
    const { startTs, endTs } = filter
    return apiCaller('communicator_get_map_users_tasks', {
      startTs,
      endTs,
    }).then((res) => {
      const {
        communicator_get_map_users_tasks: usersTask,
        communicator_get_map_users_tasks_summary: summary,
      } = res

      const userData = {
        users: parseCustomers(usersTask),
        stats: {
          allocatedCount: summary.allocated,
          unallocatedCount: summary.unallocated,
          usersCount: summary.total,
        },
      }
      return userData
    })
  },

  fetchUserTask(params: FixMeAny) {
    const { communicatorId, startTs, endTs } = params
    return apiCaller('communicator_get_tasks_by_communicator_user', {
      communicatorId,
      startTs,
      endTs,
    }).then((res) => res.communicator_get_tasks_by_communicator_user)
  },
}
