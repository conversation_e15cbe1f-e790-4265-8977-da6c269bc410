import { map } from 'lodash'

export const colorHash = {
  Gray: '#666666',
  Red: '#ce5239',
  Orange: '#f47735',
  Yellow: '#ffbd4d',
  Lime: '#b5d55e',
  Green: '#5cae60',
  Cyan: '#53b8c6',
  Blue: '#5390bc',
  Purple: '#a870bd',
  Pink: '#d66087',
} as const

export const colors = map(colorHash, (hex, label) => ({ hex, label }))

export const vehicleStatusColors = {
  driving: colorHash.Green,
  'ignition-off': '#999',
  'no-signal': colorHash.Gray,
  idling: colorHash.Yellow,
} as const

const oldColorsByHex = {
  // Legacy
  '0x0000FF': 'Blue',
  '0x00FF00': 'Green',
  '0xFF0000': 'Red',
  '0x80FFFF': 'Cyan',
  '0x666666': 'Gray',
  '0x808080': 'Gray',
  '0xFF8000': 'Orange',
  '0xFF0080': 'Pink',
  '0x8000FF': 'Purple',
  '0xFFCF00': 'Yellow',
  '0000ff': 'Blue',
  '00ff00': 'Green',
  ff0000: 'Red',
  '80ffff': 'Cyan',
  666666: 'Gray',
  808080: 'Gray',
  ff8000: 'Orange',
  ff0080: 'Pink',
  '8000ff': 'Purple',
  ffcf00: 'Yellow',

  // Interim Mistakes
  '#666666': 'Gray',
  '#70DAF7': 'Cyan',
  '#2DAB33': 'Green',
  '#FEC015': 'Orange',
  '#D0021B': 'Red',
  '#FF00B6': 'Pink',
  '#9013FE': 'Purple',
  '#2B8BEC': 'Blue',
  '#FFCF00': 'Yellow',

  // New Colors
  '#ce5239': 'Red',
  '#f47735': 'Orange',
  '#ffbd4d': 'Yellow',
  '#b5d55e': 'Lime',
  '#5cae60': 'Green',
  '#53b8c6': 'Cyan',
  '#5390bc': 'Blue',
  '#a870bd': 'Purple',
  '#d66087': 'Pink',
} as const

export default function parseColor(
  rawColor: string | undefined | null,
  returnName = false,
) {
  if (!rawColor) return colors[0].hex

  // Color has string name?
  if (rawColor in colorHash) {
    const rawColorTyped = rawColor as keyof typeof colorHash
    return returnName ? rawColorTyped : colorHash[rawColorTyped]
  }

  // Previously stored hex value that we cater for?
  if (rawColor in oldColorsByHex) {
    const rawColorTyped = rawColor as keyof typeof oldColorsByHex
    return returnName
      ? oldColorsByHex[rawColorTyped]
      : colorHash[oldColorsByHex[rawColorTyped]]
  }

  // Hex that is not catered for?
  if (/^(#|0x)([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})$/.test(rawColor)) {
    return rawColor
  }

  return returnName ? colors[0].label : colors[0].hex
}
