import apiCaller from '../api-caller'
import { ctIntl } from 'cartrack-ui-kit'
import { isTrue } from 'cartrack-utils'
import type { FixMeAny, PromiseResolvedType } from 'src/types'
import type { FetchAllDriverIdTags } from './types'

function parseDriverIdTags(rawDriverIdTags: Array<Record<string, FixMeAny>>) {
  return rawDriverIdTags.map((rawDriverIdTag) => ({
    id: rawDriverIdTag.client_driver_tag_id || rawDriverIdTag.identification_tag_id,
    firstSeenTs: rawDriverIdTag.first_seen_ts,
    idTag: rawDriverIdTag.identification_tag,
    lost: isTrue(rawDriverIdTag.lost),
    assignedTime: rawDriverIdTag.assigned_ts,
    driverId: rawDriverIdTag.client_driver_id,
    driverName: rawDriverIdTag.driver_name,
    description: rawDriverIdTag.client_driver_tag_description,
    unassignedTime:
      rawDriverIdTag.unassigned_ts && rawDriverIdTag.unassigned_ts === 'infinity'
        ? null
        : rawDriverIdTag.unassigned_ts,
    status: ctIntl.formatMessage({
      id: isTrue(rawDriverIdTag.lost) ? 'Lost' : 'Available',
    }),
    assign: ctIntl.formatMessage({
      id: rawDriverIdTag.client_driver_id ? 'Assign' : 'Unassign',
    }),
  }))
}

// Makes sense. But won't change it at first refactor.
// eslint-disable-next-line sonarjs/no-identical-functions
function parseAllDriverIdTags(rawDriverIdTags: Array<FetchAllDriverIdTags.Tag>) {
  return rawDriverIdTags.map((rawDriverIdTag) => ({
    id: rawDriverIdTag.client_driver_tag_id || rawDriverIdTag.identification_tag_id,
    firstSeenTs: rawDriverIdTag.first_seen_ts,
    idTag: rawDriverIdTag.identification_tag,
    lost: isTrue(rawDriverIdTag.lost),
    assignedTime: rawDriverIdTag.assigned_ts,
    driverId: rawDriverIdTag.client_driver_id,
    driverName: rawDriverIdTag.driver_name,
    description: rawDriverIdTag.client_driver_tag_description,
    unassignedTime:
      rawDriverIdTag.unassigned_ts && rawDriverIdTag.unassigned_ts === 'infinity'
        ? null
        : rawDriverIdTag.unassigned_ts,
    status: ctIntl.formatMessage({
      id: isTrue(rawDriverIdTag.lost) ? 'Lost' : 'Available',
    }),
    assign: ctIntl.formatMessage({
      id: rawDriverIdTag.client_driver_id ? 'Assign' : 'Unassign',
    }),
  }))
}

function normalizeDriverIdTags(
  driverId: string | number | null,
  driverIdTags: Array<Record<string, FixMeAny>>,
  expires?: boolean,
) {
  return expires
    ? driverIdTags.map((driverIdTag) => ({
        assigned_ts: driverIdTag.assignedDate || driverIdTag.assignedTime,
        client_driver_id: driverId ?? driverIdTag.driverId,
        client_driver_tag_description: driverIdTag.description,
        client_driver_tag_id: driverIdTag.id,
        identification_tag: driverIdTag.idTag,
        unassigned_ts: driverIdTag.expirationDate || driverIdTag.unassignedTime,
        undefined: 'on',
      }))
    : driverIdTags.map((driverIdTag) => ({
        assigned_ts: driverIdTag.assignedDate || driverIdTag.assignedTime,
        client_driver_id: driverId ?? driverIdTag.driverId,
        client_driver_tag_description: driverIdTag.description,
        client_driver_tag_id: driverIdTag.id,
        identification_tag: driverIdTag.idTag,
      }))
}

const driverIdTagsApi = {
  fetchAllDriverIdTags() {
    return apiCaller('ct_fleet_get_driver_tags').then(
      (res: FetchAllDriverIdTags.ApiOutput) => parseAllDriverIdTags(res.tags),
    )
  },

  fetchDriverIdTags(driverId: string | number) {
    const params = { client_driver_id: driverId }
    return Promise.all([
      apiCaller('ct_fleet_get_driver_unused_tags').then((res) =>
        parseDriverIdTags(res.unused_tags),
      ),
      apiCaller('ct_fleet_get_driver_used_tags').then((res) =>
        parseDriverIdTags(res.used_tags),
      ),
      apiCaller('ct_fleet_get_driver_assign_tags', params).then((res) =>
        parseDriverIdTags(res.assigned_tags),
      ),
      apiCaller('ct_fleet_get_driver_lost_tags', params).then((res) =>
        parseDriverIdTags(res.lost_tags),
      ),
    ])
  },

  updateAssignedDriverIdTags(
    driverId: string | number,
    driverIdTags: Array<Record<string, FixMeAny>>,
    expires: boolean,
  ) {
    return apiCaller('ct_fleet_update_driver_tags', {
      tags: normalizeDriverIdTags(driverId, driverIdTags, expires),
    })
  },

  assignDriverIdTags(
    driverId: string | number,
    driverIdTags: Array<Record<string, FixMeAny>>,
    expires: boolean,
  ) {
    return apiCaller('ct_fleet_add_driver_assign_tags', {
      tags: normalizeDriverIdTags(driverId, driverIdTags, expires),
    })
  },

  moveDriverIdTags(
    driverId: string | number | null,
    driverIdTag: Array<Record<string, FixMeAny>>,
    deactivate: boolean,
  ) {
    return apiCaller('ct_fleet_move_lost_driver_tags', {
      tags: normalizeDriverIdTags(driverId, driverIdTag),
      deactivate,
    }).then((res) => ({
      assignedDriverIdTags: parseDriverIdTags(res.assigned_tags.assigned_tags),
      lostDriverIdTags: parseDriverIdTags(res.lost_tags.lost_tags),
    }))
  },

  removeDriverIdTag(driverId: string | number, driverIdTag: Record<string, FixMeAny>) {
    return apiCaller('ct_fleet_delete_driver_tags', {
      tags: normalizeDriverIdTags(driverId, [driverIdTag]),
    }).then((res) => ({
      assignedDriverIdTags: parseDriverIdTags(res.assigned_tags),
    }))
  },
}

export default driverIdTagsApi

export type FetchAllDriverIdTagsResolved = PromiseResolvedType<
  typeof driverIdTagsApi.fetchAllDriverIdTags
>
