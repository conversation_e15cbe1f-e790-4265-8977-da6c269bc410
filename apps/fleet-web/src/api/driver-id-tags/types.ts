import type { BooleanALaCartrack1 } from 'src/types'

export declare namespace FetchAllDriverIdTags {
  type Tag = {
    assigned_ts: string | null
    client_driver_id: string | null
    client_driver_tag_description: string | null
    client_driver_tag_id: string | null
    driver_name: string | null
    first_seen_ts: string
    identification_tag: string
    identification_tag_id: string
    is_used_tag: BooleanALaCartrack1
    lost: BooleanALaCartrack1
    unassigned_ts: 'infinity' | string | null
  }
  type ApiOutput = {
    tags: Array<Tag>
  }
}
