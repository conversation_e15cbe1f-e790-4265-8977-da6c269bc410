import type { ContactType } from 'src/modules/alerts/utils'
import type { BooleanALaCartrack1 } from 'src/types'
import type { Opaque } from 'type-fest'

type BaseReminder = {
  reminder_group_id: string
  fk_user_id: string
  is_repeating: BooleanALaCartrack1
  repeat_interval: `${number}`
  last_triggered_ts: string
  last_triggered_value: string
  name: string
  repeat_times: null | string
  last_triggered_terminal_event_id: null
  num_times_triggered: string
  updated_ts: string
  created_ts: string
  starting_ts: string
  advance_notify_interval: string
  fk_advance_interval_type_id: string
  is_mandatory: BooleanALaCartrack1
  fk_client_user_id: null | string
  fk_advance_interval_type_id_2: null
  fk_interval_type_id_2: null
  repeat_interval_2: null
  advance_notify_interval_2: null
  last_triggered_value_2: string
  overdue_notification_sent: BooleanALaCartrack1
  expire_ts: null | string
  notification_contact: Array<string>
  any_vehicle: BooleanALaCartrack1
  any_driver: BooleanALaCartrack1
  start_from_zero: boolean
  reminders_id: string
  vehicles: null | string
  drivers: null | string
  vehicle_group_name: null
  driver_group_name: null
  last_triggered_values: Array<string>
  last_triggered_values_2: Array<string>
  valid_until_value: string
  reminder_type_id: '1' | '2' | '3'
  interval_type_id: string
}

export declare namespace ct_fleet_get_fleet_reminders {
  type ApiOutput = {
    ct_fleet_get_fleet_reminders: Array<
      BaseReminder & {
        advance_notification_sent: BooleanALaCartrack1
        current_value: string
        description: string
        difference_value: `${number}`
        drivers_id: Array<string>
        vehicles_id: Array<string>
      }
    >
  }
}

export declare namespace ct_fleet_get_reminders {
  type Reminder = BaseReminder & {
    drivers_id: string
    failed_reason: string | null
    registration: string | null
    vehicles_id: string
    sent_ts: string | null
    reminder_contact_type_id: ContactType
    sent_message: string | null
    vehicle_id: string
  }

  type ApiOutput = {
    ct_fleet_get_reminders: Array<Reminder>
    ct_fleet_get_fleet_reminders: Array<Reminder>
  }
}

export declare namespace GetRemindersList {
  type ReminderIntervalTypeId = Opaque<string, 'ReminderIntervalTypeId'>
  type ReminderTypeId = Opaque<string, 'ReminderTypeId'>

  type CTFleetGetFleetRemindersClientDriver = {
    client_driver_id: string
    driver_name: string
    cell_number: null | string
    id_number: null | string
    license_code: null
    client_driver_statuses: string
  }

  type CTFleetGetFleetRemindersGroupDriver = {
    group_driver_id: string
    name: string
    description: string
    user_id: string
    client_user_id: null | string
    group_driver_link_id: string
    client_driver_id: string
  }

  type CTFleetGetFleetRemindersGroupVehicle = {
    group_vehicle_id: string
    name: string
    description: null | string
    user_id: string
    client_user_id: null | string
    group_vehicle_link_id: string
    vehicle_id: string
  }

  type CTFleetGetFleetRemindersType = {
    reminder_type_id: ReminderTypeId
    type_name:
      | 'REMINDER.TYPE.VEHICLE_CLOCK'
      | 'REMINDER.TYPE.VEHICLE_ODOMETER'
      | 'REMINDER.TYPE.WORKTIME'
  }

  type CTFleetGetFleetRemindersIntervalType = {
    reminder_interval_type_id: ReminderIntervalTypeId
    type_name:
      | 'REMINDER.TYPE.VEHICLE_CLOCK.INTERVAL_TYPE.HOURS'
      | 'REMINDER.TYPE.VEHICLE_CLOCK.INTERVAL_TYPE.DAYS'
      | 'REMINDER.TYPE.VEHICLE_CLOCK.INTERVAL_TYPE.WEEKS'
      | 'REMINDER.TYPE.VEHICLE_CLOCK.INTERVAL_TYPE.MONTHS'
      | 'REMINDER.TYPE.VEHICLE_CLOCK.INTERVAL_TYPE.YEARS'
      | 'REMINDER.TYPE.VEHICLE_ODOMETER.INTERVAL_TYPE.METRIC_UNIT'
      | 'REMINDER.TYPE.VEHICLE_ODOMETER.INTERVAL_TYPE.THOUSAND_METRIC_UNIT'
      | 'REMINDER.TYPE.WORKTIME.INTERVAL_TYPE.HOURS'
      | 'REMINDER.TYPE.WORKTIME.INTERVAL_TYPE.DAYS'
      | 'REMINDER.TYPE.WORKTIME.INTERVAL_TYPE.MONTHS'
      | 'REMINDER.TYPE.WORKTIME.INTERVAL_TYPE.YEARS'

    fk_reminder_type: string
  }

  type CTFleetGetFleetRemindersTemplate = {
    reminder_template_id: string
    name: string
    fk_reminder_type_id: ReminderTypeId
    repeat_interval: string
    fk_interval_type_id: ReminderIntervalTypeId
    target_type: string
    user_id: string
  }

  type ApiOutput = {
    ct_fleet_get_fleet_reminders_types: Array<CTFleetGetFleetRemindersType>
    ct_fleet_get_fleet_reminders_interval_types: Array<CTFleetGetFleetRemindersIntervalType>
    ct_fleet_get_fleet_reminders_client_vehicles: Array<{
      [key: string]: null | string
    }>
    ct_fleet_get_fleet_reminders_templates: Array<CTFleetGetFleetRemindersTemplate>
    ct_fleet_get_fleet_reminders_client_drivers: Array<CTFleetGetFleetRemindersClientDriver>
    ct_fleet_get_fleet_reminders_group_vehicles: Array<CTFleetGetFleetRemindersGroupVehicle>
    ct_fleet_get_fleet_reminders_group_drivers: Array<CTFleetGetFleetRemindersGroupDriver>
  }
}
