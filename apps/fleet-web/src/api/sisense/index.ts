import apiCaller from 'api/api-caller'
import type { SisenseLoginApi } from './types'

function parseSisenseLogin(res: SisenseLoginApi.ApiOutput) {
  return {
    userGroupName: res.user_group_name,
    token: res.token,
    sisenseApi: res.sisense_api,
  }
}

export default {
  sisenseLogin(locale = 'en-ZA') {
    return apiCaller('ct_sisense_login', {
      locale,
    }).then(parseSisenseLogin)
  },
}
