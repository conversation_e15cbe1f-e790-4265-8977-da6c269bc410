import { getTodayStartEnd } from '../../util-functions/moment-helper'
import apiCaller from '../api-caller'
import { utcTimestampToMs } from 'cartrack-utils'
import type { FetchDvirs } from './types'
import type { FixMeAny } from 'src/types'

export enum DefectType {
  Mirrors = '1',
  Wheels = '2',
  Horn = '3',
  Tires = '4',
  OilLevel = '5',
  ParkingBrakes = '6',
  Headlights = '7',
  FuelTanks = '8',
  NoDefect = '9',
}

const defectTypes = {
  [DefectType.Mirrors]: 'Mirrors',
  [DefectType.Wheels]: 'Wheels',
  [DefectType.Horn]: 'Horn',
  [DefectType.Tires]: 'Tires',
  [DefectType.OilLevel]: 'Oil Level',
  [DefectType.ParkingBrakes]: 'Parking Brakes',
  [DefectType.Headlights]: 'Headlights',
  [DefectType.FuelTanks]: 'Fuel Tanks',
  [DefectType.NoDefect]: '',
}

export enum DefectState {
  AwaitingRepair = '0',
  Repaired = '1',
  PassedInspection = '2',
}

export const defectStates = {
  [DefectState.AwaitingRepair]: 'Awaiting Repair',
  [DefectState.Repaired]: 'Repaired',
  [DefectState.PassedInspection]: 'Passed Inspection',
} as const

export function parseDvir(raw: FetchDvirs.ApiOutput[number]) {
  return {
    ...raw,
    id: String(raw.vehicleDefectsHeaderId),
    creationTime: utcTimestampToMs(raw.eventTs),
    vehicleTypeId: raw.vehicleTypeId || 0,
    defectType: defectTypes[raw.defectTypeId],
    defectState: defectStates[raw.defectStateId],
    description:
      raw.defectTypeId === DefectType.NoDefect ? 'No Defects' : raw.description,
    repairTime: utcTimestampToMs(raw.repairDate),
    inspectTime: utcTimestampToMs(raw.inspectDate),
    vehicleId: String(raw.vehicleId),
  }
}

export function parseDvirs(rawDvirs: FetchDvirs.ApiOutput) {
  return rawDvirs ? rawDvirs.map((d) => parseDvir(d)) : []
}

export default {
  fetchDvirs(
    startTime: moment.MomentInput = null,
    endTime: moment.MomentInput = null,
    params: FixMeAny = null,
  ) {
    const dates =
      startTime && endTime
        ? getTodayStartEnd(startTime, endTime)
        : { startDate: undefined, endDate: undefined }

    return apiCaller('ct_fleet_get_DVIRS', {
      page: params?.page !== undefined ? params?.page + 1 : undefined,
      itemPerPage: params?.itemPerPage ?? undefined,
      sort: params?.sortColumn
        ? `${params.sortColumn}:${params?.sortDirection}`
        : undefined,
      filters: {
        fromDate: dates.startDate?.substring(0, 10) ?? undefined,
        toDate: dates.endDate?.substring(0, 10) ?? undefined,
        status: params?.status ?? undefined,
        search: params?.search ?? undefined,
      },
    }).then((res) => ({ meta: res.meta, data: parseDvirs(res.data) }))
  },
}
