import { useQuery } from '@tanstack/react-query'
import apiCaller from '../api-caller'
import { parseDvir } from './index'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'

async function fetchDvirDetails(vehicleDefectsHeaderId: string) {
  return apiCaller(
    'ct_fleet_get_dvir_details',
    {
      vehicleDefectsHeaderId,
    },
    { noX: true },
  ).then((res) => parseDvir(res))
}

export default function useDvirDetailsQuery(vehicleDefectsHeaderId: string) {
  return useQuery({
    queryKey: ['dvirs/dvirDetails', vehicleDefectsHeaderId] as const,
    queryFn: () => fetchDvirDetails(vehicleDefectsHeaderId),
    ...makeQueryErrorHandlerWithToast(),
  })
}
