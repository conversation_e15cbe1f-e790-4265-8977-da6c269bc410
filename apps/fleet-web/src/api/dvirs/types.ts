import type { parseDvir, parseDvirs, DefectState, DefectType } from './index'

type Dvir = {
  vehicleDefectsHeaderId: string
  eventTs: string
  vehicleTypeId: string
  defectTypeId: DefectType
  defectStateId: DefectState
  repairDate: string
  inspectDate: string
  vehicleId: string
  odometer: string
  description: string
  locationDescription: string
  driverId: string
  trailerRegistration: string
  signature?: Record<string, any>
  note: string
  date: string
  location: string
  inspectDriverName: string
  repairDriverName: string
  driverName: string
  inspectNotes: string
  vehicleRegistration: string
  inspectOdometer: string
  repairNotes: string
  repairOdometer: string
  photos?: Array<{
    base64: string
    created_ts: string
    url: string | null
    type: 'defect' | 'repair' | 'inspection'
  }>
}

export declare namespace FetchDvirs {
  type ApiOutput = Array<Dvir>
  type Return = ReturnType<typeof parseDvirs>
}

export declare namespace FetchDvirDetails {
  type ApiOutput = Dvir
  type Return = ReturnType<typeof parseDvir>
}
