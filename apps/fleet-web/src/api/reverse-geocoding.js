import { GOOGLE_MAPS_VERSION } from 'cartrack-utils/constants'

export const googleReverseGeoCoding = (coordinate, apiKey) => {
  const urlParams = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${coordinate.lat},${coordinate.lon}&key=${apiKey}&v=${GOOGLE_MAPS_VERSION}`

  return fetch(urlParams)
    .then((response) => response.json())
    .then((res) => {
      if (res && res.results) {
        return res.results[0]
      }
    })
}

export const hereReverseGeoCoding = (coordinate, apiKey) => {
  const urlParams = `https://reverse.geocoder.ls.hereapi.com/6.2/reversegeocode.json?prox=${coordinate.lat}%2C${coordinate.lon}%2C200&mode=retrieveAddresses&maxresults=1&gen=9&apiKey=${apiKey}`

  return fetch(urlParams)
    .then((response) => response.json())
    .then((res) => {
      if (res && res.Response && res.Response.View.length > 0) {
        return res.Response.View[0].Result[0]
      }
    })
}
