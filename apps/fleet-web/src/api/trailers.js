import apiCaller from './api-caller'

export const fetchApiTrailers = () =>
  apiCaller('ct_fleet_fetch_trailers').then((res) => res)

export const httpCreateTrailer = (params) =>
  apiCaller('ct_fleet_add_client_trailer', params).then((res) => res)

export const fetchTrailersForVehicle = (params) =>
  apiCaller('ct_fleet_fetch_trailers_for_vehicle', params).then((res) => res)

export const linkTrailersToVehicle = (params) =>
  apiCaller('ct_fleet_link_trailers_to_vehicle', params).then((res) => res)

export const httpUpdateTrailer = (params) =>
  apiCaller('ct_fleet_update_client_trailer', params).then((res) => res)

export const httpDeleteTrailer = (params) =>
  apiCaller('ct_fleet_delete_client_trailers', params).then((res) => res)

export const httpDeleteVehicleTrailers = (params = {}, isAll) => {
  let api = 'ct_fleet_remove_trailer_from_vehicle'
  let paramsData = params
  if (isAll) {
    api = 'ct_fleet_remove_all_trailers_from_vehicle'
    paramsData = {
      vehicle_id: params.vehicle_id,
    }
  }

  return apiCaller(api, paramsData).then((res) => res)
}
