import download from 'downloadjs'
import moment from 'moment-timezone'
import apiCaller from './api-caller'

export default {
  exportData(type, data, columns, fileName) {
    //customFileName from vehicle-raw-data.jsx
    const date = moment().format('YYYY-MM-DD')
    apiCaller('ct_fleet_export_data', { type, data, columns }, { noParse: true })
      .then((res) => res.blob())
      .then((result) =>
        download(result, fileName ? `${fileName}.xls` : `${type}-${date}.xls`),
      )
  },
}
