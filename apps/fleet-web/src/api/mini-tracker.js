import apiCaller from './api-caller'
import parseColor from './colors'
import {
  dateTimeToDateString,
  dateStringToDateTime,
  MINITRACK_ICON_TO_TYPE,
} from 'cartrack-utils'

function parseDetails(details) {
  return details.map((t) => ({
    id: t.vehicle_id,
    dateInstalled: t.date_installed ? dateStringToDateTime(t.date_installed) : 0,
    datePurchased: t.purchase_date ? dateStringToDateTime(t.purchase_date) : 0,
    nextMaintenanceDate: t.next_maintenance_date
      ? dateStringToDateTime(t.next_maintenance_date)
      : 0,
    description: t.description,
    assetSerial: t.asset_registration_serial,
    homeGeofence: t.home_geofence,
    notes: t.notes,
    goodCondition: t.good_condition === 't',
    image: t.image_base64,
    color: t.color && parseColor(t.color.toString(16)),
    colorName: t.color && parseColor(t.color.toString(16), true),
    icon: t.icon,
    type: MINITRACK_ICON_TO_TYPE[t.icon],
    name: t.registration,
    terminalSerial: t.terminal_serial,
  }))
}

function normalizeMinitracker(t) {
  return {
    vehicle_id: t.id,
    date_installed: dateTimeToDateString(t.dateInstalled),
    description: t.description,
    purchase_date: dateTimeToDateString(t.datePurchased),
    next_maintenance_date: dateTimeToDateString(t.nextMaintenanceDate),
    home_geofence: t.homeGeofence,
    good_condition: t.goodCondition,
    notes: t.notes,
    color: t.color && parseInt(t.color.replace('#', ''), 16),
    icon: t.icon,
    image: t.image,
  }
}

export default {
  fetchMinitrackerDetails() {
    return apiCaller('ct_fleet_get_minitrack_details').then((res) =>
      parseDetails(res.ct_fleet_get_minitrack_details),
    )
  },

  updateMinitracker(update) {
    return apiCaller('ct_fleet_set_minitrack_details', normalizeMinitracker(update))
  },
}
