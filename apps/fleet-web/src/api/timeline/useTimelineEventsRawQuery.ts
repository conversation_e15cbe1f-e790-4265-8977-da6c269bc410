import { useQuery, type UseQueryOptions } from '@tanstack/react-query'
import { fetchTimelineEventsRaw } from 'api/timeline'
import type { FetchTimelineEventsRaw } from 'api/timeline/types'
import type { CartrackLocales } from 'api/user/types'
import { type FuelUnitSetting, getFuelUnitSetting, getLocale } from 'duxs/user'
import { useTypedSelector } from 'src/redux-hooks'
import type { PromiseResolvedType } from 'src/types/global'
import type { Except } from 'type-fest'

export declare namespace FetchTimelineEventsRawQuery {
  type ApiInput = {
    vehicleId: string
    startTs: string
    endTs: string
    isSVR?: boolean
  }

  type ApiOutput = FetchTimelineEventsRaw.ApiOutput

  type Return = PromiseResolvedType<typeof fetchTimelineEventsRawQuery>
}

const createKey = ({
  vehicleId,
  startTs,
  endTs,
  isSVR,
}: FetchTimelineEventsRawQuery.ApiInput) =>
  ['timeline/eventsRaw', { vehicleId, startTs, endTs, isSVR }] as const

type Key = ReturnType<typeof createKey>

function useTimelineEventsRawQuery<TData = FetchTimelineEventsRawQuery.Return>(
  { vehicleId, startTs, endTs, isSVR }: FetchTimelineEventsRawQuery.ApiInput,
  options?: Except<
    UseQueryOptions<FetchTimelineEventsRawQuery.Return, Error, TData, Key>,
    'queryFn' | 'queryKey'
  >,
) {
  const userLocale = useTypedSelector(getLocale)
  const fuelUnitSetting = useTypedSelector(getFuelUnitSetting)

  return useQuery({
    queryKey: createKey({ vehicleId, startTs, endTs, isSVR }),
    queryFn: ({ queryKey }) =>
      fetchTimelineEventsRawQuery(queryKey, { userLocale, fuelUnitSetting }),
    ...options,
  })
}

async function fetchTimelineEventsRawQuery(
  [_, { vehicleId, startTs, endTs, isSVR }]: Key,
  metaData: {
    fuelUnitSetting: FuelUnitSetting
    userLocale: CartrackLocales | undefined
  },
) {
  return fetchTimelineEventsRaw(metaData, vehicleId, startTs, endTs, isSVR)
}

export default Object.assign(useTimelineEventsRawQuery, { createKey })
