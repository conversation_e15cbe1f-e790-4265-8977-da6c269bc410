import type { Except } from 'type-fest'
import type {
  UNSAFE_PositionDescription,
  PositionDescription,
  DriverName,
} from 'api/types'
import type { EventStatusClassName } from 'src/modules/map-view/map/types'
import type { FixMeAny } from 'src/types'

/* __PLEASE__ make sure to confirm with a BE dev if the sensor type means what you think it means before inserting it here.
 *  More need to be documented.
 */
export enum SensorTypeId {
  FUEL = '6', // both fuel types report number values. The difference for us is pretty much none.
  FUEL_CAN = '9',
}

export type TimelineEvent = {
  actions: Array<{
    icon: string
    description: string
    sourceType: 'video' | 'sensor'
  }>
  actions2: Array<FixMeAny>
  altClassName: string
  bearing: number
  clock: string
  clockMinutes: number
  dynamic1: string | null
  dynamic2: string | null
  dynamic3: string | null
  dynamic4: string | null
  eventCategory: string
  event_geom: string | null | undefined
  eventTimestamp: string
  eventType: string
  eventTypeDescription: string | null
  eventTypeIcon: string | null
  fuelLevel: string
  fuelPercent: string | null
  fuelUsed: string | null
  gpsFixType: number | null
  id: string
  ignition: number
  isRepeat: boolean
  isRoadSpeeding: boolean
  lat: number | null
  lng: number | null
  manifoldPressure: string | null
  odometer: number | null
  oilPressure: number
  oilTemp: number
  position: PositionDescription
  roadSpeed: number
  rpm: number
  sensors: Array<{
    distance: number
    eventTime: string
    legend: string | null
    sensorNumber: string
    sensorTypeId: `${number}`
    time: number
    value: string | number | null
    /** Chart value - Same as "value" except when the sensor type is 'DIGITAL':
     *
     * When 'DIGITAL', it contains an element from the "categories" array__ in "sensorsByNumber" object
     */
    checkvalue: string | number | null
    vehicleSensorsId: string
  }>
  speed: number
  statusClassName: EventStatusClassName
  temp1: number | null
  temp2: number | null
  temp3: number | null
  temp4: number | null
  time: number
  type: number
  unitTemp: number
  vbat: number
  nonExtVoltage: boolean
  adBlueLevel: string
  /**
   * In Celsius °C
   */
  waterTemp: number
  xAccl: string
  yAccl: string
  zAccl: string

  // When SVR
  width?: number | null
  positionType?: string | null
  radius?: number | null
}

export type BasicEvent = {
  id: TimelineEvent['id']
  coords: { lat: number; lng: number } | null
  vehicleStatus: TimelineEvent['statusClassName']
  date: TimelineEvent['eventTimestamp']
  odometer: number | null
  sensors: {
    [sensorNumber: string]:
      | string
      | number
      /** When we try to access this object on a specific event, the sensor we are looking for might be available but with an invalid value for that particular event */
      | null
      /** It might also not be in the object at all */
      | undefined
  }
}

export type EventUI = {
  out_vehicle_id: string
  pctLeft: number
  pctWidth: number
  statusClass: string
  tripNumber: number
  trip_start_type_id: string
}

export type BasicEventUI = Pick<EventUI, 'statusClass' | 'pctLeft' | 'pctWidth'>

type EventLastPosition = {
  gps_fix_type: number | null
  statusClass: EventStatusClassName | null
} & ({ lat: number; lng: number } | { lat: null; lng: null })

type TimelineTotals = {
  drivingTime: string
  idlingTime: string
  ignitionTime: string
  stopTime: string
  totalDistance: number
  totalFuelEconomy: number
  totalFuelUsed: number
}

export type TimelineActivityEvent =
  | (TimelineEvent & { coords: BasicEvent['coords'] })
  | (BasicEvent & { time: number })

export type NormalizedBaseEvents<EventType> = {
  byId: { [eventId: string]: EventType }
  allIds: Array<string>
}

export declare namespace GetDriverDayEvents {
  type ApiOutput = {
    chart: Array<{
      address: string | null
      altClassName: string
      duration: number
      eld_event_code: number
      eld_event_type: number
      eld_events_id: string
      event_number: number
      event_time: string
      exception_id: number
      is_from_phone: boolean
      latitude: number
      longitude: number
      notes: string | null
      pctLeft: number
      pctWidth: number
      statusClassName: string
      status_id: number
      time: number
      tripEndTime: number
      tripStartTime: number
    }>
    vehicleChart: Array<Record<string, FixMeAny>>
    details: Record<string, FixMeAny>
    recap: Record<string, number>
    stats: Record<string, FixMeAny>
  }
}

export declare namespace FetchTimelineEventsUI {
  type ApiBaseTrip = {
    driverId: string | null
    driverName: DriverName
    startLocation: UNSAFE_PositionDescription
    startGpsFixType: `${number}` | null
    endGpsFixType: `${number}` | null
    endLocation: UNSAFE_PositionDescription
    tripId: string
    tripNumber: number
    totalDistance: number | null | undefined
    startTime: string
    startTimeMS: number
    endTime: string
    endTimeMS: number
    end_geofence_name: string | undefined
    start_geofence_name: string | undefined
    flagged: boolean
  }

  type ComplexApiOutput = {
    alerts: {
      harshAcceleration: number
      harshBraking: number
      harshCornering: number
      roadSpeeding: number
      speeding: number
      overRev: number
      totalEventsCount: number
    }
    dayEnd: string
    dayStart: string
    eventsStart: string
    timeline: Array<EventUI>
    totals: TimelineTotals
    trips: Array<ApiBaseTrip & Record<string, FixMeAny>>
    lastPosition: EventLastPosition
  }

  type ApiOutput =
    | ComplexApiOutput
    | {
        lastPosition: EventLastPosition
      }

  type ParsedApiTrip = {
    startLocation: PositionDescription
    endLocation: PositionDescription
    startGpsFixType: number | null
    endGpsFixType: number | null
    startGeofence: ApiBaseTrip['start_geofence_name']
    endGeofence: ApiBaseTrip['end_geofence_name']
  } & Except<
    ApiBaseTrip,
    | 'endLocation'
    | 'startLocation'
    | 'endGpsFixType'
    | 'startGpsFixType'
    | 'end_geofence_name'
    | 'start_geofence_name'
  > &
    Record<string, FixMeAny>

  type ParsedLastPosition = {
    lat: number
    lng: number
    statusClassName: EventStatusClassName | null
  }

  type ReturnWithTrips = {
    lastPosition: ParsedLastPosition
    trips: Array<ParsedApiTrip>
  } & Except<ComplexApiOutput, 'lastPosition' | 'trips'>

  type Return =
    | {
        lastPosition: ParsedLastPosition
        trips: undefined
      }
    | ReturnWithTrips
}

export declare namespace FetchTimelineEventsRaw {
  type ApiOutput = {
    events: Array<
      Except<TimelineEvent, 'position'> & { position: UNSAFE_PositionDescription }
    >
    sensorByNumber: Array<SharedTimelineEventsTypes.ApiSensor>
  }
}

export declare namespace FetchMultipleDaysTimeline {
  type ApiOutput = {
    dayStart: string
    dayEnd: string
    events: Array<BasicEvent>
    timeline: Array<BasicEventUI>
    totals: TimelineTotals
    lastPosition: {
      lat: number
      lng: number
    }
    sensorByNumber: Array<SharedTimelineEventsTypes.ApiSensor>
  }

  type Return = {
    dayStart: string
    dayEnd: string
    events: NormalizedBaseEvents<BasicEvent & { time: number }>
    timeline: Array<BasicEventUI>
    totals: TimelineTotals
    lastPosition: {
      lat: number
      lng: number
    }
    sensorsByNumber: Array<SharedTimelineEventsTypes.ApiSensor>
  }
}

export declare namespace SharedTimelineEventsTypes {
  type SensorSignalType = 'DIGITAL' | 'ANALOG'

  type BaseSensor = {
    sensorNumber: string
    sensorTypeId: `${number}`
    name: string
  }

  type ApiAnalogSensor = BaseSensor & {
    signalType: 'ANALOG'
    unit: string
  }

  type ApiDigitalSensor = BaseSensor & {
    signalType: 'DIGITAL'
    categories: Array<string>
  }

  type ApiSensor = ApiAnalogSensor | ApiDigitalSensor

  type SensorsByNumber = {
    [sensorNumber: string]: ApiSensor
  }
}

export type TripsWithOrWithoutStops = Array<
  | FetchTimelineEventsUI.ParsedApiTrip
  | {
      startTime: FetchTimelineEventsUI.ParsedApiTrip['startTime'] | Date
      endTime: FetchTimelineEventsUI.ParsedApiTrip['endTime'] | Date
      stopTime: string
      startLocation: FetchTimelineEventsUI.ParsedApiTrip['startLocation']
      endLocation: FetchTimelineEventsUI.ParsedApiTrip['endLocation']
      startGeofence: FetchTimelineEventsUI.ParsedApiTrip['startGeofence']
      endGeofence: FetchTimelineEventsUI.ParsedApiTrip['endGeofence']
      startGpsFixType: FetchTimelineEventsUI.ParsedApiTrip['startGpsFixType']
      endGpsFixType: FetchTimelineEventsUI.ParsedApiTrip['endGpsFixType']
      isStopTime: true
      totalDistance: FetchTimelineEventsUI.ParsedApiTrip['totalDistance']
      maxSpeed: FetchTimelineEventsUI.ParsedApiTrip['maxSpeed']
      driverName: FetchTimelineEventsUI.ParsedApiTrip['driverName']
    }
>
