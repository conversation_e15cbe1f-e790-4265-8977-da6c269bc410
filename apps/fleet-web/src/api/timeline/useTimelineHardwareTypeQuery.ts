import { useQuery, type UseQueryOptions } from '@tanstack/react-query'
import { fetchTimelineHardwareType } from 'api/timeline'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import { minutesToMs } from 'src/util-functions/functional-utils'
import type { VehicleId } from 'api/types'

declare namespace FetchTimelineHardwareTypeQuery {
  type ApiInput = {
    vehicleId: VehicleId
  }

  type QueryInput = {
    vehicleId: VehicleId | null
  }
}

const createKey = ({ vehicleId }: FetchTimelineHardwareTypeQuery.QueryInput) =>
  ['timeline/hardwareType', { vehicleId }] as const

async function fetchTimelineHardwareTypeGuard({
  vehicleId,
}: FetchTimelineHardwareTypeQuery.QueryInput) {
  if (!vehicleId) {
    return Promise.reject()
  }
  return fetchTimelineHardwareType({ vehicleId })
}

export default function useTimelineHardwareTypeQuery(
  { vehicleId }: FetchTimelineHardwareTypeQuery.QueryInput,
  options?: Pick<UseQueryOptions, 'enabled'>,
) {
  return useQuery({
    queryKey: createKey({ vehicleId }),
    queryFn: () => fetchTimelineHardwareTypeGuard({ vehicleId }),
    staleTime: minutesToMs(10),
    ...makeQueryErrorHandlerWithToast(),
    ...options,
  })
}
