import { useQuery } from '@tanstack/react-query'
import { fetchTimelineEventsUI } from 'api/timeline'
import type { FetchTimelineEventsUI } from 'api/timeline/types'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import type { PromiseResolvedType } from 'src/types/global'

declare namespace FetchTimelineEventsUIQuery {
  type ApiInput = {
    vehicleId: string
    startTs: string
    endTs: string
    isFullTimeline?: boolean
    isSingleTrip?: boolean
  }

  type ApiOutput = FetchTimelineEventsUI.ApiOutput

  type Return = PromiseResolvedType<typeof fetchTimelineEventsUIQuery>
}

const createKey = ({
  vehicleId,
  startTs,
  endTs,
  isFullTimeline = false,
  isSingleTrip = false,
}: FetchTimelineEventsUIQuery.ApiInput) =>
  [
    'timeline/eventsUI',
    { vehicleId, startTs, endTs, isFullTimeline, isSingleTrip },
  ] as const

type Key = ReturnType<typeof createKey>

function useTimelineEventsUIQuery({
  vehicleId,
  startTs,
  endTs,
  isFullTimeline = false,
  isSingleTrip = false,
}: FetchTimelineEventsUIQuery.ApiInput) {
  return useQuery({
    queryKey: createKey({ vehicleId, startTs, endTs, isFullTimeline, isSingleTrip }),
    queryFn: ({ queryKey }) => fetchTimelineEventsUIQuery(queryKey),
    ...makeQueryErrorHandlerWithToast(),
  })
}

async function fetchTimelineEventsUIQuery([
  _,
  { vehicleId, startTs, endTs, isFullTimeline, isSingleTrip },
]: Key) {
  return fetchTimelineEventsUI(vehicleId, startTs, endTs, isFullTimeline, isSingleTrip)
}

export default Object.assign(useTimelineEventsUIQuery, { createKey })
