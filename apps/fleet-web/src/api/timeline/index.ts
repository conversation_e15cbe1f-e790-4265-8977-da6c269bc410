import { isNil } from 'lodash'
import apiCaller, { apiCallerNoX } from '../api-caller'
import {
  msToUTCTimestamp,
  convertNullToUndefined,
  isNilOrEmptyString,
  exact,
} from 'cartrack-utils'
import type { FixMeAny, PromiseResolvedType } from 'src/types'
import type {
  GetDriverDayEvents,
  FetchTimelineEventsUI,
  FetchTimelineEventsRaw,
  FetchMultipleDaysTimeline,
  SharedTimelineEventsTypes,
} from './types'
import { normalizeCoordinate } from 'src/util-functions/map-utils'
import type { EventStatusClassName } from 'src/modules/map-view/map/types'
import {
  getSensorTypeIdEnumType,
  normalizeGpsFixType,
  normalizeUnsafePositionDescription,
} from 'api/utils'
import type { VehicleId } from 'api/types'
import { formatLiquidVolumeWithOptions } from 'src/modules/components/connected/useFormatLiquidVolume'
import type { CartrackLocales } from 'api/user/types'
import type { FuelUnitSetting } from 'duxs/user'

const parseTimelineEventsSensorsByNumber = (
  sensorByNumber: Array<SharedTimelineEventsTypes.ApiSensor>,
  {
    getFuelSensorLegend,
  }: { getFuelSensorLegend: (legend: string | null) => string | null },
): Array<SharedTimelineEventsTypes.ApiSensor> =>
  sensorByNumber.map((sensor) => {
    const isFuelSensor = getSensorTypeIdEnumType(sensor.sensorTypeId) === 'fuel'
    if (sensor.signalType === 'ANALOG' && isFuelSensor) {
      const { unit, ...rest } = sensor
      return {
        ...rest,
        unit: getFuelSensorLegend(unit) ?? '',
      } satisfies SharedTimelineEventsTypes.ApiAnalogSensor
    }

    return sensor
  })

const genGetFuelSensorLegend = ({
  fuelUnitSetting,
  userLocale,
}: {
  fuelUnitSetting: FuelUnitSetting
  userLocale: CartrackLocales | undefined
}) => {
  const fuelUnitToShow =
    formatLiquidVolumeWithOptions({
      locale: userLocale,
      userLiquidVolumeUnitSetting: fuelUnitSetting,
      valueInLiters: 20, // random value just to trigger the unit computation
    })
      .genFormattedValueToParts()
      .find((part) => part.type === 'unit')?.value ?? ''

  return {
    getFuelSensorLegend: (legend: string | null) => {
      if (isNilOrEmptyString(legend)) {
        return null
      }

      return fuelUnitToShow
    },
  }
}

/**
 * Unit that has 24 trips should always be in 'driving' state
 *
 * In the future the BE should deal with this
 */
const getVehicleEventStatusFromIsSingleTrip = (
  isSingleTrip: boolean,
  eventStatus: EventStatusClassName,
) => (isSingleTrip ? 'driving' : eventStatus)

const normalizeVehicleTripHistory = (trips: Array<Record<string, any>>) =>
  trips.map((trip) => ({
    trip_id: trip.tripId,
    trip_start_ts: msToUTCTimestamp(trip.startTimeMS),
    trip_end_ts: msToUTCTimestamp(trip.endTimeMS),
    client_trip_description: trip.notes,
    client_trip_additional_description: trip.extraNotes,
    client_trip_type_id: trip.noteTypeId,
  }))

const parseTimelineEvents = (
  response: FetchTimelineEventsRaw.ApiOutput,
  isSingleTrip: boolean,
  {
    fuelUnitSetting,
    userLocale,
  }: { fuelUnitSetting: FuelUnitSetting; userLocale: CartrackLocales | undefined },
) => {
  const { getFuelSensorLegend } = genGetFuelSensorLegend({
    fuelUnitSetting,
    userLocale,
  })

  const parsedEvents = response.events.map(({ sensors, ...event }) => {
    const lat = normalizeCoordinate(event.lat)
    const lng = normalizeCoordinate(event.lng)

    const position = normalizeUnsafePositionDescription(event.position)

    return {
      ...event,
      gpsFixType: normalizeGpsFixType(event.gpsFixType),
      position,
      statusClassName: getVehicleEventStatusFromIsSingleTrip(
        isSingleTrip,
        event.statusClassName,
      ),
      latitude: lat,
      longitude: lng,
      lat,
      lng,
      sensors: sensors.map(
        (
          sensor,
        ): FetchTimelineEventsRaw.ApiOutput['events'][number]['sensors'][number] => {
          const isFuelSensor = getSensorTypeIdEnumType(sensor.sensorTypeId) === 'fuel'

          if (isFuelSensor) {
            const { checkvalue, value, legend } = sensor

            if (isNilOrEmptyString(checkvalue) || isNilOrEmptyString(value)) {
              return sensor
            }

            return exact({
              ...sensor,
              legend: getFuelSensorLegend(legend),
              value: formatLiquidVolumeWithOptions({
                locale: userLocale,
                userLiquidVolumeUnitSetting: fuelUnitSetting,
                valueInLiters: Number(value),
              }).parsedValue,
              checkvalue: formatLiquidVolumeWithOptions({
                locale: userLocale,
                userLiquidVolumeUnitSetting: fuelUnitSetting,
                valueInLiters: Number(checkvalue),
              }).parsedValue,
            })
          }

          return sensor
        },
      ),
    }
  })

  const parsedResponse = {
    events: parsedEvents,
    sensorsByNumber: parseTimelineEventsSensorsByNumber(response.sensorByNumber, {
      getFuelSensorLegend,
    }),
  }

  return parsedResponse
}

const parseMultipleDaysTimeline = (
  multipleDaysTimeline: FetchMultipleDaysTimeline.ApiOutput,
  isSingleTrip: boolean,
  {
    fuelUnitSetting,
    userLocale,
  }: { fuelUnitSetting: FuelUnitSetting; userLocale: CartrackLocales | undefined },
): FetchMultipleDaysTimeline.Return => {
  const { dayStart, dayEnd, sensorByNumber, timeline, totals } = multipleDaysTimeline

  const { getFuelSensorLegend } = genGetFuelSensorLegend({
    fuelUnitSetting,
    userLocale,
  })

  const firstEventWithCoordsIndex = multipleDaysTimeline.events.findIndex(
    (event) => event.coords !== null,
  )
  const firstEventWithCoords =
    firstEventWithCoordsIndex !== -1
      ? multipleDaysTimeline.events[firstEventWithCoordsIndex]
      : undefined

  const events = multipleDaysTimeline.events.reduce<
    FetchMultipleDaysTimeline.Return['events']
  >(
    (acc, { id, ...event }, index) => {
      let eventCoords = event.coords
      if (firstEventWithCoords !== undefined && index < firstEventWithCoordsIndex) {
        // Assign coords of the first event with coords to all previous events
        eventCoords = firstEventWithCoords.coords
      }

      acc.byId[id] = {
        ...event,
        id,
        time: new Date(event.date).getTime(),
        vehicleStatus: getVehicleEventStatusFromIsSingleTrip(
          isSingleTrip,
          event.vehicleStatus,
        ),
        coords: eventCoords,
      }
      acc.allIds.push(id)

      return acc
    },
    {
      byId: {},
      allIds: [],
    },
  )

  const normalizedLastPosition = {
    lat: normalizeCoordinate(multipleDaysTimeline.lastPosition.lat),
    lng: normalizeCoordinate(multipleDaysTimeline.lastPosition.lng),
  }

  // If last event doesn't have coords, use the last known position from timeline events UI
  if (events.allIds.length > 0) {
    const lastEvent = events.byId[events.allIds[events.allIds.length - 1]]

    if (lastEvent.coords === null) {
      events.byId[events.allIds[events.allIds.length - 1]] = {
        ...lastEvent,
        coords: normalizedLastPosition,
      }
    }
  }

  return {
    dayStart,
    dayEnd,
    sensorsByNumber: parseTimelineEventsSensorsByNumber(sensorByNumber, {
      getFuelSensorLegend,
    }),
    timeline,
    totals,
    events,
    lastPosition: normalizedLastPosition,
  }
}

const updateVehicleTripDetails = (
  id: string,
  tripToUpdate: FetchTimelineEventsUI.ParsedApiTrip,
) =>
  apiCaller('ct_fleet_update_vehicle_logbook', {
    data: [
      {
        vehicle_id: id,
        logBookEntries: normalizeVehicleTripHistory([tripToUpdate]),
      },
    ],
  })

const deleteVehicleTripDetails = ({
  vehicleId,
  tripId,
}: {
  vehicleId: VehicleId
  tripId: FetchTimelineEventsUI.ParsedApiTrip['tripId']
}) =>
  apiCallerNoX('ct_fleet_delete_vehicle_logbook', {
    vehicle_id: vehicleId,
    trip_id: tripId,
  })

const apiRequestSVRPoll = (vehicleId: FixMeAny) =>
  apiCaller('ct_sp_fleet_request_poll', { vehicleId })

const parseTimelineEventsUI = (
  data: FetchTimelineEventsUI.ApiOutput,
): FetchTimelineEventsUI.Return => {
  const parsedLastPosition = {
    lat: normalizeCoordinate(data.lastPosition.lat),
    lng: normalizeCoordinate(data.lastPosition.lng),
    statusClassName: isNilOrEmptyString(data.lastPosition.statusClass)
      ? null
      : data.lastPosition.statusClass,
  }
  if ('trips' in data) {
    return {
      ...data,
      lastPosition: parsedLastPosition,
      trips: data.trips.map((trip) => {
        const {
          startLocation,
          endLocation,
          driverId,
          startGpsFixType,
          endGpsFixType,
          start_geofence_name,
          end_geofence_name,
          ...rest
        } = trip

        const parsedRest = convertNullToUndefined(rest) // Only here for __legacy__ reasons
        const parsedTrip: FetchTimelineEventsUI.ParsedApiTrip = {
          ...parsedRest,
          driverId,
          startGpsFixType: startGpsFixType === null ? null : Number(startGpsFixType),
          endGpsFixType: endGpsFixType === null ? null : Number(endGpsFixType),
          startLocation: normalizeUnsafePositionDescription(startLocation),
          endLocation: normalizeUnsafePositionDescription(endLocation),
          startGeofence: start_geofence_name,
          endGeofence: end_geofence_name,
        }

        return parsedTrip
      }),
    }
  }

  return {
    ...data,
    lastPosition: parsedLastPosition,
    trips: undefined,
  }
}

/**
 * Fetch UI data for displaying the timeline events bar for a vehicle
 * Also contains trips UI data for timeline tables
 */
const fetchTimelineEventsUI = (
  vehicle_id: string,
  /** Example: '2019-05-12 10:59' */
  start_ts: string,
  /** Example: '2019-05-13 10:59' */
  end_ts: string,
  /** If true, it will return the UI start from the entire day. This is used for view all timeline feature */
  is_full_timeline = false,
  is_single_trip = false,
) =>
  apiCaller(
    'ct_fleet_get_vehicle_timeline',
    {
      vehicle_id,
      start_ts,
      end_ts,
      is_full_timeline,
      is_single_trip,
    },
    { noX: true },
  ).then((res: FetchTimelineEventsUI.ApiOutput) => parseTimelineEventsUI(res))

/**
 * Fetches raw timeline data from the API
 * @param {string} vehicle_id vehicle id
 * @param {string} start_ts - Example: '2019-05-12 10:59'
 * @param {string} end_ts - Example: '2019-05-13 10:59'
 * @param {boolean} is_svr is SVR
 * @returns {object} parsed timeline events data
 */
const fetchTimelineEventsRaw = (
  metaData: {
    fuelUnitSetting: FuelUnitSetting
    userLocale: CartrackLocales | undefined
  },
  vehicle_id: string,
  start_ts: string,
  end_ts: string,
  is_svr = false,
  isSingleTrip = false,
) =>
  apiCaller(
    'ct_fleet_get_vehicle_timeline_events',
    {
      vehicle_id,
      start_ts,
      end_ts,
      is_svr,
      has_sensors: true,
    },
    { noX: true },
  ).then((res: FetchTimelineEventsRaw.ApiOutput) =>
    parseTimelineEvents(res, isSingleTrip, metaData),
  )

const fetchMultipleDaysTimeline = ({
  vehicleId,
  startDate,
  endDate,
  isSingleTrip,
  metaData,
}: {
  metaData: {
    fuelUnitSetting: FuelUnitSetting
    userLocale: CartrackLocales | undefined
  }
  vehicleId: string
  /** @example 2021-01-01 00:00 */
  startDate: string
  /** @example 2021-01-19 23:59 */
  endDate: string
  isSingleTrip: boolean
}) =>
  apiCaller(
    'ct_fleet_get_timeline_events',
    {
      vehicle_id: vehicleId,
      start_date: startDate,
      end_date: endDate,
    },
    { noX: true },
  ).then((res: FetchMultipleDaysTimeline.ApiOutput) =>
    parseMultipleDaysTimeline(res, isSingleTrip, metaData),
  )

const getDriverDayEvents = ({
  userId,
  driverId,
  startDate,
  endDate,
  limit,
  inFalse,
}: Record<string, any>) =>
  apiCaller(
    'web_fetch_driver_eld_timeline_events',
    {
      in_user_id: userId.toString(),
      in_client_driver_id: driverId.toString(),
      in_start_event_timestamp: startDate,
      in_end_event_timestamp: endDate,
      in_limit: limit.toString(),
      in_false: inFalse,
    },
    { noX: true },
  ).then((res: GetDriverDayEvents.ApiOutput) => {
    const parsed = res

    return {
      ...parsed,
      chart: parsed.chart.map((event) => {
        const {
          status_id,
          is_from_phone,
          exception_id,
          eld_event_type,
          eld_event_code,
          ...rest
        } = event

        return {
          ...rest,
          statusId: status_id,
          isFromPhone: is_from_phone,
          exceptionId: exception_id,
          ELDEventType: eld_event_type,
          ELDEventCode: eld_event_code,
        }
      }),
    }
  })

const fetchTripSpeedData = (params: Record<string, any>) =>
  apiCaller('ct_fleet_get_tripstat_speed', params).then(
    (result: FixMeAny) => result || {},
  )

const fetchTripThrottleData = (params: Record<string, any>) =>
  apiCaller('ct_fleet_get_tripstat_throttle', params).then(
    (result: FixMeAny) => result || {},
  )

const fetchTripRPMData = (params: Record<string, any>) =>
  apiCaller('ct_fleet_get_tripstat_rpm', params).then(
    (result: FixMeAny) => result || {},
  )

const fetchTimelineHardwareType = (params: { vehicleId: string }) =>
  apiCaller('ct_fleet_get_timeline_hardware_type', params).then(
    ({ ct_fleet_get_timeline_hardware_type }: FixMeAny) =>
      parseTimelineHardwareType(ct_fleet_get_timeline_hardware_type) || {},
  )

const parseTimelineHardwareType = (data: Array<FixMeAny>) => ({
  hardwareTypeListId: isNil(data[0].hardware_type_list_id)
    ? null
    : Number(data[0].hardware_type_list_id),
  hardwareTypeId: isNil(data[0].hardware_type_list_id)
    ? null
    : Number(data[0].hardware_type_id),
})

export {
  fetchTimelineEventsUI,
  fetchTimelineEventsRaw,
  fetchMultipleDaysTimeline,
  getDriverDayEvents,
  apiRequestSVRPoll,
  fetchTripSpeedData,
  fetchTripThrottleData,
  fetchTripRPMData,
  updateVehicleTripDetails,
  deleteVehicleTripDetails,
  fetchTimelineHardwareType,
}

/* Resolved Types */
export type GetDriverDayEventsResolved = PromiseResolvedType<typeof getDriverDayEvents>
export type FetchTimelineEventsRawResolved = PromiseResolvedType<
  typeof fetchTimelineEventsRaw
>

export type FetchTimelineEventsUIResolved = PromiseResolvedType<
  typeof fetchTimelineEventsUI
>
