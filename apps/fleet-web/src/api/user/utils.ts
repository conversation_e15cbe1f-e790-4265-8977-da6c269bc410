import type { Opaque } from 'type-fest'
import type { KarooUiCustomizableTheme } from '@karoo-ui/core'
import { isNil, isEmpty } from 'lodash'

export type StringifiedKarooUiCustomizableTheme = Opaque<
  string,
  'StringifiedKarooUiCustomizableTheme'
>

/**
 * Encapsulates the values that the karoo ui theme setting can hold
 */
export type ParsedKarooUiCustomizableTheme = ReturnType<typeof parseKarooUiThemeSetting>

export const parseKarooUiThemeSetting = (
  rawKarooUiTheme: StringifiedKarooUiCustomizableTheme | null | undefined,
) => {
  if (isNil(rawKarooUiTheme) || typeof rawKarooUiTheme !== 'string') {
    return null
  }

  if (rawKarooUiTheme.trim().length === 0) {
    return null
  }

  try {
    const parsedTheme = JSON.parse(rawKarooUiTheme)
    if (isEmpty(parsedTheme)) {
      // The BE might return a string with an empty object/array for some reason (not uncommon) so we want to guard against those cases
      return null
    }
    return parsedTheme as KarooUiCustomizableTheme
  } catch (_e) {
    // We might end up here if an invalid theme value was stored on the DB and we can't parse it. If that is the case, we should consider the theme as "not defined"
    return null
  }
}

export const normalizeKarooUiThemeSetting = (
  karooUiTheme: KarooUiCustomizableTheme | 'DEFAULT',
) =>
  karooUiTheme === 'DEFAULT'
    ? ('' as StringifiedKarooUiCustomizableTheme) // BE will store an empty string. It doesn't need to know which default values we use. This way we also prevent default theme from being out of sync
    : (JSON.stringify(karooUiTheme) as StringifiedKarooUiCustomizableTheme)
