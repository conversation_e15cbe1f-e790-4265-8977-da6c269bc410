import type { UNSAFE_PositionDescription } from 'api/types'
import type { BooleanALaCartrack1 } from 'src/types'

export declare namespace FetchPOIs {
  type ApiOutput = {
    ct_fleet_get_poi: Array<{
      poi_id: string
      client_user_id: string | null
      name: string
      description: string
      lat: string
      lon: string
      poi_status_id: string
      geo_radius: string
      status_group_name: string | null
      status_group_colour: string | null
      colour: string
      owner: string
      allow_subuser_tosee_mainuser_poi: BooleanALaCartrack1
      allow_subuser_toedit_mainuser_poi: BooleanALaCartrack1
      allow_mainuser_tosee_subuser_poi: BooleanALaCartrack1
      allow_mainuser_toedit_subuser_poi: BooleanALaCartrack1
      position: UNSAFE_PositionDescription
    }>

    ct_fleet_get_client_POI_status: Array<{
      poi_status_id: string
      status_group_name: string | null
      status_group_colour: string
    }>
  }
}
