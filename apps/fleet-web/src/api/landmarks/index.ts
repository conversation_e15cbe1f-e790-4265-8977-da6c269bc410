import { flatMap, isEmpty, isNil, round } from 'lodash'
import parseColor from '../colors'
import apiCaller from '../api-caller'
import {
  groupByAndMapValues,
  utilRanges,
  dateTimesToUTCTimestampRange,
  utcTimestampToMs,
} from 'cartrack-utils'
import type { FetchPOIs } from './types'
import type { FixMeAny, PromiseResolvedType } from 'src/types'
import { normalizeUnsafePositionDescription } from 'api/utils'

const normalizePoiRadius = (radius: number) => (radius ? round(radius, 3) : 285)

function parseLandmarks(
  rawLandmarks: FetchPOIs.ApiOutput['ct_fleet_get_poi'],
  rawLandmarkGroups: FetchPOIs.ApiOutput['ct_fleet_get_client_POI_status'],
) {
  const landmarks = rawLandmarks.map((rawLandmark) => {
    const rawClientUserId = rawLandmark.client_user_id
    const isClientUserId = isNaN(Number(rawClientUserId)) // Due to the inconsistency result from the API, this method to determine if it is valid id
    const clientUserId = isClientUserId ? rawClientUserId : null

    const isSubUser =
      !isEmpty(clientUserId) &&
      !isNil(rawLandmark.allow_mainuser_tosee_subuser_poi) &&
      !isNil(rawLandmark.allow_mainuser_toedit_subuser_poi)

    return {
      id: rawLandmark.poi_id,
      name: rawLandmark.name,
      owner: rawLandmark.owner,
      ownerSettings: {
        isSubUser,
        ...(isSubUser
          ? {
              clientUserId,
              subUserPOICanBeSeenByMainUsers:
                rawLandmark.allow_mainuser_tosee_subuser_poi === 't',
              subUserPOICanBeEditedByMainUsers:
                rawLandmark.allow_mainuser_toedit_subuser_poi === 't',
            }
          : {}),
      },
      color: parseColor(rawLandmark.colour),
      colorName: parseColor(rawLandmark.colour, true),
      lat: Number(rawLandmark.lat),
      lng: Number(rawLandmark.lon),
      address: normalizeUnsafePositionDescription(rawLandmark.position),
      radius: Number(rawLandmark.geo_radius),
      groupId: rawLandmark.poi_status_id,
      groupName: rawLandmark.status_group_name,
      description: rawLandmark.description,
    }
  })

  const landmarkIdsByGroupId = groupByAndMapValues(landmarks, 'groupId', 'id')

  const groups = rawLandmarkGroups.map((raw) => ({
    id: raw.poi_status_id,
    name: raw.status_group_name || 'New Group', // Name can be an empty string
    color: raw.status_group_colour,
    itemIds: landmarkIdsByGroupId[raw.poi_status_id] || [],
  }))

  return {
    landmarks,
    groups,
  }
}

function parseLandmarkVisits({
  ct_fetch_vehicle_poi_visits: rawVisits,
}: Record<string, FixMeAny>) {
  return flatMap(rawVisits || [], (v) => [
    {
      vehicleId: v.out_vehicle_id,
      poiId: v.out_poi_id,
      poiName: v.out_poi_name,
      registration: v.out_registration,
      enter: true,
      time: utcTimestampToMs(v.out_enter_timestamp),
    },
    {
      vehicleId: v.out_vehicle_id,
      poiId: v.out_poi_id,
      poiName: v.out_poi_name,
      registration: v.out_registration,
      enter: false,
      time: utcTimestampToMs(v.out_exit_timestamp),
    },
  ]).filter((v) => v.time !== null)
}

const LandmarksAPI = {
  fetchLandmarks({ updateCache = false } = {}) {
    return apiCaller('ct_fleet_get_poi', undefined, {
      updateCache,
    }).then((res) =>
      parseLandmarks(res.ct_fleet_get_poi, res.ct_fleet_get_client_POI_status),
    )
  },

  fetchLandmarkVisits(id: string) {
    const { from, to } = utilRanges.lastWeek()
    const { startTS, endTS } = dateTimesToUTCTimestampRange(from, to, {
      time: false,
    })
    return apiCaller('ct_fetch_vehicle_poi_visits', {
      data: [
        {
          vehicle_id: null,
          startDate: startTS,
          endDate: endTS,
          poi_id: id,
        },
      ],
    }).then(parseLandmarkVisits)
  },

  createLandmarkGroup(name: FixMeAny) {
    return apiCaller('ct_fleet_create_client_vehicle_driver_group', {
      vehicleGroupData: {
        name,
        group_driver_id: '',
        group_vehicle_id: '',
        group_geofence_id: '',
        group_status_id: '',
        description: '',
      },
      type: 'poi',
    }).then((res) => res.groupData[0].poi_status_id)
  },

  saveLandmark({
    name,
    description,
    radius,
    lat,
    lng,
    color,
    id,
  }: Record<string, FixMeAny>) {
    return apiCaller('ct_fleet_save_poi', {
      poi_id: id,
      name,
      description,
      lat: String(lat),
      lon: String(lng),
      poi_status_id: 0,
      geo_radius: normalizePoiRadius(radius),
      colour: color,
    }).then((res) => res.ct_fleet_save_poi[0].poi_id)
  },

  updateLandmarks(landmarks: Array<FixMeAny>) {
    const update = landmarks.map((l) => ({
      poi_id: l.id,
      name: l.name,
      description: l.description,
      lat: String(l.lat),
      lng: String(l.lng),
      poi_status_id: 0,
      geo_radius: normalizePoiRadius(l.radius),
      color: l.color,
    }))
    return apiCaller('ct_fleet_save_poi_bulk', { poi_list: update })
  },

  deleteLandmark(id: string) {
    return apiCaller('ct_fleet_delete_poi', {
      poi_id: id,
    }).then(() => null)
  },
}

export default LandmarksAPI

export type FetchLandmarksResolved = PromiseResolvedType<
  typeof LandmarksAPI.fetchLandmarks
>

export type FetchLandmarksVisitsResolved = PromiseResolvedType<
  typeof LandmarksAPI.fetchLandmarkVisits
>
