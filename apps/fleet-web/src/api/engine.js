import { flow, keyBy, map, reduce } from 'lodash/fp'
import moment from 'moment-timezone'
import { isNil, mapKeys, forEach, floor } from 'lodash'

import { reeferItemKeyMap, reeferHistoryKeyMap } from '../duxs/engine'
import apiCaller from './api-caller'
import { isTrue } from 'cartrack-utils'
import { ctIntl } from 'cartrack-ui-kit'

// Sub Parse Function
const parseReeferHistory = (rawRes, vehicleId, extra) =>
  flow(
    map((item) => mapKeys(item, (value, key) => reeferHistoryKeyMap[key])),
    // Parse to useful data type
    map((item) => {
      const result = []
      for (let i = 1; i <= 4; i++) {
        let object = {}

        // Get setpoint from API, threshold from frontend >>> API need updates
        const setpoint = isNil(item[`setpoint${i}`])
          ? null
          : parseFloat(item[`setpoint${i}`])
        const threshold = isNil(extra.threshold[i - 1])
          ? null
          : parseFloat(extra.threshold[i - 1])
        if (!isNil(setpoint) && !isNil(threshold)) {
          let safeMinTemp = 0
          let safeMaxTemp = 0

          safeMinTemp = parseFloat(setpoint - threshold)
          safeMaxTemp = parseFloat(setpoint + threshold)

          object = {
            ...object,
            safeMinTemp,
            safeMaxTemp,
            // Backend data might be missing. Use frond end value instead.
            extraSetpoint: setpoint,
            extraThreshold: threshold,
          }
        }

        forEach(item, (value, key) => {
          if (!isNil(value)) {
            switch (key) {
              case 'eventTime': {
                const eventTime = ctIntl.transformServerDateStringInto(item.eventTime, {
                  format: 'HH:mm',
                })
                const hour = eventTime.substring(0, 2)
                const minute = eventTime.substring(3, 5)
                const eventHour = Number(hour) + Number(Number(minute) / 60)
                object = { ...object, eventTime, eventHour }
                break
              }

              case `threshold${i}`:
              case `returnTemp${i}`:
              case `setpoint${i}`: {
                const base = key.substring(0, key.length - 1)

                object = {
                  ...object,
                  [base]: floor(item[`${base}${i}`], 1),
                }
                break
              }

              case 'vehicleId':
                object = { ...object, vehicleId: Number(item.vehicleId) }
                break
              case 'vehicleName':
                object = { ...object, vehicleName: item.vehicleName }
                break
              default:
                break
            }
          }
        })
        result.push(object)
      }

      return result
    }),
    // Convert to useful structure for Rechart
    reduce(
      (result, item) => {
        item.forEach((subItem, index) => {
          result[vehicleId][index].data.push(subItem)
        })
        return result
      },
      { [vehicleId]: [{ data: [] }, { data: [] }, { data: [] }, { data: [] }] },
    ),
    // Calculate percentage
    map((item) => {
      const returnTempArray = item.map((subItem) =>
        subItem.data.map((realItem) => realItem.returnTemp),
      )
      const percentageArray = returnTempArray.map((tempArray, index) => {
        const isAllValueEqual = tempArray.every((val, i, arr) => val === arr[0])

        // Calculate from threshold
        const { safeMaxTemp } = item[index].data[0]
        const { safeMinTemp } = item[index].data[0]
        // Real Temp
        const tempMaxTick = Math.max(...tempArray)
        const tempMinTick = Math.min(...tempArray)
        const down = tempMinTick
        const scope = tempMaxTick - tempMinTick

        const chartMaxValue = floor(Math.max(tempMaxTick, safeMaxTemp) + 5, 1)
        const chartMinValue = floor(Math.min(tempMinTick, safeMinTemp) - 5, 1)

        // Threshold cover upper place
        const maxGrayPercentage =
          safeMaxTemp > tempMaxTick || scope === 0
            ? 0
            : 100 - parseFloat(((safeMaxTemp - down) / scope) * 100)
        // Threshold cover down place
        const minGrayPercentage =
          safeMinTemp < tempMinTick || scope === 0
            ? 100
            : 100 - parseFloat(((safeMinTemp - down) / scope) * 100)
        return {
          maxGrayPercentage,
          minGrayPercentage,
          tempMaxTick,
          tempMinTick,
          isAllValueEqual,
          chartMaxValue,
          chartMinValue,
        }
      })

      return item.map((subItem, index) => ({
        data: subItem.data,
        maxGrayPercentage: percentageArray[index].maxGrayPercentage,
        minGrayPercentage: percentageArray[index].minGrayPercentage,
        tempMaxTick: percentageArray[index].tempMaxTick,
        tempMinTick: percentageArray[index].tempMinTick,
        isAllValueEqual: percentageArray[index].isAllValueEqual,
        chartMaxValue: percentageArray[index].chartMaxValue,
        chartMinValue: percentageArray[index].chartMinValue,
      }))
    }),
    keyBy((item) => item[0].data[0].vehicleId),
  )(rawRes)

const parseTemperature = (temperature, setpoint, threshold) => {
  const temperatureValue = isNil(temperature) ? null : floor(temperature, 1)
  const setpointValue = isNil(setpoint) ? null : floor(setpoint, 1)
  const thresholdValue = isNil(threshold) ? null : floor(threshold, 1)

  const temperatureObj = {
    temperature: temperatureValue,
    temperatureColor: 'rgb(0, 0, 0)',
    isWarning: false,
  }

  if (!isNil(setpointValue) && !isNil(thresholdValue)) {
    const safeMaxValue = setpointValue + thresholdValue
    const safeMinValue = setpointValue - thresholdValue
    if (temperatureValue > safeMaxValue) {
      temperatureObj.temperatureColor = 'rgb(229, 125, 70)'
      temperatureObj.isWarning = true
    } else if (temperatureValue < safeMinValue) {
      temperatureObj.temperatureColor = 'rgb(83, 144, 198)'
      temperatureObj.isWarning = true
    }
  }

  return temperatureObj
}

// Parse Function
const parseReeferTable = (rawRes) => {
  let groupId = 0
  let refrigeratorGroupId = 0

  return flow(
    map((item) => mapKeys(item, (value, key) => reeferItemKeyMap[key])),
    map((item) => {
      const subDataCount = [0, 0, 0, 0, 0, 0]
      Object.keys(item).forEach((key) => {
        if (/temperature\d/.test(key))
          subDataCount[0] = isNil(item[key]) ? subDataCount[0] : ++subDataCount[0]
        else if (/ecoil\d/.test(key))
          subDataCount[1] = isNil(item[key]) ? subDataCount[1] : ++subDataCount[1]
        else if (/return\d/.test(key))
          subDataCount[2] = isNil(item[key]) ? subDataCount[2] : ++subDataCount[2]
        else if (/discharge\d/.test(key))
          subDataCount[3] = isNil(item[key]) ? subDataCount[3] : ++subDataCount[3]
        else if (/setpoint\d/.test(key))
          subDataCount[4] = isNil(item[key]) ? subDataCount[4] : ++subDataCount[4]
        else if (/threshold\d/.test(key))
          subDataCount[5] = isNil(item[key]) ? subDataCount[5] : ++subDataCount[5]
      })

      const parsedAlarms = item.alarms.replace(/[{"}]/g, '').split(',')

      let result = { ...item }

      const groupDataCount = Math.max(...subDataCount)
      if (groupDataCount >= 1) result = { ...item, groupDataCount, groupId: groupId++ }

      if (item.alarmCount > 1)
        result = { ...result, refrigeratorGroupId: refrigeratorGroupId++ }

      return { ...result, groupDataCount, parsedAlarms }
    }),
    map((item) => {
      let object = {}
      Object.keys(item).forEach((key) => {
        if (!isNil(item[key])) {
          // Use switch case here for extending more or refactor in the future
          switch (key) {
            case 'id':
              object = { ...object, [key]: Number(item[key]) }
              break
            case 'alarmCount': {
              const alarmCount = isNil(item[key]) ? null : Number(item[key])
              object = { ...object, [key]: alarmCount }
              break
            }

            case 'lastUpdate':
              object = {
                ...object,
                [key]: moment(item[key]).format('MM/DD/YYYY'),
              }
              break
            case 'lastUpdateDuration':
              object = {
                ...object,
                [key]: (moment().unix() - Number(item[key]) * 60) * 1000,
              }
              break
            case 'reeferStatus':
            case 'refrigerator':
              object = {
                ...object,
                [key]: isNil(item[key]) ? null : isTrue(item[key]),
              }
              break
            default:
              if (
                /temperature\d/.test(key) ||
                /ecoil\d/.test(key) ||
                /discharge\d/.test(key) ||
                /setpoint\d/.test(key) ||
                /threshold\d/.test(key) ||
                /return\d/.test(key)
              ) {
                if (!isNil(item[key]))
                  object = {
                    ...object,
                    [key]: floor(item[key], 1),
                  }
              } else object = { ...object, [key]: item[key] }
              break
          }
        }
      })

      // Refrigerator status
      const refrigeratorHasError = item.parsedAlarms.some((alarmItem) => {
        const arr = alarmItem.split(';')
        const parsedContent = arr[1].slice(0, arr[1].length - 1).trim()
        return parsedContent !== 'None'
      })
      const refrigeratorStatus = refrigeratorHasError ? 'Errors Only' : 'All'

      // Temperature Status
      const count = Object.keys(object).filter((key) =>
        /temperature\d/.test(key),
      ).length
      let temperatureStatus = 'All'
      for (let i = 1; i <= count; i++) {
        const temperature = floor(item[`temperature${i}`], 1)
        const setpoint = isNil(item[`setpoint${i}`])
          ? null
          : floor(item[`setpoint${i}`], 1)
        const threshold = isNil(item[`threshold${i}`])
          ? null
          : floor(item[`threshold${i}`], 1)
        if (!isNil(setpoint) && !isNil(threshold)) {
          const safeMax = setpoint + threshold
          const safeMin = setpoint - threshold
          if (temperature > safeMax || temperature < safeMin) {
            temperatureStatus = 'Errors Only'
            break
          }
        }
      }

      // Real Alarm
      const realAlarmData = item.parsedAlarms.reduce(
        (result, alarms) => {
          const data = alarms.slice(1, alarms.length - 1).split(';')

          // API still not update so need this logic ....
          if (data.length === 3)
            return {
              ...result,
              alarms: [...result.alarms, data[1]],
              alarmsColor: [...result.alarmsColor, `#${data[2]}`],
            }
          return {
            ...result,
            alarms: [...result.alarms, data[1]],
            alarmsColor: [...result.alarmsColor, `#${data[0]}`],
          }
        },
        {
          alarms: [],
          alarmsColor: [],
        },
      )

      return { ...object, temperatureStatus, refrigeratorStatus, realAlarmData }
    }),
  )(rawRes)
}

const parseInitialWidth = (data) => {
  const pageData = Object.values(data).slice(0, 25)
  const longestWordCount = pageData.reduce(
    (count, item) =>
      item.parsedAlarms.reduce((result, item) => {
        const alarmMsg = item.slice(1, item.length - 1).split(';')[1]
        return result < alarmMsg.length ? alarmMsg.length : result
      }, 0),
    0,
  )
  return Math.max(longestWordCount * 8 + 160, 220)
}

const parseDetailTemperature = (data) =>
  flow(
    map((item) => {
      const newArray = []
      const count = Object.keys(item).filter((key) =>
        /(refrigerator|ecoil|discharge|setpoint|threshold|return|temperature)\d/.test(
          key,
        ),
      ).length

      // Object to Array with 4 elements
      for (let i = 1; i <= count; i++) {
        let object = { name: `Zone ${i}` }

        Object.keys(item).forEach((key) => {
          switch (key) {
            case 'id':
              object = { ...object, [key]: item[key] }
              break
            case 'refrigerator':
            case 'reeferStatus': {
              // eslint-disable-next-line no-nested-ternary
              const value = isNil(item[key]) ? null : isTrue(item[key]) ? 'ON' : 'OFF'
              const valueColor = isTrue(item[key])
                ? 'rgb(179, 201, 105)'
                : 'rgb(152, 152, 152)'
              object = {
                ...object,
                [key]: value,
                [`${key}Color`]: valueColor,
              }
              break
            }

            case 'door': {
              // eslint-disable-next-line no-nested-ternary
              const value = isNil(item[key])
                ? null
                : isTrue(item[key])
                ? 'OPEN'
                : 'CLOSE'
              const valueColor = isTrue(item[key])
                ? 'rgb(179, 201, 105)'
                : 'rgb(152, 152, 152)'
              object = {
                ...object,
                [key]: value,
                [`${key}Color`]: valueColor,
              }
              break
            }

            case `ecoil${i}`:
            case `discharge${i}`:
            case `setpoint${i}`:
            case `threshold${i}`:
              object = {
                ...object,
                [key.slice(0, key.length - 1)]: floor(item[key], 1),
              }
              break
            case `return${i}`:
              object = { ...object, returnTemp: floor(item[key], 1) }
              break
            case 'lastUpdate':
              object = {
                ...object,
                lastUpdate: moment(item[key]).format('MM/DD/YYYY'),
              }
              break
            case `temperature${i}`: {
              const { temperature, temperatureColor, isWarning } = parseTemperature(
                item[key],
                item[`setpoint${i}`],
                item[`threshold${i}`],
              )
              object = { ...object, temperature, temperatureColor, isWarning }
              break
            }

            default:
              break
          }
        })

        if (
          !isNil(object.temperature) ||
          !isNil(object.ecoil) ||
          !isNil(object.returnTemp) ||
          !isNil(object.discharge) ||
          !isNil(object.setpoint) ||
          !isNil(object.threshold)
        )
          newArray.push(object)
      }

      return newArray
    }),
    keyBy((item) => (item.length ? item[0].id : undefined)),
  )(data)

const parseDetailRefrigerator = (data) =>
  flow(
    map((item) => {
      const realAlarmData = item.parsedAlarms.reduce(
        (result, alarms) => {
          const data = alarms.slice(1, alarms.length - 1).split(';')

          // API still not update so need this logic ....
          if (data.length === 3)
            return {
              ...result,
              alarms: [...result.alarms, data[1]],
              alarmsColor: [...result.alarmsColor, `#${data[2]}`],
            }
          return {
            ...result,
            alarms: [...result.alarms, data[1]],
            alarmsColor: [...result.alarmsColor, `#${data[0]}`],
          }
        },
        {
          alarms: [],
          alarmsColor: [],
        },
      )

      let object = {}
      Object.keys(item).forEach((key) => {
        if (!isNil(item[key])) {
          switch (key) {
            case 'id':
            case 'vehicleName':
              object = { ...object, [key]: item[key] }
              break
            case 'ignition': {
              // eslint-disable-next-line no-nested-ternary
              const value = isNil(item[key]) ? null : isTrue(item[key]) ? 'ON' : 'OFF'
              const valueColor = isTrue(item[key])
                ? 'rgb(179, 201, 105)'
                : 'rgb(152, 152, 152)'
              object = {
                ...object,
                [key]: value,
                [`${key}Color`]: valueColor,
              }
              break
            }

            case 'alarms':
              object = {
                ...object,
                [key]: realAlarmData.alarms,
                [`${key}Color`]: realAlarmData.alarmsColor,
              }
              break
            case 'fuelLevel':
              object = { ...object, [key]: `${floor(item[key], 1)}%` }
              break
            case 'engineSpeed':
              object = { ...object, [key]: `${floor(item[key], 1)} rpm` }
              break
            case 'engineTemperature':
              object = { ...object, [key]: `${floor(item[key], 1)}°C` }
              break
            case 'ignitionOnRunningHours':
            case 'engineRunningHours':
            case 'electricRunningHours':
            case 'totalRunningHours':
              object = {
                ...object,
                [key]: item.totalRunningHours
                  ? ctIntl.getDurationWithFormattedHourMinute(
                      parseInt(item.totalRunningHours, 10) * 60,
                    ).formatted
                  : null,
              }
              break
            default:
              break
          }
        }
      })

      return object
    }),
    keyBy('id'),
  )(data)

// API
export const fetchEngineApi = (params) =>
  apiCaller('ct_diagnostic_get_vehicles', params).then((res) => res)

export const fetchHistoryApi = (params) =>
  apiCaller('ct_diagnostic_get_history', params).then((res) => res)

export const fetchReeferApi = (params) =>
  apiCaller('ct_diagnostic_reefer_get_vehicles', params).then(
    ({ ct_diagnostic_reefer_get_vehicles: rawRes }) => {
      if (rawRes.length === 0) return {}

      // Data for Table
      const reefer = parseReeferTable(rawRes)

      return {
        reefer,
        width: parseInitialWidth(reefer),
        detailDataTemperature: parseDetailTemperature(reefer),
        detailDataRefrigerator: parseDetailRefrigerator(reefer),
      }
    },
  )

export const fetchReeferHistoryApi = (params, extra) =>
  apiCaller('ct_diagnostic_reefer_get_temp_history', params).then(
    ({ ct_diagnostic_reefer_get_temp_history: rawRes }) => {
      if (rawRes.length === 0) return {}

      const vehicleId = rawRes[0].out_vehicle_id

      return parseReeferHistory(rawRes, vehicleId, extra)
    },
  )

export const updateThresholdApi = (params) =>
  apiCaller('ct_diagnostic_reefer_set_thresholds', params).then((res) => res)
