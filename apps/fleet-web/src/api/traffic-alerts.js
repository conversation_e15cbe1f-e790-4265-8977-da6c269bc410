import apiCaller from './api-caller'
import { utcTimestampToMs } from 'cartrack-utils'

function parseTrafficAlerts(rawTrafficAlerts) {
  return rawTrafficAlerts.map((t) => ({
    id: t.out_timestamp + t.out_lat + t.out_lon,
    time: utcTimestampToMs(t.out_timestamp),
    message: t.out_message,
    lat: Number(t.out_lat),
    lng: Number(t.out_lon),
  }))
}

export default {
  fetchTrafficAlerts() {
    return apiCaller('ct_fleet_get_info_ticker').then((res) =>
      parseTrafficAlerts(res.ct_fleet_info_ticker),
    )
  },
}
