import { isEmpty, isNil, reduce } from 'lodash'
import * as Re from 'remeda'
import type { useLocation } from 'react-router'
import {
  type UNSAFE_PositionDescription,
  type PositionDescription,
  type GPSFixType,
  type ApiOutputVehicleId,
  vehicleIdSchema,
} from './types'
import type { DropdownOption, FixMeAny } from 'src/types'
import type { FetchVehicleDetails } from 'src/api/vehicles/types'
import { SensorTypeId } from './timeline/types'
import type { z } from 'zod'
import { isNilOrEmptyString } from 'src/util-functions/string-utils'
import { ctIntl } from 'src/util-components/ctIntl'

/**
 * @param object containing keys (url parameters) and their values (EXCEPT undefined)
 */
export const buildQueryStringFromObject = (
  object: Record<string, any>,
  { generateQueryParam = (param: any) => param } = {},
) =>
  reduce(
    object,
    (acc, value, key) => {
      if (value !== undefined) {
        acc.push(`${key}=${generateQueryParam(value)}`)
      }
      return acc
    },
    [] as Array<string>,
  ).join('&')

export function buildRouteQueryString<
  T extends
    | z.ZodIntersection<any, any>
    | z.ZodObject<z.ZodRawShape>
    | z.ZodDiscriminatedUnion<any, any>,
>({ searchParams }: { schema: T; searchParams: z.infer<T> }): string {
  return buildQueryStringFromObject(searchParams, {
    generateQueryParam: (param) => encodeURIComponent(param),
  })
}

export function buildRouteQueryStringJSONStringified<
  T extends z.ZodObject<z.ZodRawShape> | z.ZodDiscriminatedUnion<any, any>,
>({ searchParams }: { schema: T; searchParams: z.infer<T> }): string {
  const stringifiedValuesSearchParams = Re.mapValues(searchParams, (value) =>
    JSON.stringify(value),
  )
  return buildQueryStringFromObject(stringifiedValuesSearchParams, {
    generateQueryParam: (param) => encodeURIComponent(param),
  })
}

export function buildRouteQueryStringKeepingExistingSearchParams<
  T extends z.ZodObject<z.ZodRawShape> | z.ZodDiscriminatedUnion<any, any>,
>({
  location,
  searchParams,
  schema,
}: {
  location: ReturnType<typeof useLocation>
  schema: T
  searchParams: z.infer<T>
}) {
  const searchParamsObj = Object.fromEntries(
    new URLSearchParams(location.search).entries(),
  )

  return buildRouteQueryString({
    schema,
    searchParams: {
      ...searchParamsObj,
      ...searchParams,
    },
  })
}

export const normalizeUnsafePositionDescription = (
  positionDescription: UNSAFE_PositionDescription,
): PositionDescription => {
  if (
    isNil(positionDescription) ||
    isNil(positionDescription.principal.description) ||
    positionDescription.principal.description === ''
  ) {
    return null
  }

  if (
    positionDescription.principal.description === 'private' ||
    positionDescription.principal.description === 'Privacy Enabled'
  ) {
    return { visibility: 'PRIVATE' }
  }

  return {
    visibility: 'PUBLIC',
    principal: {
      // Should remove this atrocity when ELD is done properly
      description: positionDescription.principal.description.replace(
        ', United States',
        '',
      ),
    },
    alternatives: positionDescription.alternatives,
  }
}

export const normalizeGpsFixType = (
  gpsFixType: number | `${number}` | null,
): GPSFixType =>
  gpsFixType === null ? null : (Number(gpsFixType) as Exclude<GPSFixType, null>)

export const parseCustomFields = (
  customField: Record<string, FixMeAny>,
  customData: Record<string, string> | null,
) => {
  const toReturn = []

  for (const customFieldKey in customData) {
    const customFieldLabel = customData[customFieldKey]

    toReturn.push({
      key: customFieldKey,
      value: customField[customFieldKey] ?? '',
      label: customFieldLabel,
    })
  }

  return toReturn
}

export const parseStringifiedCustomData = (
  rawCustomData: Record<string, any> | string | null | [] | '',
) =>
  isEmpty(rawCustomData)
    ? {}
    : (JSON.parse(rawCustomData as string) as Record<string, any>)

export const parseApiOutputVehicleId = (rawVehicleId: ApiOutputVehicleId) =>
  vehicleIdSchema.parse(rawVehicleId)

export const parseSpecialLicenses = (
  rawSpecialLicenses: FetchVehicleDetails.SpecialLicenseType,
) => {
  const specialLicenses: Array<
    DropdownOption & FetchVehicleDetails.SpecialLicenseType[0]
  > = []

  if (!rawSpecialLicenses) {
    return specialLicenses
  }

  rawSpecialLicenses.forEach((license) => {
    specialLicenses.push({
      ...license,
      name: license.license_name,
      value: license.driver_special_license_type_id,
    })
  })

  return specialLicenses
}

export const parseVehicleMaintenanceStatus = (
  rawStatus: FetchVehicleDetails.VehicleMaintenanceStatus,
) => {
  const status: Array<
    DropdownOption & FetchVehicleDetails.VehicleMaintenanceStatus[0]
  > = []

  if (!rawStatus) {
    return status
  }

  rawStatus.forEach((st) => {
    status.push({
      ...st,
      name: st.vehicle_status_option_name,
      value: st.vehicle_status_option_id,
    })
  })

  return status
}

export const getSensorTypeIdEnumType = (
  sensorTypeId: `${number}` | number,
): 'fuel' | 'other' => {
  const sensorTypeIdString = sensorTypeId.toString()

  switch (sensorTypeIdString) {
    case SensorTypeId.FUEL:
    case SensorTypeId.FUEL_CAN: {
      return 'fuel'
    }

    default: {
      return 'other'
    }
  }
}

export function parseBETranslationId<DV extends string | null>(
  rawTranslationId: string | null,
  { defaultValueIfEmpty }: { defaultValueIfEmpty: DV },
): string | DV {
  if (isNilOrEmptyString(rawTranslationId)) {
    return defaultValueIfEmpty
  }

  const translated = ctIntl.formatMessage({ id: rawTranslationId })

  return translated.length > 0 ? translated : defaultValueIfEmpty
}

export const parseBEBoolean = <
  const FallbackValue extends boolean | string | null | undefined,
>(
  value: unknown,
  {
    fallback,
  }: {
    /** __Very important to think about this value__.
     *
     * More often than not we get invalid values and the default value __always__ depends on the use case */
    fallback: FallbackValue
  },
): boolean | FallbackValue => {
  switch (value) {
    case true:
    case 't':
    case 'true': {
      return true
    }
    case false:
    case 'f':
    case 'false': {
      return false
    }
    default: {
      return fallback
    }
  }
}

export function waitFakeEndpoint(milliseconds: number) {
  return new Promise((resolve) => window.setTimeout(resolve, milliseconds))
}
