import apiCaller from './api-caller'
import { arraySnakeToCamelCase } from 'cartrack-utils'

// Route schema resolvers
const schema = {
  routeList: (data = []) =>
    data.map((item) => ({
      id: item.route_id,
      name: item.route_name,
      description: item.route_desc,
      borderWidth: item.border_size,
      lastUpdated: item.create_ts,
      value: item.route_id,
    })),
  routePlannerTypes: (data = []) =>
    data.map((item) => ({
      name: item.label,
      value: item.value,
      isDateRange: Boolean(item.has_date_range === 't'),
    })),
  routePlannerList: (data = []) => arraySnakeToCamelCase(data),
  notifTriggerType: {
    formattedTrigerTypes: (data = []) => arraySnakeToCamelCase(data),
    active: (types) =>
      types
        .filter((item) => item.routeAlert === 't')
        .map((item) => ({
          ...item,
          hasValueField: item.hasValueField === 't',
          fieldType: item.hasValueField
            ? schema.notifTriggerType.getTriggerType(item.notificationTriggerTypeId)
            : undefined,
        })),
    getTriggerType: (typeId) => {
      const typeIdTypes = {
        55: 'percentage', // Route Slow Progress (%)
        57: 'minutes', // Not Started within Time (mins)
      }
      return typeId && typeIdTypes[typeId] ? typeIdTypes[typeId] : undefined
    },
  },
}

// Routes
const httpFetchRouteList = () =>
  apiCaller('ct_fleet_get_routes').then((res) =>
    schema.routeList(res.ct_fleet_get_routes),
  )

const httpUpdateRouteItemInfo = (param) =>
  apiCaller('ct_fleet_update_route_data', param)

const httpDeleteRoutesItem = (param) => apiCaller('ct_fleet_delete_route_data', param)

// Routes planner
const httpFetchRoutePlannerList = () =>
  apiCaller('ct_fleet_get_route_planner').then((res) => schema.routePlannerList(res))

const httpCreateEditRoutePlanner = (param, isUpdating = false) =>
  apiCaller(
    isUpdating ? 'ct_fleet_update_route_planner' : 'ct_fleet_create_route_planner',
    param,
  )

const httpFetchRoutePlannerTypes = () =>
  apiCaller('ct_fleet_get_router_types_route_planner').then((res) =>
    schema.routePlannerTypes(res),
  )

const httpFetchRouteNotifTriggerType = () =>
  apiCaller('ct_fleet_route_notification_trigger_type').then((res) => {
    const all = schema.notifTriggerType.formattedTrigerTypes(
      res.ct_fleet_route_notification_trigger_type_id,
    )
    return {
      active: schema.notifTriggerType.active(all),
      all,
    }
  })

const httpAddNewRoute = (params) => apiCaller('ct_fleet_create_route_from_trip', params)

export {
  httpFetchRouteList,
  httpUpdateRouteItemInfo,
  httpDeleteRoutesItem,
  httpFetchRoutePlannerList,
  httpCreateEditRoutePlanner,
  httpFetchRoutePlannerTypes,
  httpFetchRouteNotifTriggerType,
  httpAddNewRoute,
}
