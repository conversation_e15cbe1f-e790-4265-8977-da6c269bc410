import { isEmpty } from 'lodash'
import apiCaller from './api-caller'

function parseRandomSurveyQuestion(rawSurvey) {
  return rawSurvey.reduce((acc, survey) => {
    if (!isEmpty(survey)) {
      acc.questionId = survey.out_question_id
      acc.questionText = survey.out_question_text
      acc.questionType = survey.out_question_type
    }

    return acc
  }, {})
}

function parseSurveyQuestionOptions(rawOptions) {
  return rawOptions
    .map((o) => ({
      questionId: o.question_id,
      optionId: o.option_id,
      optionText: o.option_text,
    }))
    .reverse()
}

export default {
  fetchRandomSurveyQuestion() {
    return apiCaller('ct_survey_get_random_question').then((res) =>
      parseRandomSurveyQuestion(res.ct_survey_get_random_question),
    )
  },

  fetchQuestionOptions(questionId) {
    return apiCaller('ct_survey_get_question_options', {
      questionId,
    }).then((res) => parseSurveyQuestionOptions(res.ct_survey_get_question_options))
  },

  ignoreSurvey() {
    return apiCaller('ct_save_survey_ignore').then((res) => res.ct_save_survey_ignore)
  },

  submitSurvey(questionId, optionId, answer = '') {
    return apiCaller('ct_save_survey_answer', {
      questionId,
      optionId,
      answer,
    })
  },
}
