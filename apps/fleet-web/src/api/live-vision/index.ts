import { apiCallerNoX } from '../api-caller'
import type {
  FetchHistoricalRequestsApi,
  GetCameraDetails,
  SendRequestFootageApi,
} from './types'
import type { VehicleId } from 'api/types'

export const requestCameraDetailsApi = ({ vehicleId }: { vehicleId: VehicleId }) =>
  apiCallerNoX<GetCameraDetails.ApiOutput>('ct_vision_get_camera_details', {
    vehicleId,
  }).then((cameraDetails) => {
    const {
      activeSources,
      cameraMaxSimVideo,
      activeCameraChannelsCount,
      maxCameraChannelsEverInstalled,
      cameraStatus,
      cameraType,
    } = cameraDetails

    return {
      activeSources,
      cameraMaxSimVideo,
      activeCameraChannelsCount,
      maxCameraChannelsEverInstalled,
      cameraType,
      cameraStatus:
        cameraStatus === null || cameraStatus === ''
          ? null
          : cameraStatus
              .split('')
              .sort()
              .map((cameraNumber) => Number(cameraNumber) - 1),
    }
  })

export const requestHistoricalVideoApi = (params: SendRequestFootageApi.ApiInput) =>
  apiCallerNoX<{ ct_vision_request_historical_video: boolean }>(
    'ct_vision_request_historical_video',
    {
      vehicleId: params.vehicleId,
      cameras: params.cameras,
      startTime: params.startTime,
      endTime: params.endTime,
      comment: params.comment,
    },
  ).then((res) => res.ct_vision_request_historical_video)

export const fetchHistoricalRequestsApi = (vehicleId: string) =>
  apiCallerNoX<FetchHistoricalRequestsApi.ApiOutput>(
    'ct_vision_get_historical_requests',
    { vehicleId },
  ).then((result) => result)

export const fetchDVROnlineStatus = (vehicleId: string) =>
  apiCallerNoX<{ getDvrOnlineStatus: boolean }>('ct_vision_get_dvr_online_status', {
    vehicleId,
  }).then((res) => res.getDvrOnlineStatus)
