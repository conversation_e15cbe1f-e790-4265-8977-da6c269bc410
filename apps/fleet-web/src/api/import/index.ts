import { isTrue } from 'cartrack-utils'
import type { PromiseResolvedType } from 'src/types'
import apiCaller from '../api-caller'
import type { UploadImportData, FetchImporters } from './types'

function parseImporters({
  types,
  categories: rawCategories,
}: FetchImporters.ApiOutput) {
  const importers = types.map((t) => ({
    id: t.import_id,
    name: t.import_description,
    value: t.import_id,
    entityType: t.import_type,
    prompts: t.prompts.split(';').filter((item) => item),
    category: t.import_category_id,
    template: t.import_template,
    is_mifleet: isTrue(t.is_mifleet),
    mifleet_import_type: t.sql_name,
    stringPrompts: t.prompts,
  }))

  const categories = rawCategories.map((c) => ({
    id: c.import_category_id,
    value: c.import_category_id,
    name: c.category_description,
  }))

  return { importers, categories }
}

const importAPI = {
  fetchImporters() {
    return apiCaller('ct_fleet_get_importer').then(parseImporters)
  },
  uploadImportData({ data, id }: UploadImportData.Input) {
    return apiCaller('ct_fleet_check_importer', {
      importData: {
        data_array: data,
      },
      importId: id,
    }).then((res: UploadImportData.ApiOutput) => res)
  },
}

export default importAPI

export type FetchImportersResolved = PromiseResolvedType<
  typeof importAPI.fetchImporters
>
