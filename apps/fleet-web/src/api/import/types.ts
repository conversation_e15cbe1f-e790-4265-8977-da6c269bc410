import type { BooleanALaCartrack1, FixMeAny } from '../../types/global'

export enum ImporterImportType {
  GEOFENCE = '10',
  VEHICLE = '15',
  DRIVER = '20',
  CUSTOMER = '25',
  TASK = '30',
  POI = '35',
  CODE_ITEM_TYPE = '45',
}

export declare namespace FetchImporters {
  type ApiOutput = {
    types: Array<{
      import_id: string
      import_description: string
      user_name_limit: FixMeAny | null
      prompts: string
      sql_name: string | null
      import_category_id: string
      import_template: string | null
      is_mifleet: BooleanALaCartrack1
      mifleet_import_type_id: string | null
      import_type: ImporterImportType | null
    }>
    categories: Array<{
      import_category_id: string
      category_description: string
    }>
  }
}

export declare namespace UploadImportData {
  type Input = { data: Array<FixMeAny>; id: string }

  type ApiOutput = {
    importResult: Array<{ message: string; status: boolean | string }>
    importType:
      | 'GEOFENCE'
      | 'VEHICLE'
      | 'DRIVER'
      | 'CUSTOMER'
      | 'TASK'
      | 'POI'
      | 'REPORTS'
      | 'CODE ITEM TYPE'
      | 'RUC'
    importError: string
  }
}
