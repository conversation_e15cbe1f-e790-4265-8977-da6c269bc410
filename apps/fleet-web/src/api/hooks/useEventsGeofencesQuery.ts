import { useQuery, type UseQueryOptions } from '@tanstack/react-query'
import apiCaller from 'api/api-caller'

declare namespace FetchGeofencesByEventGeometry {
  type ApiInput = Array<string>
  type ApiOutput = { [event_geom: string]: Array<string> }
  type Return = { [event_geom: string]: Array<string> }
}

export default function useEventsGeofencesQuery(
  eventsGeometry: FetchGeofencesByEventGeometry.ApiInput,
  options?: Omit<
    UseQueryOptions<FetchGeofencesByEventGeometry.Return, Error>,
    'queryKey' | 'queryFn'
  >,
) {
  return useQuery<FetchGeofencesByEventGeometry.Return, Error>({
    queryKey: ['eventGeofences', eventsGeometry],
    queryFn: () => fetchGeofencesByEventGeometry(eventsGeometry),
    ...options,
  })
}

async function fetchGeofencesByEventGeometry(
  eventsGeometry: FetchGeofencesByEventGeometry.ApiInput,
): Promise<FetchGeofencesByEventGeometry.Return> {
  return apiCaller(
    'ct_fleet_get_geofences_name_with_event_geometric',
    {
      eventGeom: eventsGeometry,
    },
    { noX: true },
  ).then((res: FetchGeofencesByEventGeometry.ApiOutput) =>
    parseGeofencesByEventGeometry(res),
  )
}

const parseGeofencesByEventGeometry = (
  geofences: FetchGeofencesByEventGeometry.ApiOutput,
): FetchGeofencesByEventGeometry.Return => geofences // ApiOutput has the same type as Return
