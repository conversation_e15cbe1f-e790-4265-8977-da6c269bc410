import type { MomentInput } from 'moment-timezone'

import apiCaller from '../api-caller'
import {
  utcTimestampToMs,
  msToUTCTimestamp,
  msToUserUTCTimestamp,
  postgresUnarrayify,
  // - postgresArrayify,
  dateTimeToDateString,
  dateTimesToUTCTimestampRange,
  // - findLastIndex,
  // - uuid,
  unapplyNamedTimezone,
  generateObjectWithReplacedProperties,
} from 'cartrack-utils'
import { eldEventTypes } from 'cartrack-ui-kit'
import type { DateTimeRange, FixMeAny } from 'src/types'
import { normalizeCoordinate } from 'src/util-functions/map-utils'

export const timeZoneIdToTimeZoneOffsetMap = {
  10: -10 * 3.6e6, // Hawaii
  9: -8 * 3.6e6, // Alaska
  8: -7 * 3.6e6, // PDT
  6: -6 * 3.6e6, // MDT
  4: -5 * 3.6e6, // CDT
  2: -4 * 3.6e6, // EDT
  0: -8 * 3.6e6, // T O D O: remove when we have real events
}

function resolveDriverDate(time: MomentInput, defaultTimeZone?: string) {
  const altered = unapplyNamedTimezone(time, defaultTimeZone)
  return dateTimeToDateString(altered, { utc: false })
}

function resolveStatusId(type: number, code: number, statusId: number) {
  if (statusId !== 0) return statusId
  if (type === 8 && code === 1) return 3 // T O D O: remove this when Aziz fixes erroneous event types
  if (type === 1) return code
  return 0
}

function parseELDEvents(
  rawELDEvents: Array<Record<string, FixMeAny>>,
  defaultTimeZone?: string,
) {
  return rawELDEvents?.map((e) => {
    const time = utcTimestampToMs(e.event_time)
    const isFromPhone = e.is_from_phone === 't' || e.is_from_phone === true
    let ELDEventType = Number(e.eld_event_type)
    let ELDEventCode = Number(e.eld_event_code)
    if (ELDEventType === 2 && ELDEventCode === 2 && isFromPhone) {
      ELDEventType = 1
      ELDEventCode = 2
    }

    return {
      id: e.eld_events_id,
      userId: e.user_id,
      clientDriverId: e.client_driver_id,
      terminalSerial: e.terminal_serial,
      time,
      date: resolveDriverDate(time, defaultTimeZone),
      receivedTime: utcTimestampToMs(e.received_time),
      sequenceNumber: e.sequence_number,
      latitude: normalizeCoordinate(e.latitude),
      longitude: normalizeCoordinate(e.longitude),
      statusId: resolveStatusId(ELDEventType, ELDEventCode, Number(e.status_id)),
      cycleRuleId: e.cycle_rule_id,
      isFromPhone,
      address: e.address,
      ELDEventType,
      ELDEventCode,
      odometer: e.odometer,
      engineHours: e.engine_hours,
      malfunctionCode: e.malfunction_code,
      diagnosticCode: e.diagnostic_code,
      coClientDriverIds: postgresUnarrayify(e.co_client_driver_ids),
      notes: e.notes === 'null' ? undefined : e.notes,
      exceptionId: Number(e.exception_id),
      timeZoneId: e.timezone_id,
      phoneSequenceNumber: e.phone_sequence_number,
      cargoTypeId: e.cargo_type_id,
      claimerId: e.claimer_id,
      lastClientDriverId: e.lastClientDriverId,
      statusClassName:
        (ELDEventType === 1 && eldEventTypes[ELDEventType - 1]) || undefined,
      positionId: e.position_description_id,
      positionDescription: e.position_description,
      tripNumber: e.trip_number,
      firmwareVersion: e.firmware_version,
    }
  })
}

function parseEditRequests(requests: Array<Record<string, FixMeAny>>) {
  return requests.map((req) => ({
    editId: req.edit_id,
    editUserId: req.user_id,
    editClientUserId: req.client_user_id,
    editStartTs: utcTimestampToMs(req.start_ts),
    editEndTs: utcTimestampToMs(req.end_ts),
    status: Number(req.pending_status),
    events: parseELDEvents(req.edited_events),
  }))
}

/* -

  function normalizeELDEvents(ELDEvents, isEdit) {
  return ELDEvents.map(e => ({
    eld_events_id: isEdit
      ? uuid()
      : e.isFromPhone && e.ELDEventType !== 5 && e.ELDEventType !== 4
        ? `${e.terminalSerial}-${e.time}-${e.ELDEventType}-${e.ELDEventCode}`
        : e.id,
    user_id: e.userId,
    client_driver_id: e.clientDriverId,
    terminal_serial: e.terminalSerial,
    event_time: msToUTCTimestamp(e.time),
    received_time: msToUTCTimestamp(e.receivedTime),
    sequence_number: e.sequenceNumber,
    status_id: e.statusId,
    latitude: e.latitude,
    longitude: e.longitude,
    cycle_rule_id: e.cycleRuleId,
    is_from_phone: e.isFromPhone ? 't' : 'f',
    address: postgresStringEscape(e.address),
    eld_event_type: e.ELDEventType,
    eld_event_code: e.ELDEventCode,
    odometer: Number(e.odometer),
    engine_hours: e.engineHours,
    malfunction_code: e.malfunctionCode,
    diagnostic_code: e.diagnosticCode,
    co_client_driver_ids: postgresArrayify(e.coClientDriverIds, true),
    notes: postgresStringEscape(e.notes),
    exception_id: e.exceptionId,
    timezone_id: e.timeZoneId || 0,
    phone_sequence_number: e.phoneSequenceNumber,
    cargo_type_id: e.cargoTypeId,
    log_signed: 'f',
    position_description_id: e.positionId || 0,
    firmware_version: e.firmwareVersion || 'NULL',
    trip_number: e.tripNumber || 0
    // X event_record_origin: null,
    // event_record_status: null,
    // vehicle_id: null,
    // shipping_doc_number: null
  }))
}

*/

/* -

function normalizeEditedEvents(events, startOfDay) {
  const firstEditedIdx = events.findIndex(e => e.edited)
  const lastEditedIdx = findLastIndex(e => e.edited, events)
  let startTime
  let endTime

  if (firstEditedIdx === 0) {
    // Endpoint expects the startTime to be one event prior to the first edited event.
    // If it's the first event, default to one second before the event as it is almost
    // impossible to have consecutive events on the second.
    startTime = startOfDay - 1000
  } else {
    startTime = events[firstEditedIdx - 1].time
  }

  if (lastEditedIdx === events.length - 1) {
    // The same logic applies here, but defaulting to the last second of the day
    endTime = startOfDay + 8.64e7 - 1000
  } else {
    endTime = events[lastEditedIdx + 1].time
  }

  const editedEvents = events.slice(firstEditedIdx, lastEditedIdx + 1)

  return {
    events: normalizeELDEvents(editedEvents, true),
    startTime,
    endTime
  }
}

*/

export default {
  fetchELDEvents({
    userId,
    startDateTime,
    endDateTime,
    limit,
    unassignedOnly,
    defaultTimeZone,
  }: {
    userId: FixMeAny
    startDateTime: FixMeAny
    endDateTime: FixMeAny
    limit: FixMeAny
    unassignedOnly: FixMeAny
    defaultTimeZone: FixMeAny
  }) {
    return apiCaller(
      'web_fetch_eld_events',
      {
        in_user_id: userId,
        in_start_event_timestamp: msToUserUTCTimestamp(startDateTime),
        in_end_event_timestamp: msToUserUTCTimestamp(endDateTime),
        in_limit: limit,
        in_unassigned_only: unassignedOnly ? 1 : 0,
      },
      { noX: true },
    ).then((res) => parseELDEvents(res.web_fetch_eld_events, defaultTimeZone))
  },

  fetchELDEventsByTerminal({
    userId,
    terminalSerials,
    startDateTime,
    endDateTime,
    defaultTimeZone,
  }: {
    userId: FixMeAny
    terminalSerials: FixMeAny
    startDateTime: FixMeAny
    endDateTime: FixMeAny
    defaultTimeZone: FixMeAny
  }) {
    const { startTS, endTS } = dateTimesToUTCTimestampRange(
      startDateTime,
      endDateTime,
      {
        expand: false,
      },
    )
    return apiCaller(
      'fetch_eld_events_by_terminals',
      {
        userId,
        terminalSerials,
        startTS,
        endTS,
      },
      { noX: true },
    ).then((res) => parseELDEvents(res.fetch_eld_events_by_terminals, defaultTimeZone))
  },

  fetchDriverELDEvents({
    userId,
    driverId,
    startDateTime,
    endDateTime,
    limit = 100,
    defaultTimeZone,
  }: {
    userId: FixMeAny
    driverId: FixMeAny
    startDateTime: FixMeAny
    endDateTime: FixMeAny
    limit?: number
    defaultTimeZone: FixMeAny
  }) {
    return apiCaller(
      'web_fetch_driver_eld_events',
      {
        in_user_id: userId,
        in_client_driver_id: driverId,
        in_start_event_timestamp: msToUserUTCTimestamp(startDateTime),
        in_end_event_timestamp: msToUserUTCTimestamp(endDateTime),
        in_limit: limit,
      },
      { noX: true },
    ).then((res) => parseELDEvents(res.web_fetch_driver_eld_events, defaultTimeZone))
  },

  reassignTrip({
    userId,
    terminalSerial,
    newDriverId,
    tripStartDateTime,
    tripEndDateTime,
  }: {
    userId: FixMeAny
    terminalSerial: FixMeAny
    newDriverId: FixMeAny
    tripStartDateTime: FixMeAny
    tripEndDateTime: FixMeAny
  }) {
    return apiCaller(
      'reassign_trip_immediate',
      {
        in_user_id: userId,
        in_terminal_serial: terminalSerial,
        in_client_driver_id: newDriverId,
        in_trip_start_timestamp: msToUTCTimestamp(tripStartDateTime),
        in_trip_end_timestamp: msToUTCTimestamp(tripEndDateTime),
      },
      { noX: true },
    )
  },

  unassignTrip({
    userId,
    terminalSerial,
    clientDriverId,
    eventId,
  }: {
    userId: FixMeAny
    terminalSerial: FixMeAny
    clientDriverId: FixMeAny
    eventId: FixMeAny
  }) {
    return apiCaller(
      'unassign_trip_immediate',
      {
        in_user_id: userId,
        in_terminal_serial: terminalSerial,
        in_client_driver_id: clientDriverId,
        in_eld_events_id: eventId,
      },
      { noX: true },
    ).then((res) => parseELDEvents(res.unassign_trip_immediate))
  },

  editPastEvents({
    userId,
    driverId,
    updatedEvents,
    startTime,
    endTime,
  }: {
    userId: FixMeAny
    driverId: FixMeAny
    updatedEvents: Array<FixMeAny>
    startTime: FixMeAny
    endTime: FixMeAny
  }) {
    const params = {
      userId,
      driverId,
      updatedEvents: updatedEvents.map((event) =>
        generateObjectWithReplacedProperties(event, [
          ['statusId', 'status_id'],
          ['isFromPhone', 'is_from_phone'],
          ['exceptionId', 'exception_id'],
          ['ELDEventType', 'eld_event_type'],
          ['ELDEventCode', 'eld_event_code'],
        ]),
      ),
      startTime,
      endTime,
    }

    return apiCaller('edit_past_events', params, { noX: true })
  },

  submitEldReport({
    driverId,
    email,
    dateFrom,
    dateTo,
  }: {
    driverId: FixMeAny
    email: FixMeAny
    dateFrom: FixMeAny
    dateTo: FixMeAny
  }) {
    return apiCaller('ct_fleet_submit_client_report', {
      reportData: {
        locale: 'en_ZA',
        password: '',
        parameters: '',
        repeatinterval: 'None',
        alternate_subject: '',
        deleteexpired: 'f',
        unique_filename: 't',
        deliverytype: 1,
        process_server: '',
        notification_email: email,
        reportformat: 'pdf',
        alternate_extension: null,
        priorityexecution: 1,
        generatetimestamp: msToUserUTCTimestamp(new Date()),
        end_user_id: '0',
        prompts: `user_id=0&client_user_id=0&client_driver_id=${driverId}&start_date=${dateFrom}&end_date=${dateTo}&eld_graph_url=&cycle_rule=0&state=U`,
        timeZone: '0',
        create_count: 0,
        reportno: 0,
        default_retry: 6,
        altdatasource: 'FLEET_USA',
        default_retry_interval: 10,
        reportname: '//fleet/ELD_Report_v1.10.rpt',
        alternate_email_body: '',
        execute_timestamp: msToUserUTCTimestamp(new Date()),
        reportstate: 0,
        prioritylevel: '130',
        retrycount: 6,
        deliveryname: 'ELD Log Report',
        deliveryto: email,
        zip_attachment: 'f',
      },
    })
  },

  transferELDLogs({
    clientDriverId,
    startEndDate,
    comment,
  }: {
    clientDriverId: string
    startEndDate: DateTimeRange
    comment: string
  }) {
    return apiCaller(
      'eld_send_logs_fmcsa',
      {
        clientDriverId,
        startDate: startEndDate.from,
        endDate: startEndDate.to,
        comment,
        isTest: ENV.NODE_ENV === 'production' ? false : true,
      },
      { noX: true },
    )
  },

  fetchEditRequests({
    userId,
    driverId,
    startTime,
    endTime,
  }: {
    userId: FixMeAny
    driverId: FixMeAny
    startTime: FixMeAny
    endTime: FixMeAny
  }) {
    const params = {
      user_id: userId,
      in_client_driver_id: driverId,
      in_start_ts: msToUTCTimestamp(startTime),
      in_end_ts: msToUTCTimestamp(endTime),
    }
    return apiCaller('ct_fetch_edit_requests', params, {
      noX: true,
    }).then((res) => parseEditRequests(res.ct_fetch_edit_requests))
  },

  cancelEditRequests({
    userId,
    driverId,
    editId,
  }: {
    userId: FixMeAny
    driverId: FixMeAny
    editId: FixMeAny
  }) {
    const params = {
      in_user_id: userId,
      in_client_driver_id: driverId,
      in_edit_id: editId,
      in_accept: 'f',
    }
    return apiCaller('handle_edit_requests', params, {
      noX: true,
    })
  },
}
