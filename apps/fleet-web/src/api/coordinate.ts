import type { MapsExtended } from 'src/types/extended/google-maps'
import type { FixMeAny } from 'src/types'

const RADIUS = 200
const MAX_RESULTS = 1

export const searchCoordinate = (
  { maps }: MapsExtended.MapObject,
  latlng: { lat: string; lng: string },
  successCB: (result: Array<google.maps.GeocoderResult>) => void,
  errorCB: () => void,
) => {
  const geocoder = new maps.Geocoder()

  geocoder.geocode(
    {
      location: {
        lat: Number(latlng.lat),
        lng: Number(latlng.lng),
      },
    },
    (result, status) => {
      if ((status === 'OK' || status === 'ZERO_RESULTS') && result) {
        return successCB(result)
      }

      return errorCB()
    },
  )
}

export const hereSearchCoordinate = (
  latlng: {
    lat: string | number
    lng: string | number
  },
  successCB: (result: FixMeAny) => void,
  errorCB: (array: []) => void,
  apiKey: string,
  language?: string,
) => {
  const lang = language ? `&lang=${language}` : ''
  const urlParams = `https://reverse.geocoder.ls.hereapi.com/6.2/reversegeocode.json?prox=${latlng.lat}%2C${latlng.lng}%2C${RADIUS}&mode=retrieveAddresses&maxresults=${MAX_RESULTS}&gen=9&apiKey=${apiKey}${lang}`

  return fetch(urlParams)
    .then((response) => response.json())
    .then((res) => {
      if (res && res.Response && res.Response.View.length > 0) {
        const result = res.Response.View[0].Result
        return successCB(result)
      } else {
        return errorCB([])
      }
    })
    .catch(() => errorCB([]))
}
