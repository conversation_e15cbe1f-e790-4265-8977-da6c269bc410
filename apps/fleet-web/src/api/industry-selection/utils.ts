import { ctIntl } from 'src/util-components/ctIntl'
import type { Industry, IndustryOption } from './types'
import { match } from 'ts-pattern'
import type { GetIndustrySelectionMetaDataResolved } from './queries'

export function getTranslatedIndustryDescription(industry: Industry) {
  return ctIntl.formatMessage({
    id: match(industry)
      .with('AGRICULTURE', () => 'industrySelectionModal.industry.agriculture')
      .with(
        'BANKS_FINANCE_HOUSES',
        () => 'industrySelectionModal.industry.bankOrFinanceHouses',
      )
      .with('CAR_RENTAL', () => 'industrySelectionModal.industry.carRental')
      .with('CONSTRUCTION', () => 'industrySelectionModal.industry.construction')
      .with('EDUCATION', () => 'industrySelectionModal.industry.education')
      .with('FIELD_SERVICE', () => 'industrySelectionModal.industry.fieldService')
      .with('FOOD_BEVERAGE', () => 'industrySelectionModal.industry.foodAndBeverage')
      .with('GOVERNMENT', () => 'industrySelectionModal.industry.government')
      .with('HEALTHCARE', () => 'industrySelectionModal.industry.healthcare')
      .with('LOGISTICS', () => 'industrySelectionModal.industry.logistics')
      .with('MANUFACTURING', () => 'industrySelectionModal.industry.manufacturing')
      .with('MEDICAL', () => 'industrySelectionModal.industry.medical')
      .with('MINING', () => 'industrySelectionModal.industry.mining')
      .with('OTHER', () => 'industrySelectionModal.industry.other')
      .with(
        'PASSENGER_TRANSPORT',
        () => 'industrySelectionModal.industry.passengerTransport',
      )
      .with('PERSONAL_USE', () => 'industrySelectionModal.industry.personalUse')
      .with('REFRIGERATION', () => 'industrySelectionModal.industry.refrigeration')
      .with(
        'SOFTWARE_TECHNOLOGY',
        () => 'industrySelectionModal.industry.softwareAndTechnology',
      )
      .with('TOURISM_HOTELS', () => 'industrySelectionModal.industry.tourismAndHotels')
      .with(
        'UTILITIES_ENERGY',
        () => 'industrySelectionModal.industry.utilitiesAndEnergy',
      )
      .with(
        'VEHICLE_DEALERSHIP',
        () => 'industrySelectionModal.industry.vehicleDealerships',
      )
      .with('WHOLESALE', () => 'industrySelectionModal.industry.wholesale')
      .with('RETAIL', () => 'industrySelectionModal.industry.retail')
      .exhaustive(),
  })
}

export function getIndustriesDropdownOptions(
  industries: GetIndustrySelectionMetaDataResolved['industries'],
) {
  return industries
    .map(
      (industry): IndustryOption => ({
        id: industry.id,
        label: getTranslatedIndustryDescription(industry.name),
        name: industry.name,
      }),
    )
    .sort((a, b) => a.label.localeCompare(b.label))
}
