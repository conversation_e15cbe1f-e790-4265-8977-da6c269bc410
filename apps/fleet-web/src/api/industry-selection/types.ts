import type { Opaque } from 'type-fest'
import { z } from 'zod'

export type CompanyIndustryId = Opaque<'CompanyIndustryId', number>

export type IndustryOption = {
  id: CompanyIndustryId
  label: string
  name: Industry
}

export const industrySchema = z.enum([
  'AGRICULTURE',
  'BANKS_FINANCE_HOUSES',
  'CAR_RENTAL',
  'CONSTRUCTION',
  'EDUCATION',
  'FIELD_SERVICE',
  'FOOD_BEVERAGE',
  'GOVERNMENT',
  'HEALTHCARE',
  'LOGISTICS',
  'MANUFACTURING',
  'MEDICAL',
  'MINING',
  'OTHER',
  'PASSENGER_TRANSPORT',
  'PERSONAL_USE',
  'REFRIGERATION',
  'RETAIL',
  'SOFTWARE_TECHNOLOGY',
  'TOURISM_HOTELS',
  'UTILITIES_ENERGY',
  'VEHICLE_DEALERSHIP',
  'WHOLESALE',
] as const)

export type Industry = z.infer<typeof industrySchema>

export declare namespace GetIndustrySelectionMetaData {
  type ApiParams = void
  type ApiOutput = {
    lastTimeUserDismissedIndustrySelectionUnixInSeconds: number | null
    userSelectedIndustry: Industry | null
    industries: Array<{
      company_industry_id: string
      company_industry: Industry
      description: string
    }>
  }
}
