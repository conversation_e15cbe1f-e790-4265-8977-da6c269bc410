import apiCaller from 'api/api-caller'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  makeMutationErrorHandlerWithToast,
  makeQueryErrorHandlerWithToast,
} from 'api/helpers'
import { isEmpty } from 'lodash'
import {
  type CompanyIndustryId,
  type GetIndustrySelectionMetaData,
  industrySchema,
  type Industry,
} from './types'
import { minutesToMs } from 'src/util-functions/functional-utils'
import { safeParseFromZodSchema } from 'src/util-functions/zod-utils'
import type { PromiseResolvedType } from 'src/types'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'
import { createQuery } from 'src/util-functions/react-query-utils'

export type GetIndustrySelectionMetaDataResolved = PromiseResolvedType<
  typeof getIndustrySelectionMetaData
>

async function getIndustrySelectionMetaData() {
  const res = (await apiCaller('ct_fleet_get_industry_selection_meta_data', undefined, {
    noX: true,
  })) as GetIndustrySelectionMetaData.ApiOutput

  return {
    lastTimeUserDismissedIndustrySelectionUnixInSeconds:
      res.lastTimeUserDismissedIndustrySelectionUnixInSeconds,
    userSelectedIndustry: isEmpty(res.userSelectedIndustry)
      ? null
      : res.userSelectedIndustry,
    industries: Array_filterMap(
      res.industries,
      ({ company_industry, company_industry_id }, { RemoveSymbol }) => {
        const parsedIndustryName = safeParseFromZodSchema(
          industrySchema,
          company_industry,
          { defaultValue: () => '__UNKNOWN__' as const },
        )

        if (parsedIndustryName === '__UNKNOWN__') {
          // Someone might have added a new industry to the database, but the FE is still not ready to handle it.
          return RemoveSymbol
        }

        return {
          id: parseInt(company_industry_id, 10) as unknown as CompanyIndustryId,
          name: parsedIndustryName,
        }
      },
    ),
  }
}

export const industrySelectionMetaDataQuery = () =>
  createQuery({
    queryKey: ['useIndustrySelectionMetaData'] as const,
    queryFn: getIndustrySelectionMetaData,
    staleTime: minutesToMs(20),
  })

export function useIndustrySelectionMetaData() {
  return useQuery({
    ...industrySelectionMetaDataQuery(),
    /* Refetch interval is useful for users that are currently logged in and don't ever log out (e.g - Smart TVs).
      This guarantees that we still call this endpoint and verify if we need to show the industry modal for these types of users */
    refetchInterval: (query) => {
      const { data } = query.state
      if (data === undefined) {
        return false
      }
      return data.userSelectedIndustry === null ? minutesToMs(30) : false
    },
    ...makeQueryErrorHandlerWithToast(),
  })
}

export function useDeferIndustrySelectionMutation() {
  return useMutation({
    mutationFn: async () =>
      await apiCaller('ct_fleet_defer_request_industry', {}, { noX: true }),
    ...makeMutationErrorHandlerWithToast(),
  })
}

export function useSetUserIndustryMutation() {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: async ({ industry }: { industry: Industry }) =>
      await apiCaller(
        'ct_fleet_set_industry',
        { company_industry: industry },
        { noX: true },
      ),
    onSuccess() {
      queryClient.invalidateQueries(industrySelectionMetaDataQuery())
    },
    ...makeMutationErrorHandlerWithToast(),
  })
}
