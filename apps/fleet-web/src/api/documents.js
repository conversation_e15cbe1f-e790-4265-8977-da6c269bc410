import apiCaller from './api-caller'
import { utcTimestampToMs, msToUserUTCTimestamp } from 'cartrack-utils'

const documentCategories = [
  'Bill of Lading',
  'Profile Picture',
  'Accident Photo',
  'Citation',
  'Scale Ticket',
  'Other',
  'Fuel Receipt',
]

function parseDocuments(rawDocuments) {
  return rawDocuments
    ? rawDocuments
        .map((d) => ({
          id: d.out_attachment_id,
          updateTime: utcTimestampToMs(d.out_update_ts),
          driverName: d.out_driver_name,
          category: documentCategories[d.out_attachment_category_id - 1],
          referenceNumber: d.out_reference_number,
          path: d.out_path,
        }))
        .filter((d) => d.category !== 'Profile Picture')
    : []
}

export default {
  fetchDocuments(startTime, endTime) {
    return apiCaller('ct_fleet_get_documents_user', {
      from_date: msToUserUTCTimestamp(startTime),
      to_date: msToUserUTCTimestamp(endTime),
    }).then((res) => parseDocuments(res.ct_fleet_get_documents))
  },
}
