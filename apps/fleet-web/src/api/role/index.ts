import { apiCallerNoX } from '../api-caller'
import type { FixMeAny } from 'src/types'
import type { RoleProfileSchema } from 'src/modules/admin/edit-role/profile'
import type {
  FetchRoles,
  FetchRoleInfo,
  UpdateRoleInfo,
  CreateRole,
  UpdateRole,
} from './types'

const generateRoleStatus = ({
  clientUserStatus,
}: {
  clientUserStatus: string | null
}): FetchRoles.Status =>
  (clientUserStatus || '').indexOf('10') > 0 ? 'active' : 'deactivated'

export function parseFetchRoleInfoResponse(rawUser: FetchRoleInfo.ApiOutput) {
  const user = {
    id: rawUser.client_user_id,
    profile: {
      username: rawUser.user_name,
    },
  }

  return {
    user,
  }
}

export function parseRolesList(rawUsers: Array<FetchRoles.ApiOutput>) {
  return rawUsers.map((rawUser) => ({
    id: rawUser.client_user_id,
    username: rawUser.user_name,
    status: generateRoleStatus({ clientUserStatus: rawUser.client_user_statuses }),
  }))
}

export type FetchUsersReturnType = ReturnType<typeof parseRolesList>

export default {
  fetchRoles() {
    return apiCallerNoX('ct_fleet_get_role').then((res: FixMeAny) =>
      parseRolesList(res.ct_fleet_get_user || []),
    )
  },

  fetchRoleInfo(userId: string) {
    return apiCallerNoX('ct_fleet_client_role_info', {
      client_user_id: userId,
    }).then((res: FixMeAny) =>
      parseFetchRoleInfoResponse(res.ct_fleet_client_user_info),
    )
  },

  createNewRole(formData: RoleProfileSchema) {
    return apiCallerNoX<CreateRole.ApiOutput>('ct_fleet_create_role', {
      data: [{ user_name: formData.username }],
    }).then((res) => res)
  },

  updateRole(
    userId: string,
    {
      profile,
      vehicles,
    }: {
      profile?: RoleProfileSchema
      vehicles?: UpdateRoleInfo.VehicleAssigned | true | null
    },
  ) {
    function normalizeProfile(info: RoleProfileSchema | undefined): {
      general: Record<string, any> | null
    } {
      if (info === undefined) {
        return { general: null }
      }

      return {
        general: {
          user_name: info.username,
        },
      }
    }

    return apiCallerNoX<UpdateRole.ApiOutput>('ct_fleet_update_role', {
      client_user_id: userId,
      ...normalizeProfile(profile),
      vehicles: vehicles ? vehicles : null,
    }).then((res) => res)
  },
}
