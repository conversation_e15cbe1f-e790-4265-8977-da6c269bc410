import type { BooleanALaCartrack1, BooleanALaCartrack2 } from 'src/types/global'
import type { parseFetchRoleInfoResponse, parseRolesList } from '.'

type GeofencesPermissions = {
  allow_subuser_tosee_mainuser_geofences: BooleanALaCartrack1
  allow_subuser_toedit_mainuser_geofences: BooleanALaCartrack1
  allow_mainuser_tosee_subuser_geofences: BooleanALaCartrack1
  allow_mainuser_toedit_subuser_geofences: BooleanALaCartrack1
}

type POIPermissions = {
  allow_subuser_tosee_mainuser_poi: BooleanALaCartrack1
  allow_subuser_toedit_mainuser_poi: BooleanALaCartrack1
  allow_mainuser_tosee_subuser_poi: BooleanALaCartrack1
  allow_mainuser_toedit_subuser_poi: BooleanALaCartrack1
}

export declare namespace Api {
  type RoleBase = {
    user_id: string
    client_user_id: string
    user_name: string
    client_user_statuses: string
  }
}

// Endpoints
export declare namespace FetchRoles {
  type ApiOutput = Api.RoleBase & {
    is_locked: BooleanALaCartrack1
  }
  type Status = 'active' | 'deactivated'
  type Return = ReturnType<typeof parseRolesList>
}
export declare namespace FetchRoleInfo {
  type ApiOutput = Api.RoleBase &
    GeofencesPermissions &
    POIPermissions & {
      user_role_id: Array<string>
      is_locked: BooleanALaCartrack1
    }

  type Return = ReturnType<typeof parseFetchRoleInfoResponse>
  type BaseUser = FetchRoleInfo.Return['user']
}

export declare namespace UpdateRoleInfo {
  type ApiInput = GeofencesPermissions &
    POIPermissions & {
      user_id: string
      client_user_id: string
      user_name: string
      cell_no: string
      email_address: string
      buy_sms_allowed: BooleanALaCartrack2
      shared_reminders_allowed: BooleanALaCartrack2
      vehicles: []
      canSeeAllVehicles: BooleanALaCartrack2
      language_id: string
      name: string
    }

  type VehicleAssigned = {
    all: boolean
    group: Array<string>
  }
}

export type CreateOrUpdateRoleHandledError = {
  roleWithPropertiesAlreadyExists: Array<'name'> | null
}

export declare namespace CreateRole {
  type HandledError = CreateOrUpdateRoleHandledError

  type ApiOutput =
    | {
        success: true
        client_user_id: string
      }
    | ({ success?: undefined } & HandledError)
}

export declare namespace UpdateRole {
  type HandledError = {
    error: CreateOrUpdateRoleHandledError
  }

  type ApiOutput = { success: true } | ({ success: false | undefined } & HandledError)
}
