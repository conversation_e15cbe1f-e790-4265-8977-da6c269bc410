import apiCaller from './api-caller'
import { msToUserUTCTimestamp } from 'cartrack-utils'
import { connectedCtIntl } from 'src/util-components/connectedCtIntl'
import type { AppState } from 'src/root-reducer'
import type { PromiseResolvedType } from 'src/types'

function formatMessage(alarm: Record<string, any>) {
  return `Vehicle: ${alarm.registration}, Sensor: ${alarm.sensor_name}, Type: ${alarm.sensor_type}, Event: ${alarm.alarm} at ${alarm.out_location_description} (${alarm.latitude}, ${alarm.longitude})`
}

function parseAlarms(result: FetchAlarms.ApiOutput, id: string) {
  return result.ct_fleet_get_alarm_information_data
    .map((a) => ({
      id: a.id,
      eventTs: connectedCtIntl.formatDateWithHourMinute(a.alarm_ts),
      notificationMsg: formatMessage(a),
      groupDescription: `${a.sensor_type}/${a.sensor_name} (Alarm)`,
      registration: a.registration,
      vehicleId: a.vehicle_id,
      type: 'alarm' as const,
    }))
    .filter((a) => !id || id === 'All' || id === a.vehicleId)
}

const alarmsApi = {
  fetchAlarms(
    storeState: AppState,
    startTs: Date | string,
    endTs: Date | string,
    vehicleId: string,
  ) {
    const params = {
      p_sensor_type_id: '0',
      p_vehicle_sensors_id: '0',
      p_alarm_priority_id: '0',
      p_status: 0,
      p_edate: msToUserUTCTimestamp(endTs, undefined, storeState),
      p_sdate: msToUserUTCTimestamp(startTs, undefined, storeState),
    }
    return apiCaller('ct_fleet_get_alarm_information_data', {
      params,
    }).then((res: FetchAlarms.ApiOutput) => parseAlarms(res, vehicleId))
  },
}

export default alarmsApi

declare namespace FetchAlarms {
  type ApiOutput = {
    ct_fleet_get_alarm_information_data: Array<Record<string, any>>
  }
}

export type FetchAlarmsResolved = PromiseResolvedType<typeof alarmsApi.fetchAlarms>
