import { isEmpty, isNil } from 'lodash'
import { ctIntl } from 'cartrack-ui-kit'

export type NamedError = Error & { name?: string }
type CallerOptions = { isDownload?: boolean; showError?: boolean }

// Can be used to identify a specific error (e.g in a try/catch statement in sagas)
export const ERROR_TYPES = {
  NOT_AUTHENTICATED: 'NOT_AUTHENTICATED',
  NOT_AUTHORIZED: 'NOT_AUTHORIZED',
  HANDLED: 'HANDLED',
  UNHANDLED: 'UNHANDLED',
}

const createError = (message: string, name?: string): NamedError => {
  const error = new Error(message)
  if (!isEmpty(name) && !isNil(name)) {
    error.name = name
  }

  return error
}

function handleJsonResponse(res: Response, options: CallerOptions) {
  if (options.isDownload) {
    return res.blob()
  }

  return res.json().then((json) => {
    if (res.ok) {
      if (json.success) {
        return json
      }

      if (json.isAuthenticated === false) {
        // The value of this message can not be modified as it it being used in a compare function in root-saga.js:102
        throw createError(
          'Your session has been invalidated. Please log in again.',
          ERROR_TYPES.NOT_AUTHENTICATED,
        )
      }

      if (json.isAuthorized === false) {
        throw createError(
          'You are not authorized to access this content or perform this action.',
          ERROR_TYPES.NOT_AUTHORIZED,
        )
      }

      throw createError(
        isEmpty(json.errorMessage)
          ? 'The operation you requested has failed.'
          : json.errorMessage,
        ERROR_TYPES.HANDLED,
      )
    }

    throw createError(
      isEmpty(json.errorMessage)
        ? 'We are experiencing a technical error.'
        : json.errorMessage,
      ERROR_TYPES.UNHANDLED,
    )
  })
}

export const buildObjectFromQueryString = (queryString: string) =>
  queryString
    .substr(1, queryString.length)
    .split('&')
    .map((item) => {
      const data = item.split('=')
      return {
        [decodeURIComponent(data[0])]: decodeURIComponent(data[1]),
      }
    })
    .reduce((previous, current) => ({ ...previous, ...current }), {})

export function getCostsAPIFrom(
  settings = JSON.parse(sessionStorage.getItem('userSettings') || '{}'),
) {
  // Checks for ''/null/undefined
  if (!isEmpty(settings.costsApi)) {
    return settings.costsApi
  }

  const tryLocal = JSON.parse(localStorage.getItem('userSettings') || '{}')

  if (!isEmpty(tryLocal.costsApi)) {
    return tryLocal.costsApi
  }

  // returning undefined just to make the code cleaner
  return undefined
}

/*function timeout(ms: number, promise: Promise<FixMeAny>) {
  return new Promise((resolve, reject) => {
    window.setTimeout(() => {
      reject(new Error('timeout'))
    }, ms)
    promise.then(resolve, reject)
  })
}*/

export function caller(
  endpoint: string,
  methodName: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
  formData: any = null,
  options: CallerOptions = { isDownload: false, showError: true },
  fetchApi = getCostsAPIFrom(),
): Promise<any> {
  return fetch(`${fetchApi}/index.php?comms=${endpoint}`, {
    method: methodName,
    body: isNil(formData) ? null : JSON.stringify(formData),
    credentials: 'include',
    mode: 'cors',
  })
    .then((res) => handleJsonResponse(res, options))
    .catch((error) => {
      if (options.showError) {
        throw createError(
          error.message === 'Failed to fetch'
            ? 'Check your network connection.'
            : ctIntl.formatMessage({
                id: error.message,
              }),
          error.name,
        )
      }
    })
}
