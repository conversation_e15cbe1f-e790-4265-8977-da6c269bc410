import { caller } from './caller'

// Helpers
function normalizeDriverLeave(driverLeave) {
  return {
    driver_id: driverLeave.driverId || '',
    driver_leave_type: driverLeave.driverLeaveType || '',
    driver_leave_type_id: driverLeave.driverLeaveTypeId || '',
    leave_id: driverLeave.leaveId || '',
    leave_period_start: driverLeave.leavePeriodStart || '',
    leave_period_end: driverLeave.leavePeriodEnd || '',
  }
}

function parseDriverLeaves(driverLeaves) {
  return driverLeaves.map((driverLeave) => ({
    driverId: driverLeave.driver_id,
    driverLeaveType: driverLeave.driver_leave_type,
    driverLeaveTypeId: driverLeave.driver_leave_type_id,
    leaveId: driverLeave.leave_id,
    leavePeriodStart: driverLeave.leave_period_start,
    leavePeriodEnd: driverLeave.leave_period_end,
  }))
}

// Map driver leave type id with name ({ id: name })
function parseDriverLeaveTypes(driverLeaveTypes) {
  return driverLeaveTypes.reduce((acc, driverLeaveType) => {
    acc[driverLeaveType.driver_leave_type_id] = driverLeaveType.leave_type

    return acc
  }, {})
}

export default {
  fetchDriverLeave(driverId) {
    return caller(
      `drivers/driverLeaveJSON.php?action=read&driver=${driverId}`,
      'GET',
      null,
      { showError: false },
    ).then((res) =>
      res && res.totalCount > 0 ? parseDriverLeaves(res.objectList) : [],
    )
  },

  fetchDriverLeaveTypes() {
    return caller('setupGeneral/driverLeaveTypeJSON.php?action=read').then((res) =>
      res.totalCount > 0 ? parseDriverLeaveTypes(res.objectList) : {},
    )
  },

  createDriverLeave(driverLeaveData) {
    return caller(
      'drivers/driverLeaveJSON.php?action=create',
      'POST',
      normalizeDriverLeave(driverLeaveData),
    )
  },

  updateDriverLeave(driverLeaveData) {
    return caller(
      'drivers/driverLeaveJSON.php?action=update',
      'POST',
      normalizeDriverLeave(driverLeaveData),
    )
  },

  deleteDriverLeave(leaveId) {
    return caller('drivers/driverLeaveJSON.php?action=delete', 'POST', {
      leave_id: leaveId,
    })
  },
}
