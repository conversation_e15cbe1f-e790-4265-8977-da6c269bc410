import { caller } from './caller'

export default {
  getDriverCosts(driver) {
    return caller('drivers/driverCostJSON.php?action=read&driver=' + driver).then(
      (data) => data,
    )
  },

  updateDriverCosts(json) {
    return caller('drivers/driverCostJSON.php?action=update', 'POST', json).then(
      (data) => data,
    )
  },

  deleteDriverCosts(json) {
    return caller('drivers/driverCostJSON.php?action=delete', 'POST', json).then(
      (data) => data,
    )
  },

  readDriverCosts(json) {
    return caller('drivers/driverCostJSON.php?action=read', 'POST', json).then(
      (data) => data,
    )
  },

  createDriverCosts(json) {
    return caller('drivers/driverCostJSON.php?action=create', 'POST', json).then(
      (data) => data,
    )
  },
}
