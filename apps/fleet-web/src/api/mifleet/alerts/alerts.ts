import { camelCase } from 'lodash'
import { buildQueryStringFromObject } from 'src/api/utils'
import type { CostsAlert, CostsAlertNotification } from 'src/types/api/mifleet/alerts'
import { isTrue } from 'src/util-functions/validation'
import { connectedCtIntl } from 'src/util-components/connectedCtIntl'
import type { FixMeAny } from 'src/types'
import { caller } from '../caller'

type SnakeCaseCostsAlert = {
  alert_configuration_id: string
  alert_id: string
  company_id: string
  document_id: string
  document_line_id: string
  driver_id: string
  expiration_date: string
  expiration_odometer: string
  is_dismissed: boolean
  is_read: boolean
  message: string
  object_id: string
  remaining_days: string
  remaining_odometer: string
  user_id: string
  warning_date: string
  warning_type: string
  warning_type_id: string
  vehicle_id: string
}

type APICostsAlertNotification = {
  alert_id: string | null
  alert_configuration_id: string | null
  document_id: string | null
  warning_type_id: string | null
  company_id: string | null
  message: string | null
  user_id: string | null
  driver_id: string | null
  vehicle_id: string | null
  warning_date: string | null
  warning_type: string | null
}

const parseCostsAlertNotification = (
  costsAlertsNotification: APICostsAlertNotification,
) =>
  Object.keys(costsAlertsNotification).reduce((acc, key) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    ;(acc[camelCase(key) as keyof CostsAlertNotification] = (
      costsAlertsNotification as any
    )[key]) || ''

    return acc
  }, {} as CostsAlertNotification)

const parseCostsAlertsNotifications = (
  costsAlertsNotifications: Array<APICostsAlertNotification>,
) => (costsAlertsNotifications || []).map(parseCostsAlertNotification)

// Costs Alerts
const normalizeCostsAlert = (costsAlert: CostsAlert) => ({
  alert_id: costsAlert.alertId || '',
  alert_configuration_id: costsAlert.alertConfigurationId || '',
  message: costsAlert.message || '',
  company_id: costsAlert.companyId || '',
  is_dismissed: costsAlert.isDismissed || false,
  is_read: costsAlert.isRead || false,
  warning_date: costsAlert.warningDate ? costsAlert.warningDate.date : '',
  warning_type_id: costsAlert.warningTypeId,
  vehicle_id: costsAlert.vehicleId,
  warning_type: costsAlert.warningType,
})

const parseCostsAlert = (costsAlert: SnakeCaseCostsAlert) => {
  const camelCaseCostsAlert: {
    [key: string]: string | boolean
  } = {}

  Object.keys(costsAlert).map(
    (key: string) => (camelCaseCostsAlert[camelCase(key)] = (costsAlert as any)[key]),
  )

  return {
    ...camelCaseCostsAlert,
    expirationDate: connectedCtIntl.formatDateWithHourMinute(
      costsAlert.expiration_date,
    ),
    warningDate: connectedCtIntl.formatDateWithHourMinute(costsAlert.warning_date),
    isDismissed: isTrue(costsAlert.is_dismissed),
    isRead: isTrue(costsAlert.is_read),
  }
}

const parseCostsAlerts = (res: {
  alerts: Array<SnakeCaseCostsAlert>
  drivers: FixMeAny
  vehicles: FixMeAny
}) => ({
  alerts: (res.alerts || []).map(parseCostsAlert),
  drivers: res.drivers,
  vehicles: res.vehicles,
})

export default {
  fetchCostsAlerts({
    startDate,
    endDate,
    onlyActive = false,
  }: {
    startDate: string
    endDate: string
    onlyActive?: boolean
  }) {
    return caller(
      `alerts/alertJSON.php?action=read&${buildQueryStringFromObject({
        startDate: startDate,
        endDate: endDate,
        onlyActive,
      })}`,
    ).then((res) => (res.totalCount > 0 ? parseCostsAlerts(res.objectList[0]) : []))
  },

  updateCostsAlert(costsAlertData: CostsAlert) {
    return caller(
      'alerts/alertJSON.php?action=update',
      'POST',
      normalizeCostsAlert(costsAlertData),
    ).then((res) => (res.totalCount > 0 ? parseCostsAlert(res.objectList) : {}))
  },

  dismissCostsAlert(idToDismiss: string) {
    return caller('alerts/alertJSON.php?action=delete', 'POST', {
      action_type: 'dismiss',
      alerts: [
        {
          alert_id: idToDismiss,
        },
      ],
    }).then((res) => res)
  },

  markAlertsNotificationsAsRead(alertIds: Array<{ alert_id: string }>) {
    return caller('alerts/alertJSON.php?action=delete', 'POST', {
      action_type: 'read',
      alerts: alertIds,
    }).then((res) => res)
  },

  fetchCostsAlertsNotifications() {
    return caller(
      `alerts/alertJSON.php?action=read&${buildQueryStringFromObject({
        isNotificationList: true,
      })}`,
    ).then((res) =>
      res.objectList && res.objectList.alerts
        ? parseCostsAlertsNotifications([res.objectList[0].alerts])
        : [],
    )
  },
}
