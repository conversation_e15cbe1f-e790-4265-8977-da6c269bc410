import { caller } from '../caller'
import { camelCase, snakeCase, find } from 'lodash'
import type {
  CostsActive<PERSON>lert,
  CostsActiveAlertUser,
  CostsActiveAlertDriver,
  CostsActiveAlertVehicle,
  CostsActiveAlertDriverGroups,
  CostsActiveAlertVehicleGroups,
  CostsAlertTypes,
} from 'src/types/api/mifleet/alerts'
import type { ValueOf, FixMeAny } from 'src/types'
import { isTrue, isIterable } from 'cartrack-utils'

type SnakeCaseCostsActiveAlertObjects = {
  alert_configuration_object_id: string
  alert_configuration_id: string
  user_id: string
  vehicle_id: string
  vehicle_group_id: string
  driver_id: string
  fleet_user_id: string
  driver_group_id: number
}

type SnakeCaseCostsActiveAlertEmail = {
  alert_configuration_email_id: number
  alert_configuration_id: number
  user_id: number
  driver_id: number
  email: string
  recurrence_days: number
}

type SnakeCaseCostsActiveAlertPhone = {
  alert_configuration_email_id: number
  alert_configuration_id: number
  user_id: number
  driver_id: number
  phone: string
  recurrence_days: number
}

type SnakeCaseCostsActiveAlert = {
  alert_configuration_id: string
  alert_name: string
  company_id: number
  warning_type_id: number
  warning_type: string
  is_warning_per_user: boolean
  is_warning_per_vehicle: boolean
  is_warning_per_driver: boolean
  is_due_days_warning: boolean
  is_due_odometer_warning: boolean
  is_warning_for_all: boolean
  due_remaining_days: number
  due_remaining_odometer: string
  // This is not snake case but it is what it comes from the backend. There is no consistency...
  alertObjects: Array<SnakeCaseCostsActiveAlertObjects>
  alertEmails: Array<SnakeCaseCostsActiveAlertEmail>
  alertPhones: Array<SnakeCaseCostsActiveAlertPhone>
}

type BaseCostsAlertTypes = {
  warning_type_id: string
  warning_group_type_id: string
  warning_type: string
  is_deleted: string
  is_warning_per_user: string
  is_warning_per_vehicle: string
  is_warning_per_driver: string
  is_due_days_warning: string
  is_due_odometer_warning: string
}

// Helpers
const buildActiveAlertWarnedEntity = (
  alertObjectKey: keyof SnakeCaseCostsActiveAlertObjects,
  activeAlertObjects: Array<SnakeCaseCostsActiveAlertObjects>,
  alertType: 'users' | 'drivers' | 'vehicles',
  activeAlertsNameSource?: FixMeAny,
) => {
  if (alertType === 'users') {
    return activeAlertObjects
      .map((activeAlertObject) => {
        const user = find(
          activeAlertsNameSource,
          (u) => u.fleet_user_id === activeAlertObject[alertObjectKey],
        )
        return user?.username
      })
      .join(', ')
  }

  if (alertType === 'drivers') {
    return activeAlertObjects
      .map((activeAlertObject) => {
        const driver = find(
          activeAlertsNameSource,
          (u) => u.driver_id === activeAlertObject[alertObjectKey],
        )
        if (driver) {
          return driver.short_name
        } else {
          const group =
            find(
              activeAlertsNameSource,
              (u) => u.driver_group_id === activeAlertObject.driver_group_id,
            ) || {}

          return group.driver_group
        }
      })
      .join(', ')
  }

  if (alertType === 'vehicles') {
    return activeAlertObjects
      .map((activeAlertObject) => {
        const vehicle = find(
          activeAlertsNameSource,
          (u) => u.vehicle_id === activeAlertObject[alertObjectKey],
        )
        if (vehicle) {
          return vehicle.plate
        } else {
          const group =
            find(
              activeAlertsNameSource,
              (u) => u.vehicle_group_id === activeAlertObject.vehicle_group_id,
            ) || {}

          return group.vehicle_group
        }
      })
      .join(', ')
  }

  return activeAlertObjects
    .map((activeAlertObject) => activeAlertObject[alertObjectKey])
    .join(', ')
}

const normalizeAlertObjects = <O extends Record<string, any>>(alertObjects: Array<O>) =>
  alertObjects
    ? alertObjects.map((alertObject) => {
        const normalizedObject: {
          [key: string]: string | number
        } = {}

        Object.keys(alertObject).forEach((key: string) => {
          normalizedObject[snakeCase(key)] = (alertObject as any)[key]
        })

        return normalizedObject
      })
    : []

const parseCostsAlertTypes = (
  costsAlertTypes: Array<BaseCostsAlertTypes>,
): CostsAlertTypes =>
  costsAlertTypes.reduce((acc, curr) => {
    const description = camelCase(curr.warning_type)
      .replace('warningType', '')
      .replace(/([A-Z])/g, ' $1')
      .trim()
    const type = description.replace(/ .*/, '') as ValueOf<CostsAlertTypes>['type']

    acc[curr.warning_type_id] = {
      description,
      warningTypeId: curr.warning_type_id,
      type,
    }
    return acc
  }, {} as CostsAlertTypes)

const normalizeCostsActiveAlert = (costsActiveAlert: CostsActiveAlert) => ({
  alert_configuration_id: costsActiveAlert.alertConfigurationId || '',
  alert_name: costsActiveAlert.alertName || '',
  company_id: costsActiveAlert.companyId || '',
  warning_type_id: costsActiveAlert.warningTypeId || '',
  warning_type: costsActiveAlert.warningType || '',
  is_warning_per_user: costsActiveAlert.isWarningPerUser || false,
  is_warning_per_vehicle: costsActiveAlert.isWarningPerVehicle || false,
  is_warning_per_driver: costsActiveAlert.isWarningPerDriver || false,
  is_due_days_warning: costsActiveAlert.isDueDaysWarning || false,
  is_due_odometer_warning: costsActiveAlert.isDueOdometerWarning || false,
  is_warning_for_all: costsActiveAlert.isWarningForAll || false,
  due_remaining_days: costsActiveAlert.dueRemainingDays ?? '',
  due_remaining_odometer: costsActiveAlert.dueRemainingOdometer || '',
  alertObjects: normalizeAlertObjects(costsActiveAlert.alertObjects),
  alertEmails: normalizeAlertObjects(costsActiveAlert.alertEmails),
  alertPhones: normalizeAlertObjects(costsActiveAlert.alertPhones),
})

const parseCostsActiveAlert = (
  costsActiveAlert: SnakeCaseCostsActiveAlert,
  parsedUsers: Array<CostsActiveAlertUser>,
  parsedDrivers: Array<CostsActiveAlertDriver>,
  parsedVehicles: Array<CostsActiveAlertVehicle>,
  driverGroups: Array<CostsActiveAlertDriverGroups>,
  vehicleGroups: Array<CostsActiveAlertVehicleGroups>,
): CostsActiveAlert => {
  // Build warnedEntity
  const warnedEntities = {
    users: '',
    vehicles: '',
    drivers: '',
  }

  if (costsActiveAlert.is_warning_per_user) {
    warnedEntities.users = costsActiveAlert.is_warning_for_all
      ? 'All Users'
      : buildActiveAlertWarnedEntity(
          'fleet_user_id',
          costsActiveAlert.alertObjects,
          'users',
          parsedUsers,
        )
  } else if (costsActiveAlert.is_warning_per_vehicle) {
    warnedEntities.vehicles = costsActiveAlert.is_warning_for_all
      ? 'All Vehicles'
      : buildActiveAlertWarnedEntity(
          'vehicle_id',
          costsActiveAlert.alertObjects,
          'vehicles',
          [...parsedVehicles, ...vehicleGroups],
        )
  } else if (costsActiveAlert.is_warning_per_driver) {
    warnedEntities.drivers = costsActiveAlert.is_warning_for_all
      ? 'All Drivers'
      : buildActiveAlertWarnedEntity(
          'driver_id',
          costsActiveAlert.alertObjects,
          'drivers',
          [...parsedDrivers, ...driverGroups],
        )
  }

  // Convert object keys to camelcase
  const camelCaseCostsActiveAlert: {
    [key: string]: FixMeAny
  } = {}

  Object.keys(costsActiveAlert).map((key: string) => {
    camelCaseCostsActiveAlert[camelCase(key)] = (costsActiveAlert as any)[key]

    if (['alertEmails', 'alertObjects', 'alertPhones'].includes(key)) {
      ;(
        costsActiveAlert[
          key as 'alertEmails' | 'alertObjects' | 'alertPhones'
        ] as Array<
          | SnakeCaseCostsActiveAlertPhone
          | SnakeCaseCostsActiveAlertEmail
          | SnakeCaseCostsActiveAlertObjects
        >
      ).map((alertObject: FixMeAny, index) => {
        Object.keys(alertObject).map((alertObject: string) => {
          if ((costsActiveAlert as FixMeAny)[key][index][alertObject]) {
            const parsedAlertObjectValue = (costsActiveAlert as FixMeAny)[key][index][
              alertObject
            ].toString()

            camelCaseCostsActiveAlert[camelCase(key)][index][camelCase(alertObject)] =
              parsedAlertObjectValue
          }
        })
      })
    }
  })

  return {
    ...(camelCaseCostsActiveAlert as CostsActiveAlert),
    isWarningPerUser: isTrue(camelCaseCostsActiveAlert.isWarningPerUser),
    isWarningPerVehicle: isTrue(camelCaseCostsActiveAlert.isWarningPerVehicle),
    isWarningPerDriver: isTrue(camelCaseCostsActiveAlert.isWarningPerDriver),
    isDueDaysWarning: isTrue(camelCaseCostsActiveAlert.isDueDaysWarning),
    isDueOdometerWarning: isTrue(camelCaseCostsActiveAlert.isDueOdometerWarning),
    isWarningForAll: isTrue(camelCaseCostsActiveAlert.isWarningForAll),
    warnedEntities,
  }
}

// Guarantee username to always be a string and user_id to always be a number
const parseCostsActiveAlertUser = ({
  username,
  user_id,
  fleet_user_id,
  cell_number,
  email,
}: CostsActiveAlertUser): CostsActiveAlertUser => ({
  username: username.toString(),
  user_id: Number(user_id),
  fleet_user_id: String(fleet_user_id),
  cell_number,
  email,
})

const parseCostsActiveAlerts = (
  costsActiveAlerts: Array<{
    alertConfigurations: Array<SnakeCaseCostsActiveAlert>
    users: Array<CostsActiveAlertUser>
    drivers: Array<CostsActiveAlertDriver>
    vehicles: Array<CostsActiveAlertVehicle>
    driverGroups: Array<CostsActiveAlertDriverGroups>
    vehicleGroups: Array<CostsActiveAlertVehicleGroups>
  }>,
) => {
  const {
    alertConfigurations,
    users,
    drivers,
    vehicles,
    driverGroups,
    vehicleGroups = [],
  } = costsActiveAlerts[0]
  const parsedUsers = users.map(parseCostsActiveAlertUser)
  const parsedVehicleGroups = isIterable(vehicleGroups) ? vehicleGroups : []
  const parsedVehicles = isIterable(vehicles) ? vehicles : []
  const parsedCostsActiveAlerts = alertConfigurations.map((alert) =>
    parseCostsActiveAlert(
      alert,
      parsedUsers,
      drivers,
      parsedVehicles,
      driverGroups,
      parsedVehicleGroups,
    ),
  )

  return {
    costsActiveAlerts: parsedCostsActiveAlerts,
    users: parsedUsers,
    mifleetDrivers: drivers,
    mifleetVehicles: vehicles,
  }
}

export default {
  fetchCostsActiveAlerts() {
    return caller(`alerts/alertConfigurationJSON.php?action=read`).then((res) =>
      res.totalCount > 0 ? parseCostsActiveAlerts(res.objectList) : [],
    )
  },
  fetchCostsAlertTypes() {
    return caller(`alerts/alertConfigurationJSON.php?action=read`, 'POST', {
      alert_configuration_id: '0',
    }).then((res) =>
      res.totalCount > 0 ? parseCostsAlertTypes(res.objectList.warningTypes) : [],
    )
  },
  createCostsActiveAlert(costsActiveAlertData: CostsActiveAlert) {
    return caller(
      'alerts/alertConfigurationJSON.php?action=create',
      'POST',
      normalizeCostsActiveAlert(costsActiveAlertData),
    ).then((res) => (res.totalCount > 0 ? costsActiveAlertData : {}))
  },

  updateCostsActiveAlert(costsActiveAlertData: CostsActiveAlert) {
    return caller(
      'alerts/alertConfigurationJSON.php?action=update',
      'POST',
      normalizeCostsActiveAlert(costsActiveAlertData),
    ).then((res) => (res.totalCount > 0 ? costsActiveAlertData : {}))
  },

  deleteCostsActiveAlert(idToDelete: string) {
    return caller('alerts/alertConfigurationJSON.php?action=delete', 'POST', {
      alert_configuration_id: idToDelete,
    }).then(() => idToDelete)
  },
}
