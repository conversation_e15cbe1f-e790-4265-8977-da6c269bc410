import { caller } from './caller'

export declare namespace FetchDriverAssignVehicles {
  type DriverAssignVehicle = {
    assignedId: string
    vehicleId: string
    driverId: string
    plate: string
    vehicleModel: string
    vehicleManufacturer: string
    fromDate: string | Date
    toDate: string | Date
    assigned: string
  }

  type ApiInput = {
    assigned_id: string
    vehicle_id: string
    driver_id: string
    plate: string
    vehicle_model: string
    vehicle_manufacturer: string
    from_date: string | Date
    to_date: string | Date
    assigned: string
  }
  type ApiOutput = Array<ApiInput>

  type Return = Array<DriverAssignVehicle>
}
// Helpers
function normalizeDriverAssignVehicle(
  driverAssignVehicle: FetchDriverAssignVehicles.DriverAssignVehicle,
) {
  return {
    assigned_id: driverAssignVehicle.assignedId || '',
    vehicle_id: driverAssignVehicle.vehicleId || '',
    driver_id: driverAssignVehicle.driverId || '',
    plate: driverAssignVehicle.plate || '',
    vehicle_model: driverAssignVehicle.vehicleModel || '',
    vehicle_manufacturer: driverAssignVehicle.vehicleManufacturer || '',
    from_date: driverAssignVehicle.fromDate || '',
    to_date: driverAssignVehicle.toDate || '',
    assigned: driverAssignVehicle.assigned || '',
  }
}

export function parseDriverAssignVehicles(
  driverAssignVehicles: Array<FetchDriverAssignVehicles.ApiInput>,
) {
  return driverAssignVehicles.map((driverAssignVehicle) => ({
    assignedId: driverAssignVehicle.assigned_id,
    vehicleId: driverAssignVehicle.vehicle_id,
    driverId: driverAssignVehicle.driver_id,
    plate: driverAssignVehicle.plate,
    vehicleModel: driverAssignVehicle.vehicle_model,
    vehicleManufacturer: driverAssignVehicle.vehicle_manufacturer,
    fromDate: driverAssignVehicle.from_date,
    toDate: driverAssignVehicle.to_date,
    assigned: driverAssignVehicle.assigned,
  }))
}

export default {
  fetchDriverAssignVehicles(driverId: string) {
    return caller(`drivers/driverAssignedJSON.php?action=read&driver=${driverId}`).then(
      (res) =>
        res && res.totalCount > 0 ? parseDriverAssignVehicles(res.objectList) : [],
    )
  },

  fetchDriverAssignVehiclesHistory(driverId: string) {
    return caller(
      `drivers/driverAssignedJSON.php?action=read&driver=${driverId}&isHistory=true`,
      'GET',
      null,
      { showError: false },
    ).then((res) =>
      res && res.totalCount > 0 ? parseDriverAssignVehicles(res.objectList) : [],
    )
  },

  createDriverAssignVehicles(
    driverAssignedVehicle: FetchDriverAssignVehicles.DriverAssignVehicle,
  ) {
    return caller(
      'drivers/driverAssignedJSON.php?action=create',
      'POST',
      normalizeDriverAssignVehicle(driverAssignedVehicle),
    )
  },

  updateDriverAssignVehicles(
    driverAssignedVehicle: FetchDriverAssignVehicles.DriverAssignVehicle,
  ) {
    return caller(
      'drivers/driverAssignedJSON.php?action=update',
      'POST',
      normalizeDriverAssignVehicle(driverAssignedVehicle),
    )
  },

  deleteDriverAssignVehicles(assignedId: string) {
    return caller('drivers/driverAssignedJSON.php?action=delete', 'POST', {
      assigned_id: assignedId,
    })
  },
}
