import { caller } from './caller'

const endpoints = {
  fetchPurchase: 'costs/documentListJSON.php?action=read&list=Purchases',
  updatePurchase: 'vehicles/vehiclePurchaseJSON.php?action=update',
  createPurchase: 'vehicles/vehiclePurchaseJSON.php?action=create',

  fetchAccessories: 'costs/documentListJSON.php?action=read&list=Accessories',
  updateAccessories: 'vehicles/vehicleAccessoryJSON.php?action=update',
  createAccessories: 'vehicles/vehicleAccessoryJSON.php?action=create',

  fetchFinancing: 'costs/documentListJSON.php?action=read&list=Financing',
  updateFinancing: 'vehicles/vehicleFinancingJSON.php?action=update',
  createFinancing: 'vehicles/vehicleFinancingJSON.php?action=create',
}

const parseNameValueArray = (response, obj) => {
  const fullObj = { ...response[0] }

  if (!obj) {
    return fullObj
  }

  obj.map((i) => {
    const accessorArr = fullObj[i.accessor]

    accessorArr.map((o) => {
      const item = o
      item.name = o[i.keyName]
      item.value = o[i.keyValue]
      return item
    })
    return (fullObj[i.accessor] = accessorArr)
  })

  return fullObj
}

export default {
  fetchVehicleCostsPurchase() {
    return caller(endpoints.fetchPurchase).then((res) =>
      parseNameValueArray(res.objectList),
    )
  },

  updateVehicleCostsPurchase({ payload }) {
    return caller(
      payload.purchase_id == null ? endpoints.createPurchase : endpoints.updatePurchase,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchVehicleCostsAccessories() {
    return caller(endpoints.fetchAccessories).then((res) =>
      parseNameValueArray(res.objectList, [
        {
          accessor: 'accessoryTypes',
          keyValue: 'vehicle_accessory_type_id',
          keyName: 'accessory_type',
        },
      ]),
    )
  },

  updateVehicleCostsAccessories({ payload }) {
    return caller(
      payload.accessory_id == null
        ? endpoints.createAccessories
        : endpoints.updateAccessories,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchVehicleCostsFinancing() {
    return caller(endpoints.fetchFinancing).then((res) =>
      parseNameValueArray(res.objectList),
    )
  },

  updateVehicleCostsFinancing({ payload }) {
    return caller(
      payload.financing_id == null
        ? endpoints.createFinancing
        : endpoints.updateFinancing,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },
}
