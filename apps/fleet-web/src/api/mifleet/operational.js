import { isEmpty } from 'lodash'
import { caller } from './caller'
import { parseOperationalCostsArray } from './utils/parse-name-value-array'

const endpoints = {
  miscEndpoint: 'costs/documentListJSON.php?action=read&list=Misc',
  tollFraudEndpoint: 'overviews/tollValidationJSON.php?action=read',
  updateFraudEndpoint: 'overviews/tollValidationJSON.php?action=update',
  fetchDashboard: 'dashboard/fleetCostJSON.php?action=read',
  fetchFuel: 'costs/documentListJSON.php?action=read&list=Fuelling',
  updateFuel: 'vehicles/vehicleFuellingJSON.php?action=update',
  createFuel: 'vehicles/vehicleFuellingJSON.php?action=create',

  fetchToll: 'costs/documentListJSON.php?action=read&list=Tolls',
  updateToll: 'vehicles/vehicleTollJSON.php?action=update',
  createToll: 'vehicles/vehicleTollJSON.php?action=create',

  fetchCleaning: 'costs/documentListJSON.php?action=read&list=Cleaning',
  updateCleaning: 'vehicles/vehicleCleaningJSON.php?action=update',
  createCleaning: 'vehicles/vehicleCleaningJSON.php?action=create',

  fetchMaintenance: 'costs/documentListJSON.php?action=read&list=Maintenances',
  updateMaintenance: 'vehicles/vehicleMaintenanceJSON.php?action=update',
  createMaintenance: 'vehicles/vehicleMaintenanceJSON.php?action=create',

  fetchTowing: 'costs/documentListJSON.php?action=read&list=Breakdowns',
  updateTowing: 'vehicles/vehicleBreakdownJSON.php?action=update',
  createTowing: 'vehicles/vehicleBreakdownJSON.php?action=create',

  fetchFines: 'costs/documentListJSON.php?action=read&list=Fines',
  updateFines: 'vehicles/vehicleFineJSON.php?action=update',
  createFines: 'vehicles/vehicleFineJSON.php?action=create',

  fetchDriverCosts: 'costs/documentListJSON.php?action=read&list=DriverCosts',
  updateDriverCosts: 'drivers/driverCostJSON.php?action=update',
  createDriverCosts: 'drivers/driverCostJSON.php?action=create',

  fetchTires: 'costs/documentListJSON.php?action=read&list=Tires',
  updateTires: 'vehicles/vehicleTyreJSON.php?action=update',
  createTires: 'vehicles/vehicleTyreJSON.php?action=create',

  updateTire: 'vehicles/vehicleTyreTireJSON.php?action=update',
  createTire: 'vehicles/vehicleTyreTireJSON.php?action=create',
  deleteTire: 'vehicles/vehicleTyreTireJSON.php?action=delete',

  fetchIncidents: 'costs/documentListJSON.php?action=read&list=Incidents',
  updateIncidents: 'vehicles/vehicleIncidentJSON.php?action=update',
  createIncidents: 'vehicles/vehicleIncidentJSON.php?action=create',

  updateIncidentsTP: 'vehicles/vehicleIncidentThirdPartyJSON.php?action=update',
  createIncidentsTP: 'vehicles/vehicleIncidentThirdPartyJSON.php?action=create',
  deleteIncidentsTP: 'vehicles/vehicleIncidentThirdPartyJSON.php?action=delete',

  fetchOil: 'costs/documentListJSON.php?action=read&list=Oils',
  updateOil: 'vehicles/vehicleOilJSON.php?action=update',
  createOil: 'vehicles/vehicleOilJSON.php?action=create',

  fetchConsumables: 'costs/documentListJSON.php?action=read&list=Consumables',
  updateConsumables: 'vehicles/vehicleConsumableJSON.php?action=update',
  createConsumables: 'vehicles/vehicleConsumableJSON.php?action=create',

  fetchRentalCosts: 'costs/documentListJSON.php?action=read&list=Rentals',
  updateRentalCosts: 'vehicles/vehicleRentalJSON.php?action=update',
  createRentalCosts: 'vehicles/vehicleRentalJSON.php?action=create',

  fetchLeasingCosts: 'costs/documentListJSON.php?action=read&list=Leasings',
  updateLeasingCosts: 'vehicles/vehicleLeasingJSON.php?action=update',
  createLeasingCosts: 'vehicles/vehicleLeasingJSON.php?action=create',
}

const normalizeUpdate = (item) => {
  const newObj = { ...item }

  if (item.is_tank_full !== 'f' && item.is_tank_full !== 't') {
    newObj.is_tank_full = 'f'
  }

  return newObj
}

export default {
  fetchOperationalDashboard(data) {
    const dashboardEndpoint =
      data && data.payload
        ? `${endpoints.fetchDashboard}&start_date=${data.payload.start_date}&end_date=${data.payload.end_date}`
        : endpoints.fetchDashboard
    return caller(dashboardEndpoint).then((res) => res.objectList)
  },

  fetchOperationalTollFraud(data) {
    return caller(endpoints.tollFraudEndpoint, 'POST', data).then(
      (res) => res.objectList,
    )
  },

  fetchOperationalMisc(data) {
    const miscEndpoint =
      data && data.payload
        ? `${endpoints.miscEndpoint}&start_date=${data.payload.start_date}&end_date=${data.payload.end_date}`
        : endpoints.miscEndpoint
    return caller(miscEndpoint).then((res) =>
      parseOperationalCostsArray(res.objectList),
    )
  },

  fetchOperationalGrouped(data) {
    const groupedEndpoint = `costs/costJSON.php?action=read&start_date=${data.payload.start_date}&end_date=${data.payload.end_date}`
    return caller(groupedEndpoint, 'GET').then((res) => res.objectList)
  },

  updateOperationalTollFraud(data) {
    return caller(endpoints.updateFraudEndpoint, 'POST', data).then(
      (res) => res.objectList,
    )
  },

  fetchOperationalFuel(data) {
    const fuelEndpoint =
      data && data.payload
        ? `${endpoints.fetchFuel}&start_date=${data.payload.start_date}&end_date=${
            data.payload.end_date
          }${data.payload.vehicleId ? `&vehicle_id=${data.payload.vehicleId}` : ''}`
        : endpoints.fetchFuel
    return caller(fuelEndpoint).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'contractFuelCards',
          keyValue: 'contract_fuel_card_id',
          keyName: 'supplier',
        },
        { accessor: 'drivers', keyValue: 'driver_id', keyName: 'short_name' },
        {
          accessor: 'fuelTransactionTypes',
          keyValue: 'fuel_transaction_type_id',
          keyName: 'tag',
        },
      ]),
    )
  },

  updateOperationalFuel({ payload }) {
    return caller(
      payload.fuelling_id == null ? endpoints.createFuel : endpoints.updateFuel,
      'POST',
      normalizeUpdate(payload),
    ).then((res) => res.objectList)
  },

  fetchOperationalOils() {
    return caller(endpoints.fetchOil).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'oilTypes',
          keyValue: 'vehicle_oil_type_id',
          keyName: 'oil_type',
        },
      ]),
    )
  },

  updateOperationalOils({ payload }) {
    return caller(
      payload.oil_id == null ? endpoints.createOil : endpoints.updateOil,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalConsumables() {
    return caller(endpoints.fetchConsumables).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'consumableTypes',
          keyValue: 'vehicle_consumable_type_id',
          keyName: 'consumable_type',
        },
      ]),
    )
  },

  updateOperationalConsumables({ payload }) {
    return caller(
      payload.consumable_id == null
        ? endpoints.createConsumables
        : endpoints.updateConsumables,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalRentalCosts() {
    return caller(endpoints.fetchRentalCosts).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'rentalCostTypes',
          keyValue: 'vehicle_rental_cost_type_id',
          keyName: 'rental_cost_type',
        },
      ]),
    )
  },

  updateOperationalRentalCosts({ payload }) {
    return caller(
      payload.rental_id == null
        ? endpoints.createRentalCosts
        : endpoints.updateRentalCosts,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalLeasingCosts() {
    return caller(endpoints.fetchLeasingCosts).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'leasingCostTypes',
          keyValue: 'vehicle_leasing_cost_type_id',
          keyName: 'leasing_cost_type',
        },
      ]),
    )
  },

  updateOperationalLeasingCosts({ payload }) {
    return caller(
      payload.leasing_id == null
        ? endpoints.createLeasingCosts
        : endpoints.updateLeasingCosts,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalTolll(data) {
    const tollEndpoint =
      data && data.payload
        ? `${endpoints.fetchToll}&start_date=${data.payload.start_date}&end_date=${
            data.payload.end_date
          }${data.payload.vehicleId ? `&vehicle_id=${data.payload.vehicleId}` : ''}`
        : endpoints.fetchToll

    return caller(tollEndpoint).then((res) =>
      parseOperationalCostsArray(res.objectList),
    )
  },

  updateOperationalToll({ payload }) {
    return caller(
      payload.toll_id == null ? endpoints.createToll : endpoints.updateToll,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalCleaning() {
    return caller(endpoints.fetchCleaning).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'cleaningTypes',
          keyValue: 'vehicle_cleaning_type_id',
          keyName: 'cleaning_type',
        },
      ]),
    )
  },

  updateOperationalCleaning({ payload }) {
    return caller(
      payload.cleaning_id == null ? endpoints.createCleaning : endpoints.updateCleaning,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalMaintenance(data) {
    const maintenanceEndpoint =
      data && data.payload
        ? `${endpoints.fetchMaintenance}&start_date=${
            data.payload.start_date
          }&end_date=${data.payload.end_date}${
            data.payload.vehicleId ? `&vehicle_id=${data.payload.vehicleId}` : ''
          }`
        : endpoints.fetchMaintenance
    return caller(maintenanceEndpoint).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'maintenanceTypes',
          keyValue: 'vehicle_maintenance_type_id',
          keyName: 'maintenance_type',
        },
      ]),
    )
  },

  updateOperationalMaintenance({ payload }) {
    return caller(
      payload.maintenance_id == null
        ? endpoints.createMaintenance
        : endpoints.updateMaintenance,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalTowing() {
    return caller(endpoints.fetchTowing).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'drivers',
          keyValue: 'driver_id',
          keyName: 'short_name',
        },
        {
          accessor: 'breakdownTypes',
          keyValue: 'vehicle_breakdown_type_id',
          keyName: 'breakdown_type',
        },
      ]),
    )
  },

  updateOperationalTowing({ payload }) {
    return caller(
      payload.breakdown_id == null ? endpoints.createTowing : endpoints.updateTowing,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalFines(data) {
    const finesEndpoint =
      data && data.payload
        ? `${endpoints.fetchFines}&start_date=${data.payload.start_date}&end_date=${
            data.payload.end_date
          }${data.payload.vehicleId ? `&vehicle_id=${data.payload.vehicleId}` : ''}`
        : endpoints.fetchFines

    return caller(finesEndpoint).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'drivers',
          keyValue: 'driver_id',
          keyName: 'short_name',
        },
        {
          accessor: 'fineTypes',
          keyValue: 'vehicle_fine_type_id',
          keyName: 'fine_type',
        },
      ]),
    )
  },

  updateOperationalFines({ payload }) {
    return caller(
      payload.fine_id == null ? endpoints.createFines : endpoints.updateFines,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalDriverCosts() {
    return caller(endpoints.fetchDriverCosts).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'driverCostTypes',
          keyValue: 'driver_cost_type_id',
          keyName: 'cost_type',
        },
      ]),
    )
  },

  updateOperationalDriverCosts({ payload }) {
    return caller(
      isEmpty(payload.cost_id)
        ? endpoints.createDriverCosts
        : endpoints.updateDriverCosts,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchOperationalTires(data) {
    const tiresEndpoint =
      data && data.payload
        ? `${endpoints.fetchTires}&start_date=${data.payload.start_date}&end_date=${
            data.payload.end_date
          }${data.payload.vehicleId ? `&vehicle_id=${data.payload.vehicleId}` : ''}`
        : endpoints.fetchTires

    return caller(tiresEndpoint).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'tireOperationTypes',
          keyValue: 'vehicle_tyre_operation_id',
          keyName: 'tyre_operation',
        },
        {
          accessor: 'tireLocationTypes',
          keyValue: 'vehicle_tyre_location_id',
          keyName: 'tyre_location',
        },
      ]),
    )
  },

  updateOperationalTires({ payload }) {
    return caller(
      payload.tyre_id == null ? endpoints.createTires : endpoints.updateTires,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  updateOperationalTiresTire(data) {
    return caller(
      isEmpty(data.tyre_tire_id) ? endpoints.createTire : endpoints.updateTire,
      'POST',
      data,
    ).then((res) => ({
      ...res.objectList,
      document_line_id: data.document_line_id,
    }))
  },

  deleteOperationalTiresTire(data) {
    return caller(endpoints.deleteTire, 'POST', data).then((res) => ({
      ...res.objectList,
      document_line_id: data.document_line_id,
    }))
  },

  fetchOperationalIncidents(data) {
    const IncidentsEndpoint =
      data && data.payload
        ? `${endpoints.fetchIncidents}&start_date=${data.payload.start_date}&end_date=${
            data.payload.end_date
          }${data.payload.vehicleId ? `&vehicle_id=${data.payload.vehicleId}` : ''}`
        : endpoints.fetchIncidents

    return caller(IncidentsEndpoint).then((res) =>
      parseOperationalCostsArray(res.objectList, [
        {
          accessor: 'drivers',
          keyValue: 'driver_id',
          keyName: 'short_name',
        },
        {
          accessor: 'incidentTypes',
          keyValue: 'vehicle_incident_type_id',
          keyName: 'incident_type',
        },
      ]),
    )
  },

  updateOperationalIncidents({ payload }) {
    return caller(
      payload.incident_id == null
        ? endpoints.createIncidents
        : endpoints.updateIncidents,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  updateOperationalIncidentsTP(data) {
    return caller(
      data.incident_third_party_id == null
        ? endpoints.createIncidentsTP
        : endpoints.updateIncidentsTP,
      'POST',
      data,
    ).then((res) => ({
      ...res.objectList,
      document_line_id: data.document_line_id,
    }))
  },

  deleteOperationalIncidentsTP(data) {
    return caller(endpoints.deleteIncidentsTP, 'POST', data).then((res) => ({
      ...res.objectList,
      document_line_id: data.document_line_id,
    }))
  },
}
