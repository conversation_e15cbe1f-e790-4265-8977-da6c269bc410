import { isEmpty } from 'lodash'
import { caller } from './caller'
import {
  parseRegulatoryCostsArray,
  parseOperationalCostsArray,
} from './utils/parse-name-value-array'

const endpoints = {
  fetchDriverPermits: 'costs/documentListJSON.php?action=read&list=DriverPermits',
  updateDriverPermits: 'comms=drivers/driverPermitJSON.php?action=update',
  createDriverPermits: 'comms=drivers/driverPermitJSON.php?action=create',

  fetchInsurance: 'costs/documentListJSON.php?action=read&list=Insurances',

  fetchTaxes: 'costs/documentListJSON.php?action=read&list=Taxes',
  updateTaxes: 'comms=vehicles/vehicleTaxJSON.php?action=update',
  createTaxes: 'comms=vehicles/vehicleTaxJSON.php?action=create',

  fetchPermits: 'costs/documentListJSON.php?action=read&list=Permits',
  updatePermits: 'vehicles/vehiclePermitJSON.php?action=update',
  createPermits: 'vehicles/vehiclePermitJSON.php?action=create',
}

export default {
  fetchRegulatoryPermits() {
    return caller(endpoints.fetchPermits).then((res) =>
      parseRegulatoryCostsArray(
        res.objectList,
        'permitTypes',
        'vehicle_permit_type_id',
        'permit_type',
      ),
    )
  },

  updateRegulatoryPermits({ payload }) {
    return caller(
      payload.permit_id == null ? endpoints.createPermits : endpoints.updatePermits,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchRegulatoryInsurance() {
    return caller(endpoints.fetchInsurance).then((res) =>
      parseOperationalCostsArray(res.objectList),
    )
  },

  fetchRegulatoryTaxes() {
    return caller(endpoints.fetchTaxes).then((res) =>
      parseRegulatoryCostsArray(res.objectList, 'taxTypes', 'tax_type_id', 'tax_name'),
    )
  },

  updateRegulatoryTaxes({ payload }) {
    return caller(
      payload.tax_id == null ? endpoints.createTaxes : endpoints.updateTaxes,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },

  fetchRegulatoryDriverPermits() {
    return caller(endpoints.fetchDriverPermits).then((res) =>
      parseRegulatoryCostsArray(
        res.objectList,
        'driverPermitTypes',
        'driver_permit_type_id',
        'permit_type',
      ),
    )
  },

  updateRegulatoryDriverPermits({ payload }) {
    return caller(
      isEmpty(payload.permit_id)
        ? endpoints.createDriverPermits
        : endpoints.updateDriverPermits,
      'POST',
      payload,
    ).then((res) => res.objectList)
  },
}
