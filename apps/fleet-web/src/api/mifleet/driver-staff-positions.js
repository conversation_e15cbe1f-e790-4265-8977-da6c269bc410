import { caller } from './caller'

// Helpers
const normalizeDriverStaffPosition = (driverStaffPosition) => ({
  staff_position_id: driverStaffPosition.staffPositionId || '',
  staff_position_type_id: driverStaffPosition.staffPositionTypeId || '',
  driver_id: driverStaffPosition.driverId || '',
  position_type: driverStaffPosition.positionType || '',
  assigned: driverStaffPosition.assigned || '',
})

const parseDriverStaffPosition = (driverStaffPosition) => ({
  staffPositionId: driverStaffPosition.staff_position_id,
  staffPositionTypeId: driverStaffPosition.staff_position_type_id,
  driverId: driverStaffPosition.driver_id,
  positionType: driverStaffPosition.position_type,
  assigned: driverStaffPosition.assigned,
})

const parseDriverStaffPositions = (driverStaffPositions) =>
  driverStaffPositions.map(parseDriverStaffPosition)

export default {
  fetchDriverStaffPositions(driverId) {
    return caller(
      `drivers/driverStaffPositionJSON.php?action=read&driver=${driverId}`,
    ).then((res) =>
      res && res.totalCount > 0 ? parseDriverStaffPositions(res.objectList) : [],
    )
  },

  createDriverStaffPositions(driverStaffPositionData) {
    return caller(
      'drivers/driverStaffPositionJSON.php?action=create',
      'POST',
      normalizeDriverStaffPosition(driverStaffPositionData),
    ).then((res) =>
      res.totalCount > 0 ? parseDriverStaffPosition(res.objectList) : {},
    )
  },

  updateDriverStaffPositions(driverStaffPositionData) {
    return caller(
      'drivers/driverStaffPositionJSON.php?action=update',
      'POST',
      normalizeDriverStaffPosition(driverStaffPositionData),
    ).then((res) =>
      res.totalCount > 0 ? parseDriverStaffPosition(res.objectList) : {},
    )
  },

  deleteDriverStaffPositions(driverStaffPositionData) {
    return caller(
      'drivers/driverStaffPositionJSON.php?action=delete',
      'POST',
      normalizeDriverStaffPosition(driverStaffPositionData),
    ).then((res) =>
      res.totalCount > 0 ? parseDriverStaffPosition(res.objectList) : {},
    )
  },
}
