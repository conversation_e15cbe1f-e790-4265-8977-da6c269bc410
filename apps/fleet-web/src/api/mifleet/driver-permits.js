import { caller } from './caller'

function normalizeDriverPermit(driverPermit) {
  return {
    driver_id: driverPermit.driverId || '',
    permit_id: driverPermit.permitId || '',
    driver_permit_type_id: driverPermit.permitTypeId || '',
    permit_number: driverPermit.permitNumber || '',
    begin_date: driverPermit.beginDate || '',
    expiration_date: driverPermit.expirationDate || '',
    description: driverPermit.description || '',
  }
}

const parseDriverPermit = (driverPermit) => ({
  driverId: driverPermit.driver_id || '',
  permitId: driverPermit.permit_id || '',
  permitTypeId: driverPermit.driver_permit_type_id.toString() || '',
  permitNumber: driverPermit.permit_number || '',
  beginDate: driverPermit.begin_date || '',
  expirationDate: driverPermit.expiration_date || '',
  description: driverPermit.description || '',
  permitTypeName: driverPermit.driver_permit_type || '',
})

const parseDriverPermits = (driverPermits) => driverPermits.map(parseDriverPermit)

export default {
  fetchDriverPermits(driverId) {
    return caller(
      `drivers/driverPermitJSON.php?action=read&driver=${driverId}`,
      'GET',
      null,
      { showError: false },
    ).then((res) => {
      if (res && res.totalCount > 0) {
        const driverPermitData = res.objectList[0]
        return {
          permits: parseDriverPermits(driverPermitData.permits || []),
          permitTypes: driverPermitData.permitTypes,
          taxTypes: driverPermitData.taxTypes,
        }
      }

      return {}
    })
  },

  createDriverPermit(driverPermitData) {
    return caller(
      'drivers/driverPermitJSON.php?action=create',
      'POST',
      normalizeDriverPermit(driverPermitData),
    ).then((res) => {
      if (res.totalCount > 0) {
        const createdDriverPermit = res.objectList
        return {
          createdDriverPermit: parseDriverPermit(createdDriverPermit || {}),
          success: res.success,
        }
      }

      return { success: res.success }
    })
  },

  updateDriverPermit(driverPermitData) {
    return caller(
      'drivers/driverPermitJSON.php?action=update',
      'POST',
      normalizeDriverPermit(driverPermitData),
    ).then((res) => ({
      success: res.success,
    }))
  },

  deleteDriverPermit(permitId) {
    return caller('drivers/driverPermitJSON.php?action=delete', 'POST', {
      permit_id: permitId,
    }).then((res) => ({
      success: res.success,
    }))
  },
}
