import type {
  parseUser<PERSON>oles,
  parseUser<PERSON><PERSON>,
  parseBase<PERSON>ser<PERSON><PERSON>,
  parseUser<PERSON>oleReports,
} from '.'

export namespace UserRole {
  type Base = {
    user_role_id: number | string
    company_id: number
    tag: string
    is_admin: 't' | 'f'
    is_deleted: boolean
  }

  type ParsedBase = ReturnType<typeof parseBaseUserRole>
}

export namespace FetchUserRoles {
  type Return = ReturnType<typeof parseUserRoles>
}

export namespace FetchUserRole {
  type ResponseSystemPermission = {
    view_permission_id: number | null
    edit_permission_id: number | null
    delete_permission_id: number | null
    parent_id: number | null
    module_id: number
    user_role_id: number
    tag: string
    view_permission: boolean | null
    edit_permission: boolean | null
    delete_permission: boolean | null
    objectList: Array<ResponseSystemPermission>
  }

  type Response = UserRole.Base & {
    systemPermissions: Array<ResponseSystemPermission>
    vehiclePermissions: {
      vehicles: Array<{
        vehicle_id: number
        plate: string | number
        manufacturer: string
        model: string
        manufacture_year: number
        vehicle_type: string
        allowed: boolean
      }>
      costCentres: Array<Group>
      vehicleGroups: Array<Group>
      ungroupedVehicleIds: Array<Vehicle['id']>
    }
    drivers: Array<{
      driver_id: string
      driver_name: string | 0 // Welp
      employee_number: number | null
      id_number: string | number | null
      allowed: boolean
      created_by: string
    }>
    reports: Array<{
      user_role_report_id: number
      report_id: number
      user_role_id: number
      report_name: string
      allowed: boolean
    }>
  }

  type Group = {
    id?: number
    tag: string
    vehicleIds: Array<Vehicle['id']>
  }

  type Vehicle = {
    id: number
    tag: string
    manufacturer: string
    model: string
    year: number
    type: string
    allowed: boolean
  }

  type Driver = {
    id: string
    name: string
    employeeNumber: number | null
    employeeId: string
    allowed: boolean
    created_by: string
  }

  type FeaturePermission = { id: number; checked: boolean }

  type Feature = {
    tag: string
    edit: FeaturePermission['id'] | null
    view: FeaturePermission['id'] | null
    delete: FeaturePermission['id'] | null
    subFeatures: Array<Feature>
  }

  type Report = ReturnType<typeof parseUserRoleReports>[0]

  type Return = ReturnType<typeof parseUserRole>
}
