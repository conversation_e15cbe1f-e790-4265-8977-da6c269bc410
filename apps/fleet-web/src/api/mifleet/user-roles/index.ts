import type { FixMeAny } from 'src/types'
import { caller } from '../caller'
import type { UserRole, FetchUserRole } from './types'
import { isTrue } from 'cartrack-utils'

export const parseBaseUserRole = (rawRole: UserRole.Base) => ({
  id: Number(rawRole.user_role_id),
  companyId: rawRole.company_id,
  tag: rawRole.tag,
  isAdmin: isTrue(rawRole.is_admin),
})

const parseUserRoleVehicles = (
  rawVehicles: FetchUserRole.Response['vehiclePermissions']['vehicles'],
) =>
  rawVehicles.reduce((acc, rawVehicle) => {
    acc[rawVehicle.vehicle_id] = {
      id: rawVehicle.vehicle_id,
      allowed: rawVehicle.allowed,
      manufacturer: rawVehicle.manufacturer,
      model: rawVehicle.model,
      tag: String(rawVehicle.plate),
      type: rawVehicle.vehicle_type,
      year: rawVehicle.manufacture_year,
    }
    return acc
  }, {} as Record<FetchUserRole.Vehicle['id'], FetchUserRole.Vehicle>)

const parseUserRoleDrivers = (rawDrivers: FetchUserRole.Response['drivers']) => {
  const driversData: {
    // In the future, drivers will probably have groups as well
    // so we normalize the data already so it requires less refactor then
    drivers: Record<FetchUserRole.Driver['id'], FetchUserRole.Driver>
    driverIds: Array<FetchUserRole.Driver['id']>
  } = {
    drivers: {},
    driverIds: [],
  }

  return rawDrivers.reduce((acc, rawDriver) => {
    acc.drivers[rawDriver.driver_id] = {
      id: rawDriver.driver_id,
      name: rawDriver.driver_name ? String(rawDriver.driver_name) : '',
      employeeNumber: rawDriver.employee_number,
      employeeId: rawDriver.id_number === null ? '' : rawDriver.id_number.toString(),
      allowed: rawDriver.allowed,
      created_by: rawDriver.created_by,
    } as FetchUserRole.Driver

    acc.driverIds.push(rawDriver.driver_id)
    return acc
  }, driversData)
}

export const parseUserRoleReports = (rawReports: FetchUserRole.Response['reports']) =>
  rawReports.map((rawReport) => ({
    id: rawReport.report_id,
    name: rawReport.report_name,
    allowed: rawReport.allowed,
  }))

export function parseUserRoleFeaturesData(
  rawFeatures: FetchUserRole.Response['systemPermissions'],
) {
  const featuresData: {
    features: Array<FetchUserRole.Feature>
    permissions: Record<number, FetchUserRole.FeaturePermission>
  } = {
    features: [],
    permissions: {},
  }

  const parseFeature = (
    f: FetchUserRole.ResponseSystemPermission,
  ): FetchUserRole.Feature => ({
    tag: f.tag,
    edit: f.edit_permission_id,
    view: f.view_permission_id,
    delete: f.delete_permission_id,
    subFeatures: f.objectList.map(parseFeature),
  })

  const generateFeaturePermissions = (f: FetchUserRole.ResponseSystemPermission) => {
    if (f.delete_permission_id !== null) {
      featuresData.permissions[f.delete_permission_id] = {
        id: f.delete_permission_id,
        checked: Boolean(f.delete_permission),
      }
    }
    if (f.view_permission_id !== null) {
      featuresData.permissions[f.view_permission_id] = {
        id: f.view_permission_id,
        checked: Boolean(f.view_permission),
      }
    }
    if (f.edit_permission_id !== null) {
      featuresData.permissions[f.edit_permission_id] = {
        id: f.edit_permission_id,
        checked: Boolean(f.edit_permission),
      }
    }
    f.objectList.forEach((s) => generateFeaturePermissions(s))
  }

  rawFeatures.forEach((f) => {
    featuresData.features.push(parseFeature(f))
    generateFeaturePermissions(f)
  })

  return featuresData
}

export function parseUserRole(rawRole: FetchUserRole.Response) {
  const roleDetails: {
    id: number
    companyId: number
    tag: string
    isAdmin: boolean
    vehiclesData: {
      /* We normalize vehicles into a single source of truth. This will allow much easier updates and avoid bugs */
      vehicles: Record<FetchUserRole.Vehicle['id'], FetchUserRole.Vehicle>
      groups: {
        costCentres: Array<FetchUserRole.Group>
        vehicleGroups: Array<FetchUserRole.Group>
        unGroupedVehiclesIds: Array<FetchUserRole.Group>
      }
    }
    driversData: ReturnType<typeof parseUserRoleDrivers>
    featuresData: ReturnType<typeof parseUserRoleFeaturesData>
    reportsData: ReturnType<typeof parseUserRoleReports>
  } = {
    vehiclesData: {
      groups: {
        costCentres: (rawRole.vehiclePermissions.costCentres as FixMeAny).map(
          (costCentre: FixMeAny) => ({
            ...costCentre,
            id: costCentre.cost_centre_id,
            tag: String(costCentre.cost_centre),
          }),
        ),
        vehicleGroups: rawRole.vehiclePermissions.vehicleGroups,
        unGroupedVehiclesIds: [
          {
            tag: 'Ungrouped',
            vehicleIds: rawRole.vehiclePermissions.ungroupedVehicleIds,
          },
        ],
      },
      vehicles: parseUserRoleVehicles(rawRole.vehiclePermissions.vehicles),
    },
    driversData: parseUserRoleDrivers(rawRole.drivers),
    reportsData: parseUserRoleReports(rawRole.reports),
    featuresData: parseUserRoleFeaturesData(rawRole.systemPermissions),
    ...parseBaseUserRole(rawRole),
  }

  return roleDetails
}

export const parseUserRoles = (objectList: Array<UserRole.Base>) =>
  objectList.map(parseBaseUserRole)

export default {
  fetchUserRoles() {
    return caller('user/userRoleJSON.php?action=read').then((data) =>
      parseUserRoles(data.objectList),
    )
  },
  createUserRole({ companyId, tag }: { companyId: string; tag: string }) {
    return caller('user/userRoleJSON.php?action=create', 'POST', {
      company_id: companyId,
      tag,
      is_admin: false,
    }).then((data) => Number(data.objectList.user_role_id))
  },
  updateUserRoleDetails({ companyId, tag, id, isAdmin }: UserRole.ParsedBase) {
    return caller('user/userRoleJSON.php?action=update', 'POST', {
      user_role_id: id.toString(),
      company_id: companyId,
      tag,
      is_admin: isAdmin,
    })
  },
  deleteUserRole(userRoleId: number) {
    return caller('user/userRoleJSON.php?action=delete', 'POST', {
      user_role_id: userRoleId,
    })
  },

  fetchUserRole(userRoleId: number) {
    return caller('user/userRoleJSON.php?action=read', 'POST', {
      user_role_id: userRoleId.toString(),
    }).then((data) => parseUserRole(data.objectList))
  },

  updateUserRoleVehicles(
    userRoleId: number,
    vehicles: Array<Pick<FetchUserRole.Vehicle, 'id' | 'allowed'>>,
  ) {
    return caller(`user/userRoleVehicleJSON.php?action=update`, 'POST', {
      user_role_id: userRoleId,
      vehicles,
    })
  },

  updateUserRoleReport({
    userRoleId,
    reportId,
    allowed,
  }: {
    userRoleId: number
    reportId: number
    allowed: boolean
  }) {
    return caller(`user/userRoleReportJSON.php?action=create`, 'POST', {
      user_role_id: userRoleId,
      report_id: reportId,
      allowed,
    })
  },

  updateUserRoleDriver({
    userRoleId,
    driverId,
    allowed,
  }: {
    userRoleId: number
    driverId: string
    allowed: boolean
  }) {
    return caller(`user/userRoleDriverJSON.php?action=create`, 'POST', {
      user_role_id: userRoleId,
      driver_id: driverId,
      allowed,
    })
  },

  updateUserRoleFeaturePermissions(
    userRoleId: number,
    permissions: Array<FetchUserRole.FeaturePermission>,
  ) {
    return caller(`user/userRoleSystemPermissionJSON.php?action=update`, 'POST', {
      user_role_id: userRoleId,
      permissions,
    })
  },
}
