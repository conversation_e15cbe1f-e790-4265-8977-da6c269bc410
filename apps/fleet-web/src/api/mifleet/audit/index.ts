import { buildQueryStringFromObject } from 'src/api/utils'
import { connectedCtIntl } from 'src/util-components/connectedCtIntl'
import apiCaller from 'api/api-caller'
import { caller } from '../caller'
import type { FetchSessions, FetchAuthentications } from './types'
import type { FixMeAny } from 'src/types'

const auditEndpoint = (suffix: string) => 'user/userAuditJSON.php?action=' + suffix

export const parseFetchSessions = (objectList: FetchSessions.Response) =>
  objectList.map((rawSession) => ({
    userId: rawSession.user_id.toString(),
    username: rawSession.username,
    expirationDate: connectedCtIntl.formatDateWithHourMinute(
      rawSession.expiration_date,
    ),
    ipAddress: rawSession.ip_address,
    httpAgent: rawSession.http_agent,
  }))

export function parseUserAuthentications(
  userLoginHistory: FetchAuthentications.Response,
) {
  return userLoginHistory.map((r) => ({
    username: r.sub_client_id && r.sub_client_name ? r.sub_client_name : r.client_name,
    date: connectedCtIntl.formatDateWithHourMinute(r.event_time),
    sourceIpAddress: r.ip_address,
    origin: r.origin,
    success: r.status === 'success',
  }))
}

export default {
  fetchSessions({ userId, auditType }: { userId: string | null; auditType: string }) {
    const queryObject: { userId?: string; audit: string } = {
      audit: auditType,
    }

    if (userId) {
      queryObject.userId = userId
    }

    return caller(
      auditEndpoint(`read&${buildQueryStringFromObject(queryObject)}`),
    ).then((res: FixMeAny) => parseFetchSessions(res.objectList || []))
  },
  deleteSession(userId: string) {
    return caller(auditEndpoint('delete'), 'POST', {
      user_id: userId,
    })
  },

  fetchUserAuthentications({
    userId = '',
    startDate,
    endDate,
  }: {
    userId?: string
    startDate: string
    endDate: string
  }) {
    const params = {
      date_range_start: startDate,
      date_range_end: endDate,
      client_user_id: userId,
    }

    return apiCaller('ct_fleet_get_total_usersettings_login_history', {
      data: params,
    }).then((res: FixMeAny) => parseUserAuthentications(res)) as Promise<
      ReturnType<typeof parseUserAuthentications>
    >
  },
}
