import type { parseFetchSessions, parseUserAuthentications } from '.'

export declare namespace FetchSessions {
  type Response = Array<{
    user_id: number
    company_id: number
    user_name: string
    username: string
    email: string
    expiration_date: string
    ip_address: string
    http_agent: string
  }>

  type Return = ReturnType<typeof parseFetchSessions>
  type UserSession = Return[0]
}

export declare namespace FetchAuthentications {
  type Response = Array<{
    event_time: string
    origin: string
    ip_address: string
    client_id: string
    sub_client_id: string
    client_name: string
    sub_client_name: string | null
    status: 'success' | 'failure'
  }>

  type Return = ReturnType<typeof parseUserAuthentications>
  type Authentication = Return[0]
}
