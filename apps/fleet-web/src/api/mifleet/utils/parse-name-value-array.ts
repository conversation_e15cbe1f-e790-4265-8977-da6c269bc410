import type { FixMeAny } from 'src/types'

function parseRegulatoryCostsArray(
  response: Array<FixMeAny>,
  accessor: string,
  keyValue: string,
  keyName: string,
) {
  const fullObj = { ...response[0] }
  const accessorArr = fullObj[accessor]

  accessorArr.map((o: FixMeAny) => {
    const item = o
    item.name = o[keyName]
    item.value = o[keyValue]
    return item
  })
  fullObj[accessor] = accessorArr
  return fullObj
}

const parseOperationalCostsArray = (
  response: Array<FixMeAny>,
  obj?: Array<{ accessor: string; keyValue: string; keyName: string }>,
) => {
  const fullObj = { ...response[0] }

  if (!obj) {
    return fullObj
  }

  obj.map((i) => {
    const accessorArr = fullObj[i.accessor]

    accessorArr.map((o: FixMeAny) => {
      const item = o

      if (i.accessor === 'contractFuelCards') {
        item.name = `${o[i.keyName]}`
      } else {
        item.name = o[i.keyName]
      }

      item.value = o[i.keyValue]
      return item
    })
    return (fullObj[i.accessor] = accessorArr)
  })

  return fullObj
}

export { parseRegulatoryCostsArray, parseOperationalCostsArray }
