import { each, find, first, groupBy, last, map, orderBy, uniq } from 'lodash'
import moment from 'moment-timezone'
import apiCaller from './api-caller'

function parseMessage(rawMessage) {
  return {
    ackTimestamp: rawMessage.ack_timestamp,
    createTs: rawMessage.create_ts,
    identifier: rawMessage.identifier,
    messageId: rawMessage.message_id,
    messageBody: rawMessage.message_body,
    messageDirectionId: Number(rawMessage.message_direction_id),
    messageSourceId: rawMessage.message_source_id,
    messageStateId: rawMessage.message_state_id,
    messageTypeId: rawMessage.message_type_id,
    targetId: rawMessage.target_id,
    userId: rawMessage.user_id,
    userName: rawMessage.target_name,
  }
}

function parseConversations(rawConversations) {
  const conversations = []
  const messages = {}

  const conversationTypes = {
    ct_fleet_get_user_conversations: 'user',
    ct_fleet_get_group_conversations: 'group',
  }

  each(conversationTypes, (type, key) => {
    each(rawConversations[key], (conversation, id) => {
      const { name } = conversation
      let lastMessage = null
      let hasUnread = false

      if (conversation.latest_messages) {
        lastMessage = parseMessage(conversation.latest_messages[0])
        const latestMessages = orderBy(
          conversation.latest_messages.map(parseMessage),
          (m) => moment(m.createTs),
        )
        messages[id] = latestMessages

        each(conversation.latest_messages, (msg) => {
          if (
            !hasUnread &&
            msg.message_direction_id === '1' &&
            msg.message_state_id !== '3'
          ) {
            hasUnread = true
          }
        })
      }

      const members = conversation.members
        ? conversation.members.map((member) => ({
            id: member.member_id,
            name: member.member_name,
          }))
        : null

      conversations.push({
        id,
        lastMessage: lastMessage ? lastMessage.messageBody : null,
        lastMessageTs: lastMessage ? lastMessage.createTs : null,
        name,
        members,
        type,
        hasUnread,
      })
    })
  })

  return {
    conversations: orderBy(conversations, (c) => moment(c.lastMessageTs), 'desc'),
    messages,
  }
}

function parseSearchList(searchList, type) {
  const conversations = groupBy(searchList.map(parseMessage), 'targetId')

  const results = {}
  each(conversations, (messages, id) => {
    const lastMessage = last(messages)
    results[id] = {
      id,
      lastMessage: lastMessage.messageBody,
      lastMessageId: lastMessage.messageId,
      lastMessageTs: lastMessage.createTs,
      name: lastMessage.userName,
      type,
    }
  })

  return results
}

function parseSearchResults(res) {
  return {
    searchResults: {
      messages: parseSearchList(res.ct_fleet_search_messages, 'message'),
      users: parseSearchList(res.ct_fleet_search_users, 'user'),
      groups: parseSearchList(res.ct_fleet_search_user_groups, 'group'),
    },
  }
}

function parseMessageTemplates(rawTemplates) {
  return rawTemplates.map((t) => ({
    id: t.message_template_id,
    userId: t.user_id,
    message: t.message_template,
    description: t.message_template_description,
  }))
}

function parseUserGroups(rawGroups) {
  const uniqueGroups = uniq(map(rawGroups, 'user_group_id'))
  const userGroups = uniqueGroups.map((user_group_id) => ({
    id: user_group_id,
    name: find(rawGroups, { user_group_id }).user_group_name,
    members: rawGroups
      .filter((group) => group.user_group_id === user_group_id)
      .map((user) => ({
        id: user.member_id,
        name: user.member_name,
      })),
  }))
  return { userGroups }
}

export default {
  /* Conversations --------------------------- */

  fetchConversations() {
    return apiCaller('ct_fleet_get_conversations').then((res) =>
      parseConversations(res),
    )
  },

  acknowledgeMessages(messages) {
    return apiCaller('ct_fleet_acknowledge_messages', { messages }).then((res) => ({
      messages: res.ct_fleet_acknowledge_messages.map(parseMessage),
    }))
  },

  /* Messages -------------------------------- */

  fetchMessages(conversationId, messages, direction, type) {
    if (!messages || messages.length === 0) {
      return apiCaller('ct_fleet_get_latest_messages', {
        conversationId,
        type,
        limit: 20,
      }).then((res) => ({
        messages: res.ct_fleet_get_latest_messages.map(parseMessage),
      }))
    }

    // RMM: Direction value is either 'previous' or 'next', depending on what data you want to fetch
    const lastMessage =
      direction === 'previous'
        ? first(orderBy(messages, (c) => moment(c.lastMessageTs)))
        : last(orderBy(messages, (c) => moment(c.lastMessageTs)))

    return apiCaller('ct_fleet_get_messages', {
      conversationId,
      direction,
      lastMessage,
      type,
      limit: 20,
    }).then((res) => ({
      messages: res.ct_fleet_get_messages.map(parseMessage),
    }))
  },

  fetchUnreadMessages() {
    return apiCaller('ct_fleet_get_unread_messages').then(
      (res) => res.ct_fleet_get_unread_messages.total_unread,
    )
  },

  sendMessage(targetId, type, messageBody) {
    return apiCaller('ct_fleet_send_message', {
      targetId,
      type,
      messageBody,
    }).then((res) => parseMessage(res.ct_fleet_send_message))
  },

  sendNewGroupMessage(name, members, messageBody) {
    return apiCaller('ct_fleet_send_new_group_message', {
      name,
      members,
      messageBody,
    }).then((res) => parseMessage(res.ct_fleet_send_new_group_message))
  },

  /* Message Templates ---------------------------- */

  fetchMessageTemplates() {
    return apiCaller('ct_fleet_get_message_templates').then((res) =>
      parseMessageTemplates(res.ct_fleet_get_message_templates),
    )
  },

  createMessageTemplate(template) {
    return apiCaller('ct_fleet_create_message_template', {
      template,
    }).then((res) => parseMessageTemplates(res.ct_fleet_create_message_template))
  },

  updateMessageTemplate(template) {
    return apiCaller('ct_fleet_update_message_template', {
      template,
    }).then((res) => parseMessageTemplates(res.ct_fleet_update_message_template))
  },

  deleteMessageTemplate(template) {
    return apiCaller('ct_fleet_delete_message_template', {
      template,
    }).then((res) => parseMessageTemplates(res.ct_fleet_delete_message_template))
  },

  /* Search ---------------------------------- */

  searchMessages(searchText) {
    return apiCaller('ct_fleet_search_messages', { searchText }).then((res) =>
      parseSearchResults(res),
    )
  },

  jumpToMessage(messageId, type) {
    return apiCaller('ct_fleet_get_jump_to_message', { messageId, type }).then(
      (res) => ({
        searchResultMessages: res.ct_fleet_get_jump_to_message.map(parseMessage),
      }),
    )
  },

  /* User Groups ---------------------------------- */

  fetchUserGroups() {
    return apiCaller('ct_fleet_get_user_groups').then((res) =>
      parseUserGroups(res.ct_fleet_get_user_groups),
    )
  },

  updateUserGroup(groupId, name, members) {
    return apiCaller('ct_fleet_update_user_group', {
      groupId,
      name,
      members,
    }).then((res) => parseUserGroups(res.ct_fleet_get_user_groups))
  },
}
