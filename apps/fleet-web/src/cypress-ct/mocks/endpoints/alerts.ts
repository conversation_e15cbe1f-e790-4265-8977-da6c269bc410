export const alertsEndpointMocks = {
  ct_fleet_get_alerts_types: () => ({
    successReply: {
      delay: 100,
      body: {
        id: 10,
        result: {
          contact_types: [
            {
              contact_description: 'Short Message Service',
              message_cost: '1',
              notification_contact_type_id: '1',
            },
            {
              contact_description: 'Line',
              message_cost: '0.25',
              notification_contact_type_id: '30',
            },
          ],
          geofence_group_light: {
            geofence_groups_id: [],
            geofence_ids: [],
          },
          notification_trigger_types: [],
          sensors: [],
          static_bit_types: [],
          systemzones_light: [],
        },
      },
    },
  }),
}
