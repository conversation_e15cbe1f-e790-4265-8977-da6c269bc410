import type { StaticResponse } from 'cypress/types/net-stubbing'
import { times } from 'lodash'
import type {
  FacilityDeviceId,
  FetchLocationDetails,
  FacilityTypeId,
  Ct_fleet_get_facility_devices,
} from 'src/modules/lists/Facilities/api/types'
import type {
  FetchVisionVehicleEvents,
  VehicleEventId,
} from 'src/modules/vision/VisionEvents/api/types'
import type { CheckUserCredit } from 'src/modules/vision/VisionDataUsage/api/types'
import { mockImg } from 'src/modules/vision/VisionSettings/CamerasEvents/api/mocks'
import type {
  CameraConfigurationScheduleTimeUTCApiOutput,
  Ct_fetch_facility_devices_with_configured_events,
  Ct_vision_get_settings,
} from 'src/modules/vision/VisionSettings/CamerasEvents/api/types'
import type {
  Ct_vision_get_terminal_list,
  VehicleVideoSourceId,
} from 'src/modules/vision/api/types'
import type { FetchVehicles } from 'api/vehicles/types'
import {
  type ApiOutputVehicleId,
  DriverNameVisibilityStatus,
  type VehicleType,
  type VehicleId,
  type TerminalEventId,
  type VideoRequestId,
  type VehicleGroupId,
} from 'api/types'

export const visionEndpointMocks = {
  ct_fleet_get_facility_details: () => ({
    successReply: ({
      facilityId,
      devices,
    }: {
      facilityId: string
      devices?: FetchLocationDetails.ApiOutput['ct_fleet_get_devices']
    }) => ({
      delay: 50,
      body: {
        id: 10,
        result: {
          ct_fleet_get_devices: devices ?? [],
          ct_fleet_get_image_base64: {
            image:
              'data:image/webp;base64,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',
          },
          ct_fleet_get_locations: {
            site_location_id: facilityId,
            site_location_name: 'facility name',
            site_location_type_id: '1' as FacilityTypeId,
            address: 'address',
            description: 'description',
            geofence_id: null,
            latitude: 30,
            longitude: 40,
          },
        } satisfies FetchLocationDetails.ApiOutput,
      },
    }),
  }),
  ct_vision_get_terminal_list: (opts?: {
    result?: Ct_vision_get_terminal_list.ApiOutput
  }) => ({
    successReply: {
      delay: 50,
      body: {
        id: 10,
        result:
          opts?.result ??
          ({
            ct_vision_terminals: [
              {
                terminal_type: 'vehicle',
                vehicle_id: '237220892',
                type: null,
                registration: 'TEMP-ERIC',
                vehicle_name: 'TEMP-ERIC',
                description: 'Beige 1991 Toyota Hilux',
                videoUnit: {
                  type: 'HOWEN',
                  activeSources: [
                    { id: '1' as VehicleVideoSourceId },
                    { id: '2' as VehicleVideoSourceId },
                  ],
                },
              },
              {
                terminal_type: 'vehicle',
                vehicle_id: '237220657',
                type: null,
                registration: 'TEMP-ERSALES',
                vehicle_name: 'TEMP-ERSALES',
                description: 'Beige 1991 Toyota Hilux',
                videoUnit: {
                  type: 'HOWEN',
                  activeSources: [{ id: '1' as VehicleVideoSourceId }],
                },
              },
              {
                terminal_type: 'vehicle',
                vehicle_id: '237220417',
                type: null,
                registration: 'TEMP-GERTPARKES',
                vehicle_name: 'TEMP-GERTPARKES',
                description: 'Beige 1991 Toyota Hilux',
                videoUnit: {
                  type: 'HOWEN',
                  activeSources: [
                    { id: '1' as VehicleVideoSourceId },
                    { id: '2' as VehicleVideoSourceId },
                  ],
                },
              },
              {
                terminal_type: 'vehicle',
                vehicle_id: '237220776',
                type: null,
                registration: 'TEMP-JOHNM',
                vehicle_name: 'TEMP-JOHNM',
                description: 'Beige 1991 Toyota Hilux',
                videoUnit: { type: 'HOWEN', activeSources: [] },
              },
              {
                terminal_type: 'facility',
                facility_id: '4',
                facility_name: 'Facility 4',
                facility_description: 'description 4',
                devices: times(125, (i) => ({
                  device_id: i.toString() as FacilityDeviceId,
                  device_name: `Cam ${i}`,
                  latitude: i % 2 === 0 ? -Math.random() : Math.random(),
                  longitude: i % 2 === 0 ? -Math.random() : Math.random(),
                  online: true,
                })),
              },
              {
                terminal_type: 'facility',
                facility_id: '1',
                facility_name: 'Facility 1',
                facility_description: 'description 1',
                devices: [
                  {
                    device_id: '1' as FacilityDeviceId,
                    device_name: 'Side Door Cam',
                    latitude: 0.12,
                    longitude: 0.145,
                    online: true,
                  },
                ],
              },
              {
                terminal_type: 'facility',
                facility_id: '2',
                facility_name: 'Facility 2',
                facility_description: 'description 2',
                devices: times(5, (i) => ({
                  device_id: i.toString() as FacilityDeviceId,
                  device_name: `Cam ${i}`,
                  latitude: i % 2 === 0 ? -Math.random() : Math.random(),
                  longitude: i % 2 === 0 ? -Math.random() : Math.random(),
                  online: true,
                })),
              },
              {
                terminal_type: 'facility',
                facility_id: '3',
                facility_name: 'Facility 3',
                facility_description: 'description 3',
                devices: [],
              },
            ],
            ct_vision_vehicle_groups: {
              ['177' as VehicleGroupId]: {
                description: null,
                group_vehicle_id: '177' as VehicleGroupId,
                name: 'Automation Group',
                vehicles: [],
              },
              ['1372' as VehicleGroupId]: {
                description: null,
                group_vehicle_id: '1372' as VehicleGroupId,
                name: 'TestGroupNew',
                vehicles: [],
              },
            },
          } satisfies Ct_vision_get_terminal_list.ApiOutput),
        error: null,
      },
    } satisfies StaticResponse,
  }),
  ct_fetch_facility_devices_with_configured_events: () => ({
    successReply: {
      delay: 100,
      body: {
        id: 10,
        result: [
          {
            device_id: '1',
            device_name: 'Side Door Cam',
            facility_id: '23',
            facility_name: 'Warehouse',
            configuredEvents: [
              {
                eventType: 'multi-object',
                notificationFrequencyInSeconds: 30,
                schedule: { from: '12:30', to: '19:23' },
                roi: [
                  {
                    inverse_roi: false,
                    x1: '0.37',
                    x2: '0.61',
                    y1: '0.12',
                    y2: '0.43',
                  },
                ],
                targets: [
                  {
                    targetName: 'car',
                    threshold: 0.7,
                    between_number: 5,
                    condition: 'greater',
                    number: 5,
                  },
                ],
              },
            ],
          },
        ] satisfies Ct_fetch_facility_devices_with_configured_events.ApiOutput,
        error: null,
      },
    } satisfies StaticResponse,
  }),
  ct_fleet_get_facility_devices: ({
    deviceId,
    facilityId,
  }: {
    deviceId: string
    facilityId: string
  }) => ({
    successReply: {
      delay: 100,
      body: {
        id: 10,
        result: [
          {
            deviceid: deviceId,
            device_name: 'Side Door Cam',
            device_type: null,
            facility_id: facilityId,
            facility_name: 'Warehouse',
          },
        ] satisfies Ct_fleet_get_facility_devices.ApiOutput,
        error: null,
      },
    } satisfies StaticResponse,
  }),
  ct_vision_get_settings: ({
    facilityId,
    deviceId,
    multiObjectSchedule,
  }: {
    facilityId: string
    deviceId: string
    multiObjectSchedule: {
      from: CameraConfigurationScheduleTimeUTCApiOutput
      to: CameraConfigurationScheduleTimeUTCApiOutput
    }
  }) => ({
    successReply: {
      delay: 100,
      body: {
        id: 10,
        result: {
          device_id: deviceId,
          device_name: 'Side Door Cam',
          facility_id: facilityId,
          image: mockImg,
          facility_name: 'Warehouse',
          configuredEvents: [
            {
              eventType: 'multi-object',
              notificationFrequencyInSeconds: 30,
              schedule: multiObjectSchedule,
              roi: [
                {
                  inverse_roi: false,
                  x1: '0.37',
                  x2: '0.61',
                  y1: '0.12',
                  y2: '0.43',
                },
              ],
              targets: [
                {
                  targetName: 'car',
                  threshold: 0.7,
                  between_number: 5,
                  condition: 'greater',
                  number: 5,
                },
              ],
            },
          ],
        } satisfies Ct_vision_get_settings.ApiOutput,
        error: null,
      },
    } satisfies StaticResponse,
  }),
  ct_vision_get_historical_requests: () => ({
    successReply: {
      delay: 100,
      body: {
        id: 10,
        result: {
          rows: [
            {
              id: '191513500' as VehicleEventId,
              video_request_id: '191513500' as VideoRequestId,
              vehicle_id: '1524423' as VehicleId,
              registration: 'YG3FW M',
              driver_name: 'Maria Manalo',
              driver_id: '',
              terminal_event_id: '1223372049868095652' as TerminalEventId,
              address: '2541 Aguilar St.',
              start_time: '2021-03-24 12:34:43+02',
              end_time: '2021-03-24 12:38:03+02',
              req_comments: 'Fatigue',
              comment: '',
              image: mockImg,
              hasCoaching: false,
              available_camera_channels: [
                {
                  status: 'Complete',
                  url: 'https://comms1_lv.cartrack.co.za/video/video.php?key=807b8f2a119798c5da8ba78a3206c87ab1a652c4ad5a483d50be814fb5be7eb3b26c97fc9373bc3bba27f7e735dd2462',
                },
                {
                  status: 'Pending',
                  url: 'https://comms1_lv.cartrack.co.za/video/video.php?key=807b8f2a119798c5da8ba78a3206c87ab1a652c4ad5a483d50be814fb5be7eb39e93c3f76af8e63847cb1c75a81fa6a3',
                },
                {
                  status: 'In progress',
                  url: 'https://comms1_lv.cartrack.co.za/video/video.php?key=807b8f2a119798c5da8ba78a3206c87ab1a652c4ad5a483d50be814fb5be7eb303041fa06c58e709565466fea36b6b0d',
                },
                {
                  status: 'Does not exist',
                  url: 'https://comms1_lv.cartrack.co.za/video/video.php?key=807b8f2a119798c5da8ba78a3206c87ab1a652c4ad5a483d50be814fb5be7eb375431d1827fef98ddf9b902c40be5bdb',
                },
                {
                  status: 'Does not exist',
                  url: null,
                },
                {
                  status: 'Does not exist',
                  url: null,
                },
                {
                  status: 'Does not exist',
                  url: null,
                },
                {
                  status: 'Does not exist',
                  url: null,
                },
              ],
            },
          ],
          serverModel: {
            pageInfo: {
              totalRowCount: '13353',
              nextCursorRow: {
                id: '785711' as VehicleEventId,
                start_time: '2023-06-29 00:00:00+02',
              },
            },
          },
        } satisfies FetchVisionVehicleEvents.ApiOutput,
        error: null,
      },
    },
  }),
  ct_vision_check_user_credit: () => ({
    have_data: {
      delay: 50,
      body: {
        id: 10,
        result: {
          total_credit_balance: 200,
          total_credit_balance_in_mb: 10000,
          minimum_limit: 500,
          available_data_remaining: 402.46,
        } satisfies CheckUserCredit.ApiOutput,
      },
    },
    no_data_no_credit: {
      delay: 50,
      body: {
        id: 10,
        result: {
          total_credit_balance: 0,
          total_credit_balance_in_mb: 0,
          minimum_limit: 500,
          available_data_remaining: 0,
        } satisfies CheckUserCredit.ApiOutput,
      },
    },
    no_data_have_credit: {
      delay: 50,
      body: {
        id: 10,
        result: {
          total_credit_balance: 2014,
          total_credit_balance_in_mb: 2014,
          minimum_limit: 500,
          available_data_remaining: 0,
        } satisfies CheckUserCredit.ApiOutput,
      },
    },
  }),
  ct_fleet_get_vehiclelist_v3: () => ({
    successReply: {
      delay: 100,
      body: {
        id: 10,
        result: {
          ct_fleet_get_vehicle_stats: {
            activeVehicles: 0,
          },
          ct_fleet_get_vehiclelist: [
            {
              bearing: '0',
              chassisnr: null,
              client_vehicle_description: 'Toyota CHR - Victor',
              client_vehicle_description1: 'test2',
              client_vehicle_description2: 'test3',
              colour: 'Black',
              default_driver: '64436100-412c-11ec-b8e7-a4bf011631fb',
              enginenr: null,
              event_ts: '2023-05-09 19:03:23+08',
              fuel_economy: null,
              home_geofence: '82a9354c-2254-11ec-8342-a4bf011631fb',
              ignition: '2',
              latitude: '1.320065',
              longitude: '103.888758',
              licence_code: 'CODE1234',
              licence_expiry_date: '2099-01-03 08:00:02+08',
              licence_issued_date: '2000-01-03 08:00:02+08',
              manufacturer: 'Renault',
              max_speed: '0',
              model: 'Twingo',
              modelyear: '1991',
              monthly_mileage_limit: '0',
              odometer: '8711509',
              out_inspected_defects: null,
              out_new_defects: null,
              out_repaired_defects: null,
              out_special_instructions: '',
              overspeed_threshold: null,
              speed_source: undefined,
              position_description: {
                principal: {
                  description: '2 Aljunied Ave 1, Singapore 389977, Singapore',
                },
                alternatives: {
                  description_al:
                    '2, 389977, Aljunied Ave 1, Paya Lebar, Singapore, Singapore, Singapore',
                },
              },
              registration: 'HA650002',
              speed: '0',
              terminal_serial: 'HA650002',
              tolling_tag_id: 'ABCTOLLGATE',
              vehicle_id: '2453919' as ApiOutputVehicleId,
              vehicle_name: 'Hyundai 🚙',
              default_timezone: '862',
              start_inhibit_allowed: 'f',
              can_poll_svr: 'f',
              can_poll_fleet: 't',
              road_speed: null,
              rpm: '0',
              water_temp: null,
              oil_temp: null,
              clock: null,
              privacy_total: false,
              privacy_location: false,
              carpool_status: null,
              is_camera: 't',
              camera_online: 'f',
              is_wifi: 'f',
              diagnostic_error_count: null,
              gps_fix_type: '3',
              colour_code: 'Gray',
              vehicle_type: '0' as VehicleType,
              terminal_description: ['Fleet'],
              is_single_trip: false,
              driver_behaviour: '3',
              running_status: 'NO SIGNAL',
              statusClassName: 'stationary',
              taxiSensorNo: null,
              alertsActions: {
                batteryAlerts: { powerOFF: false, batteryPercentage: 0 },
                actionAlerts: {
                  actions: [],
                  actions2: [],
                  eventType: 'PER',
                  eventTypeIcon: null,
                  eventTypeDescription: null,
                },
                driverIDAlerts: { driverIDTag: null, driverIDTagTimestamp: null },
              },
              driver_name: {
                status: DriverNameVisibilityStatus.public,
                name: 'Victor Deramecourt',
              },
              camera_type: 'HOWEN',
              diagnostic: [
                {
                  out_vehicle_id: '2453919',
                  out_vehicle_name: 'Hyundai 🚙',
                  out_vehicle_make: 'Renault',
                  out_vehicle_model: 'Twingo',
                  out_vehicle_colour: 'Black',
                  out_fault_active: 'f',
                  out_fault_name: 'None',
                  out_fault_hint: '',
                  out_fault_color: '111111',
                  out_date_detected: null,
                  out_date_repaired: null,
                  out_time_duration: null,
                  out_time_distance: null,
                  vehicle_group_name: ['Latest firmware'],
                },
              ],
              vehicle_group_ids: [],
              subuser_visibility: true,
            },
          ],
          ct_fleet_get_vehicles_with_liveVision: undefined,
          ct_fleet_get_vehiclelist_vgroup: {
            '1807': {
              group_vehicle_id: '1807',
              name: 'Latest firmware',
              description: '',
              vehicles: ['2453919'] as Array<VehicleId>,
            },
            '1916': {
              group_vehicle_id: '1916',
              name: 'CART00023 Test',
              description: '',
            },
            '1917': {
              group_vehicle_id: '1917',
              name: 'CART00023 TEST 2',
              description: 'TEST GROUP',
            },
          },
          ct_fleet_get_vehicle_types: [
            {
              type_id: '0' as VehicleType,
              type_description: 'Default',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '1' as VehicleType,
              type_description: 'Motorbike',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '2' as VehicleType,
              type_description: 'Small Car',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '3' as VehicleType,
              type_description: 'Sedan Car',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '4' as VehicleType,
              type_description: '4X4',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '5' as VehicleType,
              type_description: 'Van',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '6' as VehicleType,
              type_description: 'Small Truck',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '7' as VehicleType,
              type_description: 'Large Truck',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '8' as VehicleType,
              type_description: 'Small Machine',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '9' as VehicleType,
              type_description: 'Large Machine',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '10' as VehicleType,
              type_description: 'Bus',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '11' as VehicleType,
              type_description: 'Golf Cart / Buggy',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '12' as VehicleType,
              type_description: 'Truck Concrete Pump',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '13' as VehicleType,
              type_description: 'Truck Mixer',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '14' as VehicleType,
              type_description: 'Mobile Crane',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '15' as VehicleType,
              type_description: 'Boat',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '16' as VehicleType,
              type_description: 'Generator',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '17' as VehicleType,
              type_description: 'Crane',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '18' as VehicleType,
              type_description: 'Static Pump',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '19' as VehicleType,
              type_description: 'Trailer',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '20' as VehicleType,
              type_description: 'WiFi Units',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '21' as VehicleType,
              type_description: 'Pickup Truck',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '22' as VehicleType,
              type_description: 'Ambulance',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '23' as VehicleType,
              type_description: 'Water Truck',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '24' as VehicleType,
              type_description: 'Fire Truck',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '25' as VehicleType,
              type_description: 'Road Roller',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '26' as VehicleType,
              type_description: 'Grader',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '27' as VehicleType,
              type_description: 'Forklift',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '28' as VehicleType,
              type_description: 'Tractor',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '29' as VehicleType,
              type_description: 'Dump Truck',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '30' as VehicleType,
              type_description: 'Backhoe',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '31' as VehicleType,
              type_description: 'Loader',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '32' as VehicleType,
              type_description: 'Lorry',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '33' as VehicleType,
              type_description: 'Lorry Crane',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '34' as VehicleType,
              type_description: 'Tow Truck',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '35' as VehicleType,
              type_description: 'Uncharacterized',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '36' as VehicleType,
              type_description: 'Patrol',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '37' as VehicleType,
              type_description: 'Prisoner Transport',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '38' as VehicleType,
              type_description: 'Bullet Proof',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '39' as VehicleType,
              type_description: 'Jetski',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
            {
              type_id: '40' as VehicleType,
              type_description: 'Railway Car',
              maintenance_cost_per_km: '0.00',
              fuel_cost_per_km: '0.00',
              staff_cost_per_hour: '0.00',
              emission_per_km: '0.00',
            },
          ],
        } satisfies FetchVehicles.ApiOutput,
      },
    },
  }),
}
