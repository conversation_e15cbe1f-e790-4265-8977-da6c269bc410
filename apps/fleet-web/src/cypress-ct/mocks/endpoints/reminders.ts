import type { DriverId, Standard_BE_TranslationId, VehicleId } from 'api/types'
import type { FetchAdminRemindersPrerequisites } from 'src/modules/admin/reminders/api/useAdminRemindersPrerequisitesQuery'
import type { FetchRemindersList } from 'src/modules/admin/reminders/api/useRemindersListQuery'
import type {
  AdminReminderTypes,
  ReminderCategoryId,
  FetchAdminRemindersOverview,
  ReminderCriteriaId,
  ReminderId,
} from 'src/modules/admin/reminders/types'

export const remindersEndpointMocks = {
  ct_fleet_get_reminder_prerequisites: () => ({
    delay: 50,
    body: {
      id: 10,
      result: {
        reminders_categories: [
          {
            reminder_category_id: '1' as ReminderCategoryId,
            category_name: 'Service',
            category_name_translation_id: 'Service' as Standard_BE_TranslationId,
            category_type: '1',
            user_id: null,
            is_deleted: 'f',
            category_type_name: 'VEH<PERSON><PERSON>',
          },
          {
            reminder_category_id: '2' as ReminderCategoryId,
            category_name: 'Medical Check',
            category_name_translation_id: 'Medical Check' as Standard_BE_TranslationId,
            category_type: '2',
            user_id: null,
            is_deleted: 'f',
            category_type_name: 'DRIVER',
          },
          // custom category
          {
            reminder_category_id: '25' as ReminderCategoryId,
            category_name: 'Custom 1',
            category_name_translation_id: null,
            category_type: '1',
            user_id: 'user1',
            is_deleted: 'f',
            category_type_name: 'VEHICLE',
          },
          {
            reminder_category_id: '26' as ReminderCategoryId,
            category_name: 'Custom 2',
            category_name_translation_id: null,
            category_type: '2',
            user_id: 'user1',
            is_deleted: 'f',
            category_type_name: 'DRIVER',
          },
        ],
        reminders_client_drivers: [
          {
            client_driver_id: 'driver1' as DriverId,
            driver_name: 'driver1',
          },
          {
            client_driver_id: 'driver2' as DriverId,
            driver_name: 'driver2',
          },
        ],
        reminders_client_vehicles: [
          {
            vehicle_id: 'vehicle1' as VehicleId,
            registration: 'vehicle1',
            has_pto: 't',
          },
          {
            vehicle_id: 'vehicle2' as VehicleId,
            registration: 'vehicle2',
            has_pto: 'f',
          },
        ],
        reminders_criteria_types: [
          {
            reminder_criteria_type_id: '1',
            reminder_criteria_type_name:
              'Distance' as AdminReminderTypes.CriteriaType['reminder_criteria_type_name'],
          },
          {
            reminder_criteria_type_id: '2',
            reminder_criteria_type_name:
              'Hours of Operation' as AdminReminderTypes.CriteriaType['reminder_criteria_type_name'],
          },
          {
            reminder_criteria_type_id: '3',
            reminder_criteria_type_name:
              'Date' as AdminReminderTypes.CriteriaType['reminder_criteria_type_name'],
          },
          {
            reminder_criteria_type_id: '4',
            reminder_criteria_type_name:
              'PTO' as AdminReminderTypes.CriteriaType['reminder_criteria_type_name'],
          },
        ],
        reminders_group_drivers: [
          {
            group_driver_id: 'group1',
            name: 'group1',
            description: '',
            user_id: 'user1',
            client_user_id: 'user1',
            parent_group_driver_id: null,
            is_deleted: 'f',
            group_driver_link_id: 'link1',
            client_driver_id: 'client1',
          },
          {
            group_driver_id: 'group2',
            name: 'group2',
            description: '',
            user_id: 'user2',
            client_user_id: 'user2',
            parent_group_driver_id: null,
            is_deleted: 'f',
            group_driver_link_id: 'link2',
            client_driver_id: 'client2',
          },
        ],
        reminders_group_vehicles: [
          {
            group_vehicle_id: 'group1',
            name: 'group1',
            description: '',
            user_id: 'user1',
            client_user_id: null,
            group_vehicle_link_id: 'link1',
            vehicle_id: 'vehicle1',
          },
        ],
        reminders_interval_types: [
          {
            reminder_interval_type_id: '2',
            type_name: 'Hours' as AdminReminderTypes.IntervalType['type_name'],
            fk_reminder_type: '1',
          },
          {
            reminder_interval_type_id: '3',
            type_name: 'Days' as AdminReminderTypes.IntervalType['type_name'],
            fk_reminder_type: '1',
          },
          {
            reminder_interval_type_id: '4',
            type_name: 'Weeks' as AdminReminderTypes.IntervalType['type_name'],
            fk_reminder_type: '1',
          },
          {
            reminder_interval_type_id: '5',
            type_name: 'Months' as AdminReminderTypes.IntervalType['type_name'],
            fk_reminder_type: '1',
          },
          {
            reminder_interval_type_id: '6',
            type_name: 'Years' as AdminReminderTypes.IntervalType['type_name'],
            fk_reminder_type: '1',
          },
        ],
      } satisfies FetchAdminRemindersPrerequisites.ApiOutput,
    },
  }),
  ct_fleet_get_fleet_reminders_overview: () => ({
    delay: 50,
    body: {
      id: 10,
      result: [
        {
          reminder_id: 'reminder1' as ReminderId,
          vehicle_id: 'vehicle1' as VehicleId,
          driver_id: null,
          is_applicable: true,
          reminder_category_id: '1' as ReminderCategoryId,
          vehicle: 'Vehicle 1',
          driver: null,
          reminder_criteria_id: null,
          reminder_criteria_type_id: '1',
          first_reminder: '88000',
          repeat_interval: null,
          repeat_interval_type_id: '8',
          odometer: '1000',
          current_value: '11261000',
          next_reminder: '11251000',
          difference_value: -10000,
        },
        {
          reminder_id: 'reminder2' as ReminderId,
          vehicle_id: 'vehicle2' as VehicleId,
          driver_id: null,
          is_applicable: false,
          reminder_category_id: '25' as ReminderCategoryId,
          vehicle: 'Vehicle 2',
          driver: null,
          reminder_criteria_id: null,
          reminder_criteria_type_id: null,
          first_reminder: null,
          repeat_interval: null,
          repeat_interval_type_id: null,
          odometer: '1000',
          current_value: null,
          next_reminder: null,
          difference_value: null,
        },
        {
          reminder_id: 'reminder3' as ReminderId,
          vehicle_id: null,
          driver_id: 'driver1' as DriverId,
          is_applicable: true,
          reminder_category_id: '2' as ReminderCategoryId,
          vehicle: null,
          driver: 'Driver 1',
          reminder_criteria_id: null,
          reminder_criteria_type_id: null,
          first_reminder: null,
          repeat_interval: null,
          repeat_interval_type_id: null,
          odometer: '0',
          current_value: '',
          next_reminder: '',
          difference_value: null,
        },
        {
          reminder_id: 'reminder4' as ReminderId,
          vehicle_id: null,
          driver_id: 'driver2' as DriverId,
          is_applicable: true,
          reminder_category_id: '26' as ReminderCategoryId,
          vehicle: null,
          driver: 'Driver 2',
          reminder_criteria_id: null,
          reminder_criteria_type_id: null,
          first_reminder: null,
          repeat_interval: null,
          repeat_interval_type_id: null,
          odometer: '0',
          current_value: '',
          next_reminder: '',
          difference_value: null,
        },
      ] satisfies FetchAdminRemindersOverview.ApiOutput,
    },
  }),
  ct_fleet_add_reminder: () => ({
    delay: 50,
    body: {
      error: null,
      id: 10,
      result: 'OK',
    },
  }),
  ct_fleet_get_fleet_reminders_events: () => ({
    delay: 50,
    body: {
      error: null,
      id: 10,
      result: [
        {
          reminder_criteria_id: '33285' as ReminderCriteriaId,
          first_reminder: '444',
          reminder_criteria_type_id: '2',
          reminder_criteria_type_name: 'backEnd.admin.reminder.type.hours_of_operation',
          repeat_interval: null,
          repeat_interval_type_id: '2',
          advance_notify_interval: null,
          advance_notify_interval_type_id: '2',
          stop_repeating_at: null,
          last_triggered_value: '0',
          status: 'VALID',
          completion_value: null,
          completion_comment: null,
          completed_ts: null,
          reminder_id: '23685' as ReminderId,
          comment: 'Oil change',
          vehicle: {
            id: '37000538',
            name: 'TEST_PRIV',
          },
          driver: null,
          category_name: 'backEnd.admin.reminder.category.service',
          category_name_translation_id:
            'backEnd.admin.reminder.category.service' as Standard_BE_TranslationId,
          reminder_category_id: '1' as ReminderCategoryId,
          reminder_category_user_id: null,
          clock: '2809',
          reminder_event_id: null,
          expiry_value: null,
          current_value: 0,
          next_reminder: null,
        },
      ] satisfies FetchRemindersList.ApiOutput,
    },
  }),
}
