import type { ApiNote } from 'src/modules/detail/DriverNotes/types'
import type { StaticResponse } from 'cypress/types/net-stubbing'

export const driverNotesEndpointMocks = {
  ct_fleet_get_driver_notes: (apiNotes: Array<ApiNote>) => ({
    successReply: {
      delay: 100,
      body: {
        error: null,
        id: 10,
        result: {
          ct_fleet_get_driver_notes: apiNotes,
        },
      },
    } satisfies StaticResponse,
  }),
  ct_fleet_add_driver_notes: (apiNote: ApiNote) => ({
    successReply: {
      delay: 100,
      body: {
        error: null,
        id: 10,
        result: {
          ct_fleet_add_driver_notes: [apiNote],
        },
      },
    } satisfies StaticResponse,
  }),
  ct_fleet_update_driver_notes: () => ({
    successReply: {
      delay: 100,
      body: {
        error: null,
        id: 10,
        result: 'Updated all records',
      },
    } satisfies StaticResponse,
  }),
  ct_fleet_delete_driver_notes: () => ({
    successReply: {
      delay: 100,
      body: {
        error: null,
        id: 10,
        result: 'Deleted all records',
      },
    } satisfies StaticResponse,
  }),
}
