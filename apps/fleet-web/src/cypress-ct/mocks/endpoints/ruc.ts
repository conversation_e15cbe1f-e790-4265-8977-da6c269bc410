import type { VehicleId } from 'api/types'
import type { StaticResponse } from 'cypress/types/net-stubbing'
import type {
  Fetch_vehicles_with_ruc_addon,
  RucLicenseId,
} from 'src/modules/ruc/api/types'

export const rucEndpointMocks = {
  fetch_vehicles_with_ruc_addon: () => ({
    successReply: {
      delay: 50,
      body: {
        id: 10,
        result: [
          {
            vehicle_id: '15652' as VehicleId,
            registration: 'HDH45',
            odometerInKm: 1310,
            licenses: [
              {
                license_id: '1' as RucLicenseId,
                issueDate: '2023-01-01 18:05:41.758634+00',
                licenseNumber: '41674',
                validFromInKm: 500,
                validToInKm: 1400,
                status: 'VALID',
                distRecorder: null,
                deleted_by: null,
                deleted_ts: null,
              },
              {
                license_id: '2' as RucLicenseId,
                issueDate: '2022-04-01 18:05:41.758634+00',
                licenseNumber: '523125',
                validFromInKm: 0,
                validToInKm: 500,
                status: 'EXPIRED',
                distRecorder: null,
                deleted_by: null,
                deleted_ts: null,
              },
            ],
          },
          {
            vehicle_id: '2123' as VehicleId,
            registration: 'AEJSO21',
            odometerInKm: 123,
            licenses: [],
          },
          {
            vehicle_id: 'myboy with one license' as VehicleId,
            registration: 'ADGET231',
            odometerInKm: 1023,
            licenses: [
              {
                license_id: '21113_2' as RucLicenseId,
                issueDate: '2022-04-01 18:05:41.758634+00',
                licenseNumber: '523125',
                validFromInKm: 0,
                validToInKm: 500,
                status: 'EXPIRED',
                distRecorder: null,
                deleted_by: null,
                deleted_ts: null,
              },
            ],
          },
          {
            vehicle_id: '3542' as VehicleId,
            registration: 'BO01ASD',
            odometerInKm: 1020,
            licenses: [
              {
                license_id: '12' as RucLicenseId,
                issueDate: '2023-03-01 18:05:41.758634+00',
                licenseNumber: '123132',
                validFromInKm: 500.2,
                validToInKm: 1400,
                status: 'VALID',
                distRecorder: null,
                deleted_by: null,
                deleted_ts: null,
              },
              {
                license_id: '22' as RucLicenseId,
                issueDate: '2022-06-01 18:05:41.758634+00',
                licenseNumber: '304123',
                validFromInKm: 0,
                validToInKm: 500,
                status: 'EXPIRED',
                distRecorder: null,
                deleted_by: null,
                deleted_ts: null,
              },
            ],
          },
        ] satisfies Fetch_vehicles_with_ruc_addon.ApiOutput,
        error: null,
      },
    } satisfies StaticResponse,
  }),
}
