import type { Ct_fleet_get_start_prevent_settings_section } from 'src/modules/admin/user-settings/ImmobiliseVehicleSection/api/types'

export const userProfileEndpointMocks = {
  ct_fleet_get_start_prevent_settings_section: () => ({
    delay: 100,
    body: {
      id: 10,
      result: {
        appImobForceSubUserOTP: false,
        appImobForceUserOTP: false,
        status: 'OK',
      } satisfies Ct_fleet_get_start_prevent_settings_section.ApiOutput,
    },
  }),
  ct_fleet_delete_two_factor_auth_type: () => ({
    delay: 100,
    body: {
      id: 10,
      result: 'OK',
    },
  }),
  ct_fleet_two_factor_validate_otp: () => ({
    delay: 100,
    body: {
      id: 10,
      result: { isOTPValid: true },
    },
  }),
  ct_fleet_update_two_factor_auth_type: () => ({
    delay: 100,
    body: {
      id: 10,
      result: { otp_expiration_in_minutes: 5 },
    },
  }),
  ct_fleet_two_factor_auth_validate_password: () => ({
    delay: 100,
    body: {
      id: 10,
      result: { isPasswordValid: true },
    },
  }),
  ct_fleet_activate_two_factor_auth: () => ({
    delay: 100,
    body: {
      id: 10,
      result: 'OK',
    },
  }),
  ct_fleet_get_user_profile_settings: (tfaConfig: {
    enabled: boolean
    email: string | null
    phone: string | null
    email_date_added: string | null
    phone_date_added: string | null
  }) => ({
    delay: 100,
    body: {
      id: 10,
      result: {
        personal_settings: {
          username: 'CART00003',
          logo: '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',
          email: '<EMAIL>',
          gps_format: 'D',
          date_time_format: '0',
          timezone: 'Asia/Singapore',
          timezone_from_pc: false,
          timezone_of_user: null,
          road_description_type: 'principal',
          mobile_number: '+**********',
        },
        access_keys: {
          sso: {
            key: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhY2NvdW50IjoiQ0FSVDAwMDAzIiwicGFzc3dvcmRfaGFzaCI6IjA5MDIyQ0VFMzU3QkJBMjE4RTMxMzA0MjFBODg3MjQxMkJCRkVDRjUiLCJjbGllbnRfdXNlcl9uYW1lIjpudWxsfQ.4YFGTKN5Rvg3p49uuuB6uqVMcIXkYVCBeKbCcV9KZGs',
          },
          api: {
            key: 'bcd773dc4a6a2a4094a89342ce031a5d225fa7121ba83da4b16145e3fae276b6',
          },
        },
        two_factor_authentication: tfaConfig,
        mifleet_settings: {
          user_name: 'CART00046',
          language_id: '1',
        },
      },
    },
  }),
}

export const TWOFA_DISABLED = {
  enabled: false,
  email: '',
  phone: null,
  email_date_added: '',
  phone_date_added: null,
}

export const TWOFA_ENABLED_WITHOUT_CONTACT = {
  enabled: true,
  email: '',
  phone: null,
  email_date_added: '',
  phone_date_added: null,
}

export const TWOFA_ENABLED_WITH_EMAIL = {
  enabled: true,
  email: 'joseph.feng@cartrack',
  phone: null,
  email_date_added: '2023-05-22 14:38:04.125653+08',
  phone_date_added: null,
}

export const TWOFA_ENABLED_WITH_PHONE = {
  enabled: true,
  email: null,
  phone: '+6587665679',
  email_date_added: null,
  phone_date_added: '2023-05-22 14:38:04.125653+08',
}
