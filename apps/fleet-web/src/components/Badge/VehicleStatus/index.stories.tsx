import * as React from 'react'
import { select } from '@storybook/addon-knobs'
import VehicleStatusBadge from '.'

export default {
  component: VehicleStatusBadge,
}

export const Normal = () => {
  const status = select(
    'Vehicle Status',
    vehicleStatusOptions,
    vehicleStatusOptions['Driving'],
  )

  return <VehicleStatusBadge status={status} />
}

const vehicleStatusOptions: Record<
  string,
  React.ComponentProps<typeof VehicleStatusBadge>['status']
> = {
  Driving: 'DRIVING',
  Idling: 'IDLING',
  'Ignition Off': 'IGNITION_OFF',
  'No Signal': 'NO_SIGNAL',
  Speeding: 'SPEEDING',
}
