import styled, { css } from 'styled-components'
import { startCase } from 'lodash'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import type { VehicleStatus } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import theme from 'src/components/_themes/default'
import { ctIntl } from 'src/util-components/ctIntl'
import { variables } from 'src/shared/components/styled/global-styles'

type Props = {
  status: VehicleStatus
}

const VehicleStatusBadge = ({ status }: Props) => (
  <Container status={status}>
    <Label status={status}>{ctIntl.formatMessage({ id: startCase(status) })}</Label>
  </Container>
)

export default VehicleStatusBadge

const Container = styled.div<{ status: Props['status'] }>`
  ${(props) => props.theme.positioning.mixins.flex.center};
  display: inline-flex;
  min-width: 74px;
  border-radius: 39px;
  padding: 3px ${spacing[2]};
  ${(props) =>
    props.status ===
    'NO_SIGNAL' /** TEMPORARY FIX - NO SIGNAL state will be redesigned and follow other state design closer */
      ? css`
          background: white;
          border: 1px solid ${variables.gray60};
        `
      : css`
          background-color: ${theme.colors.vehicleStatus[props.status]};
        `}
  max-width: 173px;
`

const Label = styled.span<{ status: Props['status'] }>`
  font-size: 11px;
  color: ${(props) => (props.status === 'NO_SIGNAL' ? variables.gray60 : '#ffffff')};
  text-overflow: ellipsis;
  text-transform: uppercase;
  ${(props) => props.theme.typography.mixins.robotoBold};
  word-break: break-word;
  text-align: center;
  text-wrap: wrap;
`
