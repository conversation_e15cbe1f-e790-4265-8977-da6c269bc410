import * as React from 'react'
import { select, text } from '@storybook/addon-knobs'
import Badge from '.'

export default {
  component: Badge,
}

export const Normal = () => {
  const color = select('Color', colorOptions, colorOptions['Blue'])
  const label = text('Badge Label', 'Badge Label')

  return (
    <Badge color={color}>
      <Badge.Label>{label}</Badge.Label>
    </Badge>
  )
}

const colorOptions: Record<string, React.ComponentProps<typeof Badge>['color']> = {
  Blue: 'blue',
  Cyan: 'cyan',
  'Light Blue': 'lightBlue',
  Red: 'red',
  Green: 'green',
  Gray: 'gray',
  Yellow: 'yellow',
}
