import styled from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'

const Container = styled.div<{
  color?: 'green' | 'red' | 'cyan' | 'blue' | 'lightBlue' | 'gray' | 'yellow'
  backgroundColor?: string
}>`
  ${(props) => props.theme.positioning.mixins.flex.center};
  background-color: ${(props) =>
    props.color
      ? {
          blue: '#5099CE',
          cyan: '#53B8C6',
          lightBlue: '#73ADD7',
          red: '#CE5239',
          green: '#2DAB33',
          gray: '#666666',
          orange: '#f47735',
          yellow: '#FFBD4D',
        }[props.color]
      : props.backgroundColor};
  height: 24px;
  border-radius: 11px;
  width: 83px;
  min-width: min-content;
  padding: ${spacing[1]};
`

const Label = styled.span`
  font-size: 11px;
  line-height: 13px;
  color: white;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  ${(props) => props.theme.typography.mixins.robotoBold};
`

const Badge = Object.assign(Container, { Label })

export default Badge
