import * as React from 'react'
import type { Props as ReactModalProps } from 'react-modal'
import styled from 'styled-components'
import BaseModal, { type ModalProps } from './BaseModal'
import Button, { type ButtonProps } from '../../../util-components/button-storeless'

import { ctIntl } from 'src/util-components/ctIntl'

function ConfirmationModal({
  title,
  header,
  subContent,
  children,
  onClose,
  rejectLabel,
  confirmLabel,
  isOpen,
  extraClassNames,
  onResult,
  style,
  modalIcon,
  buttonStyle,
  ...props
}: Props) {
  const handleConfirm = () => {
    onResult && onResult(true)
  }
  const handleReject = () => {
    onResult && onResult(false)
  }
  return (
    <BaseModal
      {...props}
      onClose={onClose}
      isOpen={isOpen}
      style={style}
      title={title}
      extraClassNames={extraClassNames}
    >
      {modalIcon && <IconContainer>{modalIcon}</IconContainer>}
      {header && (
        <ConfirmationHeader>
          {ctIntl.formatMessage({ id: `${header}` })}
        </ConfirmationHeader>
      )}
      {subContent && (
        <ConfirmationSubContent>
          {ctIntl.formatMessage({ id: `${subContent}` })}
        </ConfirmationSubContent>
      )}
      {children && <div>{children}</div>}
      <ConfirmationButtons>
        <Button
          grouped
          label={rejectLabel}
          onClick={handleReject}
        />
        <Button
          grouped
          label={confirmLabel}
          style={buttonStyle}
          onClick={handleConfirm}
        />
      </ConfirmationButtons>
    </BaseModal>
  )
}

type Props = {
  header?: string
  subContent?: string
  title?: string
  modalIcon?: JSX.Element
  children?: React.ReactNode
  isOpen?: boolean
  hideCloseIcon?: boolean
  onClose?: (event: React.MouseEvent) => void
  onResult?: (value: boolean) => void
  rejectLabel?: string
  confirmLabel?: string
  buttonStyle?: ButtonProps
  hideTitle?: boolean
  extraClassNames?: ModalProps['extraClassNames'] & {
    buttonsClassName?: string
  }
} & ReactModalProps

export default ConfirmationModal

const IconContainer = styled.div`
  height: auto;
  width: 60px;
  margin-top: 10px;
`

const ConfirmationHeader = styled.div`
  font-size: 28px;
  line-height: 33px;
  font-weight: 400;
  margin-top: 18px;
  font-family: 'Roboto Condensed', sans-serif;
`
const ConfirmationSubContent = styled.div`
  font-size: 18px;
  line-height: 21px;
  margin: 10px auto;
  max-width: 225px;
  font-family: 'Roboto Condensed', sans-serif;
`

const ConfirmationButtons = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 22px;
`
