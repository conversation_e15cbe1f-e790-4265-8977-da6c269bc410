import { useEffect, type ReactNode } from 'react'
import type { FixMeAny } from 'src/types'
import ReactModal, { type Props as ReactModalProps } from 'react-modal'
import { ctIntl } from 'src/util-components/ctIntl'
import Close from '../../Icon/Close'
import styled from 'styled-components'

const BaseModal = ({
  onClose = () => {},
  title,
  style,
  extraClassNames = {},
  ...props
}: ModalProps) => {
  useEffect(() => {
    // https://github.com/reactjs/react-modal/issues/133#issuecomment-194034344
    ReactModal.setAppElement('body')
  }, [])
  return (
    <StyledBaseModal
      {...props}
      contentLabel={title}
      style={{ ...style }}
      onRequestClose={onClose}
      className={extraClassNames.contentClassName}
      overlayClassName={extraClassNames.overlayClassName}
      shouldCloseOnOverlayClick
    >
      <div>
        <Header>
          {title ? (
            <ModalTitle>{title && ctIntl.formatMessage({ id: title })}</ModalTitle>
          ) : null}
          {props.hideCloseIcon ? null : (
            <ModalHeader>
              <ModalClosedWrapper onClick={onClose}>
                <CrossIcon />
              </ModalClosedWrapper>
            </ModalHeader>
          )}
        </Header>
        <ModalBody>{props.children}</ModalBody>
      </div>
    </StyledBaseModal>
  )
}

export type ModalProps = {
  hideCloseIcon?: boolean
  children?: ReactNode
  onClose?: (event: FixMeAny) => any
  title?: ReactModalProps['contentLabel']
  extraClassNames?: {
    contentClassName?: string
    overlayClassName?: string
    modalClassName?: string
    headerClassName?: string
    bodyClassName?: string
  }
} & ReactModalProps

export default BaseModal

const StyledBaseModal = styled((props) => <ReactModal {...props} />)`
  background: white;
  border: 1px solid rgb(204, 204, 204);
  border-radius: 4px;
  left: 50%;
  outline: none;
  overflow: visible !important;
  padding: 20px;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 5px 5px 25px 0 rgba(179, 179, 179, 0.25);
  z-index: 1000;
`
const Header = styled.div`
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  height: 40px;
  left: 0;
  padding: 0 20px;
  position: absolute;
  right: 0;
  top: 20px;
`

const ModalTitle = styled.div`
  align-self: center;
  color: #333;
  font-family: $roboto;
  font-size: 14px;
  font-weight: bold;
`

const ModalHeader = styled.div`
  display: flex;
  height: auto;
  width: auto;
  justify-items: flex-end;
  margin-left: auto;
`

const ModalClosedWrapper = styled.div`
  align-items: center;
  border: 1px solid #cccccc;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  height: 35px;
  justify-content: center;
  width: 35px;

  &:hover {
    opacity: 0.8;
  }
`
const CrossIcon = styled((props) => <Close {...props} />)`
  height: 16px;
  width: 11px;
`
const ModalBody = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  height: calc(100% - 25px);
  margin-top: 25px;
  text-align: center;
  padding: 0 24px;
`
