import { boolean, color, text } from '@storybook/addon-knobs'
export const commonModalKnobsOption = () => {
  const hideCloseIcon = boolean('Hide Close Icon', false)
  const height = text('Height Of Modal', '300px')
  const width = text('Width Of Modal', '455px')
  const title = boolean('Enable Title', false)
  const titleText = text('Title', 'Enable Title to See this.')

  return {
    hideCloseIcon,
    height,
    width,
    title,
    titleText,
  }
}

export const CommonConfirmationModalKnobs = () => {
  const headerText = text('Header', 'Header Text Here')
  const subContent = text('Sub Content', 'Sub Content Here')
  const confirmButtonText = text('Confirm Button Text', 'OK')
  const confirmButtonColor = color('Confirm Button Color', '#CE5239')

  return {
    headerText,
    subContent,
    confirmButtonText,
    confirmButtonColor,
  }
}

export const modalIcon = () => <div>Icon Here</div>
