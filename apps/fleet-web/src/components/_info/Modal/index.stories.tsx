/* eslint-disable storybook/no-title-property-in-meta */
/* eslint-disable storybook/csf-component */
import { useState } from 'react'
import BaseModal from './BaseModal'
import ConfirmationModal from './ConfirmModal'
import AlertModal from './AlertModal'
import { text } from '@storybook/addon-knobs'
import Button from '../../../util-components/button-storeless'
import {
  commonModalKnobsOption,
  CommonConfirmationModalKnobs,
  modalIcon,
} from './utils'

export default {
  title: 'Info/Modal',
}

const BaseModalExample = () => {
  const [isModalOpen, setModalState] = useState(false)

  const handleClicked = () => {
    const newValue = !isModalOpen
    setModalState(newValue)
  }

  const { title, titleText, hideCloseIcon, height, width } = commonModalKnobsOption()

  return (
    <div>
      <Button
        onClick={handleClicked}
        label={'Click to Open Modal'}
      />
      <BaseModal
        isOpen={isModalOpen}
        onClose={handleClicked}
        title={title ? titleText : ''}
        hideCloseIcon={hideCloseIcon}
        style={{
          content: {
            height: height,
            width: width,
          },
        }}
      >
        <div>
          <h1>Children Here</h1>
          <p>Rest of children...</p>
        </div>
      </BaseModal>
    </div>
  )
}

const ConfirmModalExample = () => {
  const [isModalOpen, setModalState] = useState(false)
  const cancelButtonText = text('Cancel Button Text', 'Cancel')

  const handleClicked = () => {
    const newValue = !isModalOpen
    setModalState(newValue)
  }

  const handleConfirm = (confirmed: boolean) => {
    if (confirmed) {
      // do clicked actions such as delete or ok
    }

    setModalState(false)
  }

  const { title, titleText, hideCloseIcon, height, width } = commonModalKnobsOption()

  const { headerText, subContent, confirmButtonColor, confirmButtonText } =
    CommonConfirmationModalKnobs()

  return (
    <div>
      <Button
        onClick={handleClicked}
        label={'Click to Open Modal'}
      />
      <ConfirmationModal
        isOpen={isModalOpen}
        onClose={handleClicked}
        title={title ? titleText : ''}
        onResult={handleConfirm}
        hideCloseIcon={hideCloseIcon}
        style={{
          content: {
            height: height,
            width: width,
          },
        }}
        header={headerText}
        buttonStyle={{
          color: confirmButtonColor,
        }}
        confirmLabel={confirmButtonText}
        rejectLabel={cancelButtonText}
        subContent={subContent}
        modalIcon={modalIcon()}
      />
    </div>
  )
}

const AlertModalExample = () => {
  const [isModalOpen, setModalState] = useState(false)

  const { title, titleText, hideCloseIcon, height, width } = commonModalKnobsOption()

  const { headerText, subContent, confirmButtonColor, confirmButtonText } =
    CommonConfirmationModalKnobs()
  const modalIcon = () => <div>Icon Here</div>

  const handleClicked = () => {
    const newValue = !isModalOpen
    setModalState(newValue)
  }

  const handleConfirm = () => {
    setModalState(false)
  }

  return (
    <div>
      <Button
        onClick={handleClicked}
        label={'Click to Open Modal'}
      />
      <AlertModal
        isOpen={isModalOpen}
        onClose={handleClicked}
        title={title ? titleText : ''}
        onResult={handleConfirm}
        hideCloseIcon={hideCloseIcon}
        style={{
          content: {
            height: height,
            width: width,
          },
        }}
        header={headerText}
        buttonStyle={{
          color: confirmButtonColor,
        }}
        confirmLabel={confirmButtonText}
        subContent={subContent}
        modalIcon={modalIcon()}
      />
    </div>
  )
}

export const Normal = () => <BaseModalExample />
export const ConfirmModel = () => <ConfirmModalExample />
export const AlertModel = () => <AlertModalExample />
