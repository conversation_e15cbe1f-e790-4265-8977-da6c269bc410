import * as React from 'react'
import { ctIntl } from 'src/util-components/ctIntl'
import styled from 'styled-components'
import BaseModal, { type ModalProps } from './BaseModal'
import Button, { type ButtonProps } from '../../../util-components/button-storeless'
import type { Props as ReactModalProps } from 'react-modal'
import type { FixMeAny } from 'src/types'

const AlertModal = ({
  title,
  header,
  subContent,
  children,
  childrenContainerStyle,
  onClose,
  confirmLabel,
  isOpen,
  extraClassNames,
  onResult,
  style,
  modalIcon,
  buttonStyle,
  confirmButtonProps,
  noConfirmationButton = false,
  ...props
}: Props) => (
  <BaseModal
    {...props}
    onClose={onClose}
    isOpen={isOpen}
    style={style}
    title={title}
    extraClassNames={extraClassNames}
  >
    {modalIcon && <IconContainer>{modalIcon}</IconContainer>}
    {header && (
      <ConfirmationHeader>{ctIntl.formatMessage({ id: header })}</ConfirmationHeader>
    )}
    {subContent && (
      <ConfirmationSubContent>
        {ctIntl.formatMessage({ id: subContent })}
      </ConfirmationSubContent>
    )}
    {children && <div style={childrenContainerStyle}>{children}</div>}
    {!noConfirmationButton && (
      <ConfirmationButton>
        <Button
          grouped
          label={confirmLabel}
          style={buttonStyle}
          onClick={onResult}
          {...confirmButtonProps}
        />
      </ConfirmationButton>
    )}
  </BaseModal>
)

type Props = {
  header?: string
  subContent?: string
  title?: string
  modalIcon?: JSX.Element
  children?: React.ReactNode
  childrenContainerStyle?: FixMeAny
  isOpen?: boolean
  hideCloseIcon?: boolean
  onClose?: (event: FixMeAny) => any
  onResult?: (event: FixMeAny) => any
  confirmLabel?: string
  buttonStyle?: ButtonProps
  hideTitle?: boolean
  extraClassNames?: ModalProps['extraClassNames'] & {
    buttonsClassName?: string
  }
  confirmButtonProps?: ButtonProps
  noConfirmationButton?: boolean
} & ReactModalProps

export default AlertModal

const IconContainer = styled.div`
  height: auto;
  width: 60px;
  margin-top: 10px;
`

const ConfirmationHeader = styled.div`
  font-size: 28px;
  line-height: 33px;
  font-weight: 400;
  margin-top: 18px;
  font-family: 'Roboto Condensed', sans-serif;
`
const ConfirmationSubContent = styled.div`
  font-size: 18px;
  line-height: 21px;
  margin: 10px auto;
  max-width: 240px;
  font-family: 'Roboto Condensed', sans-serif;
`

const ConfirmationButton = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 22px;
`
