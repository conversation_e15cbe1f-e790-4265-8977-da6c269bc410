import styled from 'styled-components'
import Spinner from './index'
import { radios } from '@storybook/addon-knobs'
import type { FixMeAny } from 'src/types'

export default {
  component: Spinner,
}

export const Normal = () => {
  const positions: FixMeAny = {
    Absolute: 'absolute',
    Inline: 'inline',
  }
  const position = radios('Position', positions, positions.Absolute, 'grp-position')

  return (
    <Container>
      <span>The spinner should be appear after this text if it is inline.</span>
      <Spinner absolute={position === positions.Absolute} />
    </Container>
  )
}

const Container = styled.div`
  border: 1px solid gray;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 500px;
  height: 100px;

  > span {
    display: inline-block;
    margin-right: 10px;
  }
`
