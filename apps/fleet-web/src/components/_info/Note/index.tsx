import { Box, styled, Typography } from '@karoo-ui/core'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'

type Props = {
  label: string
  className?: string
}

export const InfoNote = ({ label, className }: Props) => (
  <InfoContainer
    className={className}
    sx={{ gap: 0.5 }}
  >
    <InfoOutlinedIcon htmlColor="#26a5df" />

    <Typography variant="body2">{label}</Typography>
  </InfoContainer>
)

const InfoContainer = styled(Box)`
  display: flex;
  justify-content: center;
  align-items: center;
  width: max-content;
`
