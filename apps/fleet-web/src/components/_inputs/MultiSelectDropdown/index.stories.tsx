import { useState } from 'react'
import MultiSelectDropDown from './index'
import { text } from '@storybook/addon-knobs'
import type { FixMeAny } from 'src/types'

export default {
  component: MultiSelectDropDown,
}

const vehicles = [
  { name: 'vehicle1', value: 'vehicle1', label: 'vehicle1' },
  {
    name: 'vehicle11',
    value: 'vehicle11',
    label: 'vehicle11',
  },
]

export const Normal = () => {
  const width = text('Width', '250px')
  interface State {
    vehicles: Array<{
      name: string
      value: string
      label: string
    }>
  }
  const [filters, setFilters] = useState<State>(() => ({
    vehicles: [],
    selectedItems: [],
  }))

  const handleVehiclesChange = (vehicles: FixMeAny) => {
    setFilters({
      vehicles: vehicles,
    })
  }

  return (
    <MultiSelectDropDown
      value={filters.vehicles}
      options={vehicles}
      onChange={handleVehiclesChange}
      placeholder="Vehicle"
      containerStyles={{ width: width }}
    />
  )
}
