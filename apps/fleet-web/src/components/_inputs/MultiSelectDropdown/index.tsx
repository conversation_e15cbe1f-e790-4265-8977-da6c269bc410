import { type ComponentProps, useState, type CSSProperties } from 'react'

import Select, {
  components,
  type Props as SelectProps,
  type OptionsType,
  type IndicatorProps,
  type ValueContainerProps,
  type StylesConfig,
  type OptionProps,
} from 'react-select'

import { ctIntl } from 'src/util-components/ctIntl'
import { map, isNil } from 'lodash'
import Icon from 'src/components/Icon'
import CheckBox from 'src/util-components/custom/CheckBox/checkbox'
import styled from 'styled-components'
import type { FixMeAny } from 'src/types'

type Props = SelectProps<any> & {
  grouped?: boolean
  required?: boolean
  containerStyles?: CSSProperties
  extraClassNames?: { containerClassNames: string }
  extraValueContainerStyles?: { overflow?: string; maxHeight?: string }
}

const optionsResolver = (options: OptionsType<any>) =>
  map(options, (item) => ({ ...item, value: item.value, label: item.name }))

const DropdownIndicator = (props: IndicatorProps<any>) => (
  <components.DropdownIndicator {...props}>
    <IconStyle icon="caret-down" />
  </components.DropdownIndicator>
)

const ValueContainer = ({ children, ...props }: ValueContainerProps<any>) =>
  props.selectProps.required ? (
    <components.ValueContainer {...props}>
      <RequiredStyle>*</RequiredStyle>
      {children}
    </components.ValueContainer>
  ) : (
    <components.ValueContainer {...props}>{children}</components.ValueContainer>
  )

const MultiSelectDropDown = ({
  defaultValue,
  options,
  grouped = false,
  placeholder = '',
  required,
  value,
  className = '',
  styles,
  containerStyles,
  extraClassNames = { containerClassNames: '' },
  ...props
}: Props) => {
  const [focused, setFocused] = useState(false)

  const showMiniPlaceholder = (value && value.length > 0) || focused

  const placeholderValue = !isNil(placeholder)
    ? ctIntl.formatMessage({ id: placeholder as string })
    : ''

  const customStyles: StylesConfig = {
    ...styles,
    valueContainer: (base) => ({
      ...base,
      marginTop: showMiniPlaceholder ? '2px' : 'initial',
    }),
    option: (styles, { isFocused, isSelected }) => ({
      ...styles,
      // eslint-disable-next-line no-nested-ternary
      backgroundColor: isSelected ? 'white' : isFocused ? '#eee' : 'white',
      color: isSelected && 'black',
    }),
  }

  return (
    <DropDownContainer style={{ ...containerStyles }}>
      {options && options.length > 0 ? (
        <Select
          {...props}
          value={value}
          defaultValue={defaultValue || []}
          options={grouped ? options : optionsResolver(options)}
          placeholder={!showMiniPlaceholder && placeholderValue}
          isMulti
          isRtl={false}
          blurInputOnSelect={false}
          components={{
            DropdownIndicator,
            ValueContainer,
            Option,
          }}
          className={className || extraClassNames.containerClassNames}
          styles={customStyles}
          onBlur={() => setFocused(false)}
          onFocus={() => setFocused(true)}
        />
      ) : (
        <p>No Options Available</p>
      )}
      {showMiniPlaceholder && <PlaceHolderStyle>{placeholderValue}</PlaceHolderStyle>}
    </DropDownContainer>
  )
}

export default MultiSelectDropDown

const Option = (optionProps: OptionProps<FixMeAny> & Record<string, any>) => (
  <components.Option {...optionProps}>
    <CheckBox
      checked={optionProps.isSelected}
      value={optionProps.value}
      label={optionProps.value}
    />
  </components.Option>
)

const DropDownContainer = styled.div`
  position: relative;
  min-height: 38px;
  min-width: 240px;
  max-width: 300px;
  width: ${(props) => props.style && props.style.width};
`

const PlaceHolderStyle = styled.span`
  color: #666;
  font-size: 6px;
  left: 13px;
  position: absolute;
  text-transform: uppercase;
  top: 2px;
`

const RequiredStyle = styled.div`
  color: #ffce5239;
  font-size: 12px;
  left: 0;
  padding-left: 5px;
  position: absolute;
  top: 4px;
`

const IconStyle = styled((props: ComponentProps<typeof Icon>) => <Icon {...props} />)`
  margin: 2px 5px;
  color: #666;
`
