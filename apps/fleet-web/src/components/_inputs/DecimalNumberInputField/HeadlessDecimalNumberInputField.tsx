import type { FormikProps, FormikValues } from 'formik'
import { get } from 'lodash'
import { generateMetaProps } from 'src/shared/formik'
import { messages } from 'src/shared/formik/messages'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  useDecimalNumberInput,
  type UseDecimalNumberInputProps,
} from '../DecimalNumberInput/HeadlessDecimalNumberInput'

export type UseDecimalNumberInputFieldProps = {
  form: Pick<
    FormikProps<FormikValues>,
    'values' | 'touched' | 'errors' | 'setStatus' | 'status'
  >
  name: string
  /** Very niche prop that probably won't be needed in the future */
  statusFieldName?: string
} & Omit<UseDecimalNumberInputProps, 'value'>

export function useDecimalNumberInputField({
  form,
  name,
  statusFieldName = name,
  decimalSeparators,
  onValuesChange,
}: UseDecimalNumberInputFieldProps) {
  const onValuesChangeHandler: UseDecimalNumberInputProps['onValuesChange'] = (
    values,
  ) => {
    const { inputValue } = values

    onValuesChange(values)
    if (ENV.FEAT_MIFLEET_DECIMAL_SEPARATORS === 'true') {
      const status = { ...(form.status ?? {}) }

      if (status) {
        delete status[statusFieldName]
      }

      form.setStatus(status)

      switch (decimalSeparators.insert) {
        case 'comma': {
          if (inputValue.includes('.')) {
            form.setStatus({
              ...status,
              [statusFieldName]: ctIntl.formatMessage(
                { id: messages.validDecimalSeparator },
                { values: { invalidSeparator: '.', supportedSeparator: ',' } },
              ),
            })
          }
          break
        }
        case 'dot': {
          if (inputValue.includes(',')) {
            form.setStatus({
              ...status,
              [statusFieldName]: ctIntl.formatMessage(
                { id: messages.validDecimalSeparator },
                { values: { invalidSeparator: ',', supportedSeparator: '.' } },
              ),
            })
          }
          break
        }
      }
    }
  }

  const value = get(form.values, name)

  const { onChange, values } = useDecimalNumberInput({
    decimalSeparators,
    onValuesChange: onValuesChangeHandler,
    value,
  })

  return {
    meta: generateMetaProps(form, name, {
      extraValidation: () => (form.status ?? {})[statusFieldName],
    }),
    onChange,
    values,
  }
}

type Props = {
  children: (bag: ReturnType<typeof useDecimalNumberInputField>) => JSX.Element
} & UseDecimalNumberInputFieldProps

/**
 * When using this component make sure you use the formik field __status__ to verify is the form has errors or not
 */
export default function HeadlessDecimalNumberInputField({
  decimalSeparators,
  form,
  name,
  statusFieldName,
  onValuesChange,
  children,
}: Props) {
  const bag = useDecimalNumberInputField({
    onValuesChange,
    decimalSeparators,
    statusFieldName,
    name,
    form,
  })

  return children(bag)
}
