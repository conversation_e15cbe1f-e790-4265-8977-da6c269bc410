import TextInput from 'src/util-components/text-input'
import type { DecimalNumberInnerTextInputProps } from '../DecimalNumberInput'
import {
  useDecimalNumberInputField,
  type UseDecimalNumberInputFieldProps,
} from './HeadlessDecimalNumberInputField'

type Props = UseDecimalNumberInputFieldProps &
  Omit<DecimalNumberInnerTextInputProps, 'meta' | 'value'>
/**
 * When using this component make sure you use the formik field __status__ to verify is the form has errors or not
 */
export default function DecimalNumberInputField({
  decimalSeparators,
  form,
  name,
  statusFieldName = name,
  onValuesChange,
  ...rest
}: Props) {
  const { values, onChange, meta } = useDecimalNumberInputField({
    onValuesChange,
    decimalSeparators,
    form,
    name,
    statusFieldName,
  })

  return (
    <TextInput
      {...rest}
      meta={meta}
      name={name}
      value={values.inputValue}
      forceOriginalValue
      onChange={onChange}
    />
  )
}
