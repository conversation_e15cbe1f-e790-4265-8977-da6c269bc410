import type {
  DecimalSeparatorFormatType,
  DecimalSeparatorInsertType,
} from 'api/user/types'
import { type InputHTMLAttributes, useEffect, useState } from 'react'
import intlNumbers from 'src/util-functions/intl-numbers'

type Value = string | number | null | undefined

export type UseDecimalNumberInputProps = {
  value: Value
  decimalSeparators: {
    insert: DecimalSeparatorInsertType
    format: DecimalSeparatorFormatType
  }
  onValuesChange: (values: { inputValue: string; decimalValue: string }) => void
}

type Values = {
  inputValue: UseDecimalNumberInputProps['value']
  decimalValue: Value
}

export function useDecimalNumberInput({
  value,
  decimalSeparators,
  onValuesChange,
}: UseDecimalNumberInputProps) {
  const [values, setValues] = useState<Values>(() => ({
    inputValue: intlNumbers.formatDecimalValue(value ?? '', {
      decimalSeparator: decimalSeparators.format,
    }),
    decimalValue: value,
  }))

  useEffect(() => {
    if (value !== values.decimalValue) {
      setValues({
        decimalValue: value,
        inputValue: intlNumbers.formatDecimalValue(value ?? '', {
          decimalSeparator: decimalSeparators.format,
        }),
      })
    }
  }, [decimalSeparators.format, value, values.decimalValue])

  const onChange: InputHTMLAttributes<HTMLInputElement>['onChange'] = (e) => {
    const inputValue = e.currentTarget.value
    const decimalValue = intlNumbers.replaceCommaByDot(inputValue)

    setValues({ inputValue, decimalValue })
    onValuesChange({ inputValue, decimalValue })
  }

  return { values, onChange } as const
}

export type HeadlessDecimalNumberInputProps = {
  children: (bag: ReturnType<typeof useDecimalNumberInput>) => JSX.Element
} & UseDecimalNumberInputProps

export function HeadlessDecimalNumberInput({
  value,
  decimalSeparators,
  onValuesChange,
  children,
}: HeadlessDecimalNumberInputProps) {
  const bag = useDecimalNumberInput({
    value,
    decimalSeparators,
    onValuesChange,
  })

  return children(bag)
}
