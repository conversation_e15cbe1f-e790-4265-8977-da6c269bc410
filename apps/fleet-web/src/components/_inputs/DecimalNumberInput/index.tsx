import TextInput from 'src/util-components/text-input'
import type { ComponentProps } from 'react'
import {
  useDecimalNumberInput,
  type UseDecimalNumberInputProps,
} from './HeadlessDecimalNumberInput'

export type DecimalNumberInnerTextInputProps = Omit<
  ComponentProps<typeof TextInput>,
  'value' | 'onChange' | 'forceOriginalValue' | 'textArea' | 'type'
>

export type DecimalNumberInputProps = UseDecimalNumberInputProps &
  DecimalNumberInnerTextInputProps

export default function DecimalNumberInput({
  value,
  decimalSeparators,
  onValuesChange,
  ...rest
}: DecimalNumberInputProps) {
  const { onChange, values } = useDecimalNumberInput({
    decimalSeparators,
    onValuesChange,
    value,
  })

  return (
    <TextInput
      {...rest}
      value={values.inputValue}
      forceOriginalValue
      onChange={onChange}
    />
  )
}
