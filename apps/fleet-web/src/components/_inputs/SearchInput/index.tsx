import styled from 'styled-components'
import SearchBar from 'src/util-components/search-bar'
import type { ComponentProps } from 'react'

export default function SearchInput(
  props: Omit<ComponentProps<typeof SearchBar>, 'forceOriginalValue'>,
) {
  return (
    <StyledSearchBar
      forceOriginalValue
      {...props}
    />
  )
}

const StyledSearchBar = styled(SearchBar)`
  min-width: min-content;
`
