import { useState, useMemo, type ChangeEvent } from 'react'
import styled from 'styled-components'
import Select from 'src/util-components/custom/InputDropdown'
import { ctIntl } from 'src/util-components/ctIntl'
import type { FixMeAny } from 'src/types'
import SearchInput from '..'
import { variables } from 'src/shared/components/styled/global-styles'

type MultiSearchInputProps<T> = {
  searchText: string
  searchType: T
  options: Array<{
    name: string
    value: T
  }>
  onTextChange: (event: ChangeEvent<HTMLInputElement>) => void
  onTypeChange: (type: T) => void
  clearable?: boolean
  onClear?: () => void
}

function MultiSearchInput<T>({
  searchText,
  searchType,
  options,
  onTextChange,
  onTypeChange,
  clearable = false,
  onClear,
}: MultiSearchInputProps<T>) {
  const [isMenuOpen, setMenuOpen] = useState<boolean>(false)

  const activeOption = useMemo(
    () =>
      options.find((option) => option.value === searchType) || {
        name: ctIntl.formatMessage({ id: 'Search' }),
        value: '',
      },
    [options, searchType],
  )

  const handleSelectChange = (type: { name: string; label: string; value: T }) => {
    onTypeChange(type.value)
  }

  return (
    <Container>
      <StyledSearchInput
        placeholder={ctIntl.formatMessage({
          id: activeOption.name,
        })}
        value={searchText}
        onChange={onTextChange}
        clearable={clearable}
        onClear={onClear}
      />
      <StyledSelect
        options={options}
        value={activeOption}
        onChange={handleSelectChange}
        styles={customStyles}
        isMenuOpen={isMenuOpen}
        onMenuOpen={() => setMenuOpen(true)}
        onMenuClose={() => setMenuOpen(false)}
      />
    </Container>
  )
}

export default MultiSearchInput

const Container = styled.div`
  position: relative;
  height: 38px;
  max-width: ${variables.searchBarMaxWidth};
`

const StyledSearchInput = styled(SearchInput)`
  position: relative;
  width: 87%;
  z-index: 2;
`

const StyledSelect = styled(Select)<{ isMenuOpen: boolean }>`
  position: relative;
  top: -38px;
  z-index: ${({ isMenuOpen }) => (isMenuOpen ? 3 : 1)};
`

const customStyles = {
  option: (base: FixMeAny, { isSelected }: FixMeAny) => ({
    ...base,
    backgroundColor: isSelected ? '#eee' : '#fff',
    borderColor: '#f47735',
    color: '#000',
    top: '-30px',
  }),
  container: (base: FixMeAny) => ({
    ...base,
    outline: 'none',
  }),
  menu: (base: FixMeAny) => ({
    ...base,
    borderColor: '#f47735',
    top: '-8px',
    zIndex: 3,
  }),
  control: (base: FixMeAny) => ({
    ...base,
    boxShadow: 0,
    cursor: 'pointer',
    '&:hover': {
      border: '1px solid #ddd',
      outline: 'none',
    },
  }),
  indicatorsContainer: (base: FixMeAny) => ({
    ...base,
    position: 'relative',
    zIndex: 3,
    outline: 'none',
  }),
}
