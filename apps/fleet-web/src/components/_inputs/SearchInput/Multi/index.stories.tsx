import { useState } from 'react'
import MultiSearchInput from '.'
import { useInput } from 'src/hooks'

export default {
  component: MultiSearchInput,
}

export const Normal = () => {
  const searchInputProps = useInput('')
  const [searchType, setSearchType] = useState<'drivers' | 'places'>('drivers')

  return (
    <MultiSearchInput
      searchText={searchInputProps.value}
      searchType={searchType}
      options={[
        {
          name: 'Search Drivers',
          value: 'drivers',
        },
        {
          name: 'Search Places',
          value: 'places',
        },
      ]}
      onTextChange={searchInputProps.onChange}
      onTypeChange={setSearchType}
    />
  )
}
