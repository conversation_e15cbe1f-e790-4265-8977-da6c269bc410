import * as React from 'react'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import VehicleStat from '..'

type Props = React.ComponentProps<typeof VehicleStat> & {
  tooltipProps: Omit<React.ComponentProps<typeof ArrowedTooltip>, 'children'>
}

const VehicleStatWithToolTip = ({ tooltipProps, ...props }: Props) => (
  <ArrowedTooltip {...tooltipProps}>{VehicleStat({ ...props })}</ArrowedTooltip>
)

export default VehicleStatWithToolTip
