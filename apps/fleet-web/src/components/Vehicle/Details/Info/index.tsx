import { Stack, OverflowTypography } from '@karoo-ui/core'
import type { Vehicle } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import { ctIntl } from 'src/util-components/ctIntl'

type Props = {
  isETachoVehicle: Vehicle['isETachoVehicle']
  className?: string
}

const VehicleDetailsInfo = ({ isETachoVehicle, className }: Props) => {
  if (isETachoVehicle) {
    return (
      <Stack
        gap={0.5}
        color="#999"
        alignItems="center"
        flexDirection="row"
        className={className}
        lineHeight="normal"
      >
        <OverflowTypography
          typographyProps={{ variant: 'caption', color: 'text.secondary' }}
        >
          {ctIntl.formatMessage({ id: 'eTachoVehicle' })}
        </OverflowTypography>
      </Stack>
    )
  }

  return null
}

export default VehicleDetailsInfo
