import type { ComponentProps } from 'react'
import * as React from 'react'
import styled from 'styled-components'
import LazyHeadlessTippy from 'src/components/_popups/Tooltip/LazyHeadlessTippy'
import { isEmpty } from 'lodash'
import { generatePopupBoxAndArrow } from 'src/components/_popups/utils'

type Props = {
  children: React.ReactElement
  label: React.ReactNode
  placement?: 'bottom' | 'left' | 'right' | 'top'
  active?: boolean
} & Omit<ComponentProps<typeof LazyHeadlessTippy>, 'placement' | 'ref' | 'render'>

const ContainerTooltip = React.forwardRef<typeof LazyHeadlessTippy, Props>(
  function ContainerToolTip(
    { children, label, placement = 'bottom', popperOptions, active, ...rest },
    ref,
  ) {
    return !isEmpty(label) && !active ? (
      children
    ) : (
      <>
        <LazyHeadlessTippy
          ref={ref}
          interactive
          render={(attrs) => (
            <Container
              tabIndex={-1}
              {...attrs}
            >
              {label}
              <StyledArrow />
            </Container>
          )}
          placement={placement}
          popperOptions={{
            ...popperOptions,
            modifiers: [
              {
                name: 'flip',
                options: {
                  fallbackPlacements: ['bottom', 'top', 'right', 'left'],
                },
              },
            ],
          }}
          {...rest}
        >
          {children}
        </LazyHeadlessTippy>
      </>
    )
  },
)

export default ContainerTooltip

const { Arrow, Box } = generatePopupBoxAndArrow({ arrowWidth: 8 })

const StyledArrow = styled(Arrow)`
  :before {
    background: white;
  }
`

const Container = styled(Box)`
  background-color: white;
  max-width: 455px;
  font-size: 13px;
  font-family: 'Roboto Condensed', sans-serif;
  text-align: center;
  color: #cccccc;
  border-radius: 4px;
  white-space: normal;
  word-break: break-all;
  padding: 10px 5px;
  display: flex;
  flex-direction: column;
  max-height: 200px;
  filter: drop-shadow(0.5px 0.5px 1px rgba(0, 0, 0, 0.3));
`
