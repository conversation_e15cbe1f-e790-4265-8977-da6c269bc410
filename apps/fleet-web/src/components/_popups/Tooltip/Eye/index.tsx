import { forwardRef, type ComponentProps } from 'react'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import styled from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import Icon from 'src/components/Icon'
import { variables } from 'src/shared/components/styled/global-styles'

type Props = Omit<
  ComponentProps<typeof ArrowedTooltip>,
  'interactive' | 'placement'
> & {
  /* Required since using this tooltip should always require an action on the eye icon */
  eyeIconProps: Omit<ComponentProps<typeof Icon>, 'icon'>
}

const EyeTooltip = forwardRef<any, Props>(function EyeTooltip(
  { eyeIconProps, label, ...props },
  ref,
) {
  return (
    <ArrowedTooltip
      ref={ref}
      interactive
      placement="top"
      label={
        <Root>
          <span>{label}</span>
          <Separator />
          <StyledIcon
            icon="eye"
            {...eyeIconProps}
          />
        </Root>
      }
      {...props}
    />
  )
})

export default EyeTooltip

const Root = styled.span`
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: auto auto auto;
`

const Separator = styled.div`
  background-color: ${variables.gray40};
  width: 1px;
  margin: ${spacing[0]} ${spacing[2]};
`

const StyledIcon = styled(Icon)`
  align-self: center;
  cursor: pointer;
`
