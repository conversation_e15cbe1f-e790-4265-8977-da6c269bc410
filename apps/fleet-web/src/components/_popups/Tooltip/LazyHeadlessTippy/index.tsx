import { forwardRef, type ComponentProps, useState } from 'react'
import HeadlessTippy from '../HeadlessTippy'
import type { FixMeAny } from 'src/types'

type Props = ComponentProps<typeof HeadlessTippy>
/**
 * See https://github.com/atomiks/tippyjs-react#lazy-mounting
 * for more info why this component is useful
 *
 * Implementation based of https://gist.github.com/atomiks/520f4b0c7b537202a23a3059d4eec908#file-lazytippy-jsx
 */
const LazyHeadlessTippy = forwardRef<typeof HeadlessTippy, Props>(
  function LazyHeadlessTippy(props, ref) {
    const [mounted, setMounted] = useState(false)

    const lazyPlugin = {
      fn: () => ({
        onShow: () => setMounted(true),
        onHidden: () => setMounted(false),
      }),
    }

    const computedProps = { ...props }

    computedProps.plugins = [lazyPlugin, ...(props.plugins ?? [])]

    if (props.render) {
      computedProps.render = (...args) =>
        mounted ? (props.render as FixMeAny)(...args) : ''
    }

    return (
      <HeadlessTippy
        {...computedProps}
        ref={ref}
      />
    )
  },
)

export default LazyHeadlessTippy
