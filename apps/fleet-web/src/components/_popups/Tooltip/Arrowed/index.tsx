import type { Except } from 'type-fest'
import * as React from 'react'
import { isEmpty } from 'lodash'
import styled from 'styled-components'
import { TooltipCSS } from '..'
import { generatePopupBoxAndArrow } from '../../utils'
import { variables } from 'src/shared/components/styled/global-styles'
import LazyHeadlessTippy from '../LazyHeadlessTippy'

type Props = {
  /**
   * Must have a ref or, if a component, a forwardRef to a DOM element
   */
  children: React.ReactElement
  label: React.ReactNode
  placement?: 'bottom' | 'left' | 'right' | 'top'
} & Except<
  React.ComponentProps<typeof LazyHeadlessTippy>,
  'placement' | 'render' | 'ref'
>

const ArrowedTooltip = React.forwardRef<typeof LazyHeadlessTippy, Props>(
  function ArrowedTooltip(
    { children, label, placement = 'bottom', popperOptions, className, ...rest },
    ref,
  ) {
    return isEmpty(label) ? (
      children
    ) : (
      <>
        <LazyHeadlessTippy
          ref={ref}
          render={(attrs) => (
            <StyledBox
              className={className}
              tabIndex={-1}
              {...attrs}
            >
              {label}
              <StyledArrow />
            </StyledBox>
          )}
          placement={placement}
          popperOptions={{
            ...popperOptions,
            modifiers: [
              {
                name: 'flip',
                options: {
                  fallbackPlacements: ['bottom', 'top', 'right', 'left'],
                },
              },
            ],
          }}
          {...rest}
        >
          {children}
        </LazyHeadlessTippy>
      </>
    )
  },
)

export default ArrowedTooltip

const { Arrow, Box } = generatePopupBoxAndArrow({ arrowWidth: 8 })

const StyledArrow = styled(Arrow)`
  :before {
    background: ${variables.gray80};
  }
`

const StyledBox = styled(Box)`
  ${TooltipCSS};
`
