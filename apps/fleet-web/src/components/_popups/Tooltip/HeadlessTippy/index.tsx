import { forwardRef, type ComponentProps } from 'react'
import Tippy from '@tippyjs/react/headless'
import type { Except } from 'type-fest'

/** https://atomiks.github.io/tippyjs/v6/headless-tippy/ */
type Props = Except<
  ComponentProps<typeof Tippy>,
  | 'allowHTML'
  | 'animateFill'
  | 'animation'
  | 'arrow'
  | 'content'
  | 'duration'
  | 'inertia'
  | 'maxWidth'
  | 'onShown'
  | 'role'
  | 'theme'
>
/** Please favor __LazyHeadlessTippy__, it is more __performant__. */
const HeadlessTippy = forwardRef<typeof Tippy, Props>(function HeadlessTippy(
  props,
  ref,
) {
  return (
    <Tippy
      {...props}
      /* Missing on Tippy types.
       Allows for use cases like: https://github.com/atomiks/tippyjs-react#-multiple-tippies-on-a-single-element
    */
      {...({ ref } as any)}
    />
  )
})

export default HeadlessTippy
