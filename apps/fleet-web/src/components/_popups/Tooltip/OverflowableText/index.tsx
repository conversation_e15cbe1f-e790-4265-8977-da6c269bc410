import type { ComponentProps } from 'react'
import * as React from 'react'
import styled from 'styled-components'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import { useOverflowableElement } from '@karoo-ui/core'

export default function OverflowableTextTooltip({
  children,
  ...rest
}: Omit<ComponentProps<typeof ArrowedTooltip>, 'label' | 'children' | 'disabled'> & {
  children: string | null | undefined
}) {
  const [isOverflown, wrapperRef] = useOverflowableElement<HTMLDivElement>(
    { initialValue: false, orientation: 'horizontal' },
    [children],
  )

  return (
    <ArrowedTooltip
      label={children}
      disabled={!isOverflown}
      {...rest}
    >
      <EllipsifiedWrapper ref={wrapperRef}>{children}</EllipsifiedWrapper>
    </ArrowedTooltip>
  )
}

const EllipsifiedWrapper = styled.span`
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`

export function HeadlessOverflowableTooltip({
  children,
  label,
  ...rest
}: Omit<ComponentProps<typeof ArrowedTooltip>, 'children' | 'disabled'> & {
  children: ({ ref }: { ref: React.RefObject<HTMLElement> }) => React.ReactElement
}) {
  const [isOverflown, wrapperRef] = useOverflowableElement<HTMLDivElement>(
    { initialValue: false, orientation: 'horizontal' },
    [children],
  )

  return (
    <ArrowedTooltip
      label={label}
      disabled={!isOverflown}
      {...rest}
    >
      {children({ ref: wrapperRef })}
    </ArrowedTooltip>
  )
}
