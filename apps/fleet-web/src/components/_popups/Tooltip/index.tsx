import styled, { css } from 'styled-components'
import { variables } from 'src/shared/components/styled/global-styles'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { typography } from 'src/shared/components/styled/global-styles/typography'

export const TooltipCSS = css`
  background-color: ${variables.gray80};
  max-width: 455px;
  font-size: 13px;
  ${typography.fontFamily('robotoCondensed')};
  text-align: center;
  color: white;
  border-radius: 4px;
  white-space: normal;
  word-break: break-word;
  padding: ${spacing[1]} ${spacing[2]};
  border: none;
`

const Tooltip = styled.div`
  ${TooltipCSS}
`

export default Tooltip
