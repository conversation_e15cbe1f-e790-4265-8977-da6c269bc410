import { Paper, type PaperProps, Popper, type PopperProps } from '@karoo-ui/core'
import type { Except } from 'type-fest'

export type SideFiltersPanelProps = Except<
  PopperProps,
  'open' | 'children' | 'anchorEl'
> & {
  children: React.ReactNode
  anchorEl: HTMLElement | (() => HTMLElement) | null
  PaperProps: SideFiltersPanelPaperProps
}

export default function SideFiltersPanel({
  anchorEl,
  placement = 'right-start',
  container = anchorEl,
  children,
  PaperProps,
  sx,
  ...props
}: SideFiltersPanelProps) {
  return (
    <Popper
      open={anchorEl !== null}
      anchorEl={anchorEl}
      placement={placement}
      container={container}
      sx={{ height: '100%', ...sx }}
      {...props}
    >
      <SideFiltersPanelPaper {...PaperProps}>{children}</SideFiltersPanelPaper>
    </Popper>
  )
}

export type SideFiltersPanelPaperProps = PaperProps

export function SideFiltersPanelPaper({
  elevation = 3,
  children,
  style,
  ...props
}: SideFiltersPanelPaperProps) {
  return (
    <Paper
      elevation={elevation}
      style={{
        height: '100%',
        overflow: 'auto',
        ...style,
      }}
      {...props}
    >
      {children}
    </Paper>
  )
}
