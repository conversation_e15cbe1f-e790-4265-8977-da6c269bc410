import { useMemo, forwardRef, type ComponentProps } from 'react'
import { ctIntl } from 'src/util-components/ctIntl'
import ArrowedPopover from 'src/components/_popups/_popovers/Arrowed'
import Badge from 'src/components/Badge'
import styled from 'styled-components'
import { variables } from 'src/shared/components/styled/global-styles'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { typography } from 'src/shared/components/styled/global-styles/typography'
import TachographDriverActivityIcon from 'src/components/Icon/TachographDriverActivity'
import type { DriverWithVehicle } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import OverflowableTextTooltip from 'src/components/_popups/Tooltip/OverflowableText'
import type { Except } from 'type-fest'

type Props = {
  drivers: Array<
    Except<DriverWithVehicle, 'lastAssignedVehicleEvent'> & {
      lat: number
      lng: number
    }
  >
} & Except<ComponentProps<typeof ArrowedPopover>, 'label'>

const DriversClusterPopover = forwardRef<any, Props>(function DriversClusterPopover(
  { drivers, ...props },
  ref,
) {
  const filteredDrivers = useMemo(
    () =>
      drivers.sort(
        (a, b) =>
          (a.status && b.status ? a.status.localeCompare(b.status) : 2) ||
          a.name.localeCompare(b.name),
      ),
    [drivers],
  )

  return (
    <ArrowedPopover
      {...props}
      ref={ref}
      interactive
      label={
        <Container>
          <StyledBadge color="gray">
            <Badge.Label>
              {ctIntl.formatMessage({
                id: 'map.tachographDrivers.driversCluster.popover.header',
              })}
            </Badge.Label>
          </StyledBadge>
          <List>
            {filteredDrivers.map(({ id, name, status }) => (
              <Item key={id}>
                <TachographDriverActivityIcon status={status} />
                <ItemLabel>
                  <OverflowableTextTooltip>{name}</OverflowableTextTooltip>
                </ItemLabel>
              </Item>
            ))}
          </List>
        </Container>
      }
    />
  )
})

export default DriversClusterPopover

const StyledBadge = styled(Badge)`
  width: 100%;
`

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  > :first-child {
    margin-bottom: ${spacing[1]};
  }
`

const Item = styled.div`
  display: flex;
  align-items: center;
  padding: ${spacing[1]} ${spacing[2]};

  > :not(:last-child) {
    margin-right: ${spacing[1]};
  }
`

const ItemLabel = styled.span`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
  ${typography.fontFamily('robotoCondensed')};
  color: ${variables.gray60};
  margin-right: ${spacing[1]};
`

const List = styled.div`
  display: flex;
  flex-direction: column;
  overflow: auto;
  max-height: 200px;
  width: 100%;

  > :not(:last-child) {
    position: relative;
    :after {
      content: '';
      position: absolute;
      background: ${variables.gray40};
      height: 1px;
      width: 100%;
      bottom: 0;
      right: 0;
    }
  }
`
