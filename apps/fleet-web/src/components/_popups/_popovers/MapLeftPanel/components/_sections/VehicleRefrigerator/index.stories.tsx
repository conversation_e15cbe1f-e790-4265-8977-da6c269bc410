import styled from 'styled-components'
import VehicleRefrigerator from '.'

export default {
  component: VehicleRefrigerator,
}

export const Normal = () => (
  <Container>
    <VehicleRefrigerator
      refrigeratorTemperatures={[
        {
          id: '1',
          value: 2.3,
          threshold: {
            min: 1,
            max: 3,
          },
        },
        {
          id: '2',
          value: 2.3,
          threshold: {
            min: 1,
            max: 3,
          },
        },
        {
          id: '3',
          value: 2.3,
          threshold: {
            min: 1,
            max: 3,
          },
        },
        {
          id: '4',
          value: 10.3,
          threshold: {
            min: 1,
            max: 10,
          },
        },
      ]}
    />
  </Container>
)

const Container = styled.div`
  position: relative;
  width: max-content;
`
