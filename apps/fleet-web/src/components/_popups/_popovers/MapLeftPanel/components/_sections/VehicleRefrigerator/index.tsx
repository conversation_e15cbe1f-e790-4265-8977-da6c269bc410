import styled from 'styled-components'
import type { VehicleRefrigeratorTemperature } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import { ctIntl } from 'src/util-components/ctIntl'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import ExclamationCircle from 'src/components/Icon/ExclamationCircle'

type Props = {
  refrigeratorTemperatures: Array<VehicleRefrigeratorTemperature>
}

const VehicleRefrigerator = ({ refrigeratorTemperatures }: Props) => (
  <Container>
    <Title>{ctIntl.formatMessage({ id: 'Refrigerator' })}</Title>

    <TemperatureContainer>
      {refrigeratorTemperatures.map((temperature) => {
        const isTemperatureInViolation =
          temperature.value < temperature.threshold.min ||
          temperature.value > temperature.threshold.max

        return (
          <Temperature
            key={`${temperature.id}`}
            isTemperatureInViolation={isTemperatureInViolation}
          >
            <TemperatureLabel>
              {ctIntl.formatMessage(
                { id: 'map.leftPanel.listPopover.refrigerator.zone' },
                { values: { zoneNumber: temperature.id } },
              )}
            </TemperatureLabel>
            <TemperatureValue>
              {
                ctIntl.formatTemperature({
                  valueInCelsius: temperature.value,
                }).formattedTemperature
              }
            </TemperatureValue>
            {isTemperatureInViolation && <TemperatureWarning />}
          </Temperature>
        )
      })}
    </TemperatureContainer>
  </Container>
)

export default VehicleRefrigerator

const Container = styled.div`
  display: flex;
  flex-direction: column;
  padding-top: ${spacing[2]};

  &:not(:last-child) {
    padding-bottom: ${spacing[4]};
  }
`

const Title = styled.span`
  font-size: 14px;
  color: #333333;
  margin-bottom: ${spacing[2]};
`

const TemperatureContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, minmax(96px, auto));
  grid-gap: 5px;
`

const Temperature = styled.div<{ isTemperatureInViolation: boolean }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 18px;
  padding: ${({ isTemperatureInViolation }) =>
    isTemperatureInViolation ? '3px 2px 3px 7px' : '3px 7px'};
  color: #333;
  font-size: 12px;
  border: 1px solid #ccc;
  border-radius: 30px;
  background-color: #fff;
`

const TemperatureLabel = styled.span`
  font-weight: bold;
  margin-right: ${spacing[1]};
`

const TemperatureValue = styled.span``

const TemperatureWarning = styled(ExclamationCircle)`
  margin-left: ${spacing[1]};
`
