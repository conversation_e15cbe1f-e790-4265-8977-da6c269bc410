import styled from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { ctIntl } from 'src/util-components/ctIntl'
import { getSpeedUnit } from 'duxs/user'
import { useTypedSelector } from 'src/redux-hooks'

const PLACEHOLDER = '--'

type Props = {
  speed: number | null
  speedLimit: number | null
  rpm: number | null
  waterTemp: number | null
  oilTemp: number | null
}

const VehicleStats = ({ speed, speedLimit, rpm, waterTemp, oilTemp }: Props) => {
  const speedUnit = useTypedSelector(getSpeedUnit)

  return (
    <Container>
      <VehicleStat>
        <Value>{speed ?? PLACEHOLDER}</Value>
        <Unit>{speedUnit}</Unit>
      </VehicleStat>

      <Separator />

      <VehicleStat>
        <Value>{speedLimit ?? PLACEHOLDER}</Value>
        <Unit>{ctIntl.formatMessage({ id: 'Speed Limit' })}</Unit>
      </VehicleStat>

      <Separator />

      <VehicleStat>
        <Value>{rpm !== null ? rpm.toLocaleString() : PLACEHOLDER}</Value>
        <Unit>{ctIntl.formatMessage({ id: 'RPM' })}</Unit>
      </VehicleStat>

      <Separator />

      <VehicleStat>
        <Value>
          {waterTemp !== null
            ? ctIntl.formatTemperature({ valueInCelsius: waterTemp })
                .formattedTemperature
            : PLACEHOLDER}
        </Value>
        <Unit>{ctIntl.formatMessage({ id: 'Water Temp' })}</Unit>
      </VehicleStat>

      <Separator />

      <VehicleStat>
        <Value>
          {oilTemp !== null
            ? ctIntl.formatTemperature({ valueInCelsius: oilTemp }).formattedTemperature
            : PLACEHOLDER}
        </Value>
        <Unit>{ctIntl.formatMessage({ id: 'Oil Temp' })}</Unit>
      </VehicleStat>
    </Container>
  )
}

export default VehicleStats

const Container = styled.div`
  display: flex;
  justify-content: space-between;
  padding-top: ${spacing[2]};

  &:not(:last-child) {
    padding-bottom: ${spacing[2]};
  }
`

const Separator = styled.div`
  border-right: 1px solid #ccc;
  margin: 0 ${spacing[2]};
`

const VehicleStat = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`

const Value = styled.span`
  color: #333;
  font-size: 12px;
  font-weight: bold;
`

const Unit = styled.span`
  color: #666;
  font-size: 10px;
  text-transform: uppercase;
`
