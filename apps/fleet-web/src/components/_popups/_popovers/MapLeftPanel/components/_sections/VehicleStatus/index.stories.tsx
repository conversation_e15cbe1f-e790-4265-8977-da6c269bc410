import type { ComponentProps } from 'react'
import { DriverNameVisibilityStatus } from 'api/types'
import styled from 'styled-components'
import VehicleStatus from '.'
import { text, select } from '@storybook/addon-knobs'

export default {
  component: VehicleStatus,
}

export const Normal = () => {
  const visionCameraStatus = select(
    'Live Vision Camera Status',
    visionCameraStatusOptions,
    visionCameraStatusOptions['Active'],
  )
  const status = select(
    'Vehicle Status',
    vehicleStatusOptions,
    vehicleStatusOptions['Driving'],
  )
  const date = text('Date', '04/05/2020')
  const time = text('Time', '14:00')
  const driverName = text('Driver Name', 'Alpaca Swag')
  const streetAddress = text('Street Address', '20th Street, Santa Monica, CA')

  return (
    <Container>
      <VehicleStatus
        visionCameraStatus={visionCameraStatus}
        status={status}
        dateTime={{
          formattedDate: date,
          formattedTime: time,
        }}
        driverName={{
          name: driverName,
          status: DriverNameVisibilityStatus.public,
        }}
        streetAddress={streetAddress}
      />
    </Container>
  )
}

const Container = styled.div`
  position: relative;
  width: max-content;
`

const visionCameraStatusOptions: Record<
  string,
  ComponentProps<typeof VehicleStatus>['visionCameraStatus']
> = {
  Active: 'ACTIVE',
  Inactive: 'INACTIVE',
  'Not Installed': 'NOT_INSTALLED',
}

const vehicleStatusOptions: Record<
  string,
  ComponentProps<typeof VehicleStatus>['status']
> = {
  Driving: 'DRIVING',
  Idling: 'IDLING',
  'Ignition Off': 'IGNITION_OFF',
  'No Signal': 'NO_SIGNAL',
  Speeding: 'SPEEDING',
}
