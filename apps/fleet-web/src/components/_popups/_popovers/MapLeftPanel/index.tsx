import type { ComponentProps } from 'react'
import * as React from 'react'
import { isEmpty } from 'lodash'
import LazyHeadlessTippy from 'src/components/_popups/Tooltip/LazyHeadlessTippy'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { variables } from 'src/shared/components/styled/global-styles'
import styled from 'styled-components'
// import { Box, type BoxProps } from '@karoo-ui/core'

type HeadlessTippyProps = ComponentProps<typeof LazyHeadlessTippy>

type Props = {
  /**
   * Must have a ref or, if a component, a forwardRef to a DOM element
   */
  children: React.ReactElement
  label: React.ReactNode
  LabelContainerProps?: React.HTMLAttributes<HTMLDivElement>
} & Omit<HeadlessTippyProps, 'placement' | 'render' | 'ref' | 'interactive' | 'offset'>

const MapLeftPanelPopover = React.forwardRef<typeof LazyHeadlessTippy, Props>(
  function MapLeftPanelPopover(
    { children, label, popperOptions, LabelContainerProps, ...rest },
    ref,
  ) {
    return isEmpty(label) ? (
      children
    ) : (
      <>
        <LazyHeadlessTippy
          ref={ref}
          render={(attrs) => (
            <Box
              tabIndex={-1}
              {...LabelContainerProps}
              {...attrs}
            >
              {label}
            </Box>
          )}
          popperOptions={{
            ...popperOptions,
            modifiers: [
              {
                name: 'flip',
                options: {
                  fallbackPlacements: ['right-end'],
                },
              },
            ],
          }}
          offset={({ reference }) =>
            // Offset with and without scroll
            [0, reference.width < parseInt(variables.mapLeftPanelWidth) ? 7 : 0]
          }
          placement="right-start"
          interactive
          appendTo={document.body}
          {...rest}
        >
          {children}
        </LazyHeadlessTippy>
      </>
    )
  },
)

export default MapLeftPanelPopover

const Box = styled.div`
  position: relative;
  min-width: 312px;
  outline: none;
  padding: ${spacing[4]};
  background: white;
  border-radius: 0 5px 5px 0;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25);
`
