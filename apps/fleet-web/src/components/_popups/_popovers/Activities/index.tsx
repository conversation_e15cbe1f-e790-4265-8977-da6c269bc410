import { forwardRef, type ComponentProps } from 'react'
import { ctIntl } from 'src/util-components/ctIntl'
import ArrowedPopover from 'src/components/_popups/_popovers/Arrowed'
import Badge from 'src/components/Badge'
import styled from 'styled-components'
import { variables } from 'src/shared/components/styled/global-styles'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { typography } from 'src/shared/components/styled/global-styles/typography'
import TachographDriverActivityIcon from '../../../Icon/TachographDriverActivity'
import type { StatusTimePeriodStarterEvent } from 'src/modules/map-view/DriversMapView/Tachograph/MapArea/ui-types'

type Props = { activities: Array<StatusTimePeriodStarterEvent> } & Omit<
  ComponentProps<typeof ArrowedPopover>,
  'label'
>

const ActivitiesPopover = forwardRef<any, Props>(function ActivitiesPopover(
  { activities, ...props },
  ref,
) {
  return (
    <ArrowedPopover
      {...props}
      ref={ref}
      interactive
      label={
        <Container>
          <StyledBadge color="gray">
            <Badge.Label>
              {ctIntl.formatMessage({
                id: 'map.tachographDrivers.cluster.popover.header',
              })}
            </Badge.Label>
          </StyledBadge>
          <List>
            {activities.map(({ tachographStatus, representedDuration }) => (
              <Item
                key={`${representedDuration.start.formatted}${representedDuration.end.formatted}`}
              >
                <TachographDriverActivityIcon status={tachographStatus} />
                <ItemLabel>
                  <ItemLabelStatus>
                    {ctIntl.formatMessage({
                      id: `map.tachographDrivers.driverActivityStatus.${tachographStatus}`,
                    })}
                  </ItemLabelStatus>
                  <span>{`${representedDuration.start.formatted} - ${representedDuration.end.formatted}`}</span>
                </ItemLabel>
              </Item>
            ))}
          </List>
        </Container>
      }
    />
  )
})

export default ActivitiesPopover

const StyledBadge = styled(Badge)`
  width: 100%;
`

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;

  > :first-child {
    margin-bottom: ${spacing[1]};
  }
`

const Item = styled.div`
  display: flex;
  align-items: center;
  padding: ${spacing[1]} ${spacing[2]};

  > :not(:last-child) {
    margin-right: ${spacing[1]};
  }
`

const ItemLabel = styled.span`
  font-size: 12px;
  ${typography.fontFamily('robotoCondensed')};
  color: ${variables.gray60};
`

const ItemLabelStatus = styled.span`
  margin-right: ${spacing[1]};
`

const List = styled.div`
  display: flex;
  flex-direction: column;
  overflow: auto;
  max-height: 200px;

  > :not(:last-child) {
    position: relative;
    :after {
      content: '';
      position: absolute;
      background: ${variables.gray40};
      height: 1px;
      width: 100%;
      bottom: 0;
      right: 0;
    }
  }
`
