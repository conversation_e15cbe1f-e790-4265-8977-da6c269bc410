import { forwardRef, type ComponentProps, Children } from 'react'
import styled from 'styled-components'

import { ctIntl } from 'src/util-components/ctIntl'
import ArrowedPopover from 'src/components/_popups/_popovers/Arrowed'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { typography } from 'src/shared/components/styled/global-styles/typography'
import type { TripMarkerData } from 'src/modules/map-view/map/types'
import { UserFormattedPositionAddress } from 'src/modules/components/connected/UserFormattedPositionAddress'

type Props = { trip: TripMarkerData[0] } & Omit<
  ComponentProps<typeof ArrowedPopover>,
  'label'
>

const TripPopover = forwardRef<any, Props>(function TripPopover(
  { trip, ...props },
  ref,
) {
  return (
    <ArrowedPopover
      {...props}
      ref={ref}
      label={
        <Container>
          <TripInfoContainer>
            <TripInfo>
              <TripHeader>
                <TripLabel>
                  {ctIntl.formatMessage({
                    id: 'map.tripMarker.popover.start',
                  })}
                </TripLabel>
                <TripTime>{trip.formattedTime.start}</TripTime>
              </TripHeader>
              <TripLocation>
                <UserFormattedPositionAddress
                  address={trip.startLocation}
                  gpsFixType={trip.startGpsFixType}
                />
              </TripLocation>
            </TripInfo>

            <TripInfo>
              <TripHeader>
                <TripLabel>
                  {ctIntl.formatMessage({
                    id: 'map.tripMarker.popover.end',
                  })}
                </TripLabel>
                <TripTime>{trip.formattedTime.end}</TripTime>
              </TripHeader>
              <TripLocation>
                <UserFormattedPositionAddress
                  address={trip.endLocation}
                  gpsFixType={trip.endGpsFixType}
                />
              </TripLocation>
            </TripInfo>
          </TripInfoContainer>
          <Separator />

          <TripTotalContainer>
            <TripHeader>
              <TripLabel>
                {ctIntl.formatMessage({
                  id: 'map.tripMarker.popover.total',
                })}
              </TripLabel>
            </TripHeader>
            <TripTotalDataContainer>
              {trip.totals.map((total, index) => (
                <TripTotal key={index}>
                  <TripTotalValue>{total.value}</TripTotalValue>
                  <TripTotalLabel>{total.label}</TripTotalLabel>
                </TripTotal>
              ))}
            </TripTotalDataContainer>
          </TripTotalContainer>
        </Container>
      }
    />
  )
})

export default TripPopover

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 180px;
  font-size: 12px;
  ${typography.fontFamily('robotoCondensed')};
`

const TripInfoContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`

const TripInfo = styled.div`
  display: flex;
  flex-direction: column;

  :not(:last-child) {
    margin-bottom: ${spacing[2]};
  }
`

const TripHeader = styled.div`
  display: flex;
  justify-content: space-between;
  color: #333;
  margin-bottom: ${spacing[1]};
`

const TripLabel = styled.span``

const TripTime = styled.span`
  font-weight: bold;
`

const TripLocation = styled.span`
  ${typography.fontFamily('roboto')};
  color: #666;
  font-size: 10px;
`

const TripTotalContainer = styled.div`
  display: flex;
  flex-flow: column;
  width: 100%;
`

const TripTotalDataContainer = styled.div`
  display: grid;
  grid-template-columns: ${({ children }) => {
    const gridItems = Children.toArray(children).filter((child) => child).length
    return `repeat(${gridItems}, auto)`
  }};
`

const TripTotal = styled.div`
  display: flex;
  flex-flow: column;
  ${typography.fontFamily('roboto')};
`

const TripTotalValue = styled.span`
  color: #333;
  font-weight: bold;
`

const TripTotalLabel = styled.span`
  color: #666;
  text-transform: uppercase;
`
const Separator = styled.hr`
  height: 2px;
  width: 100%;
  border: 1px solid #ccc;
  margin: ${spacing[2]} ${spacing[0]};
`
