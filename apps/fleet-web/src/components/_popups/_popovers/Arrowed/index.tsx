import type { Except } from 'type-fest'
import * as React from 'react'
import { isEmpty } from 'lodash'
import styled from 'styled-components'
import LazyHeadlessTippy from 'src/components/_popups/Tooltip/LazyHeadlessTippy'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { generatePopupBoxAndArrow } from '../../utils'

type HeadlessTippyProps = React.ComponentProps<typeof LazyHeadlessTippy>

type Props = {
  /**
   * Must have a ref or, if a component, a forwardRef to a DOM element
   */
  children: React.ReactElement
  label: React.ReactNode
  backgroundColor?: string
} & Except<HeadlessTippyProps, 'render' | 'ref'>

const ArrowedPopover = React.forwardRef<typeof LazyHeadlessTippy, Props>(
  function ArrowedPopover(
    {
      children,
      label,
      placement = 'bottom-start',
      backgroundColor = 'white',
      popperOptions,
      ...rest
    },
    ref,
  ) {
    return isEmpty(label) ? (
      children
    ) : (
      <>
        <LazyHeadlessTippy
          ref={ref}
          render={(attrs) => (
            <StyledBox
              tabIndex={-1}
              {...attrs}
              backgroundColor={backgroundColor}
            >
              {label}
              <StyledArrow backgroundColor={backgroundColor} />
            </StyledBox>
          )}
          popperOptions={{
            ...popperOptions,
            modifiers: [
              {
                name: 'flip',
                options: {
                  fallbackPlacements: [
                    'bottom-start',
                    'top-start',
                    'right-start',
                    'left-start',
                  ],
                },
              },
              {
                name: 'arrow',
                options: {
                  padding: BORDER_RADIUS,
                },
              },
            ],
          }}
          {...rest}
          placement={placement}
        >
          {children}
        </LazyHeadlessTippy>
      </>
    )
  },
)

export default ArrowedPopover

const { Box, Arrow } = generatePopupBoxAndArrow({ arrowWidth: 12 })
const BORDER_RADIUS = 3

const StyledBox = styled(Box)<{ backgroundColor?: string }>`
  border-radius: ${BORDER_RADIUS}px;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.3);
  padding: ${spacing[2]};
  background: ${({ backgroundColor }) => backgroundColor};
`

const StyledArrow = styled(Arrow)<{ backgroundColor?: string }>`
  /** Allows for Box's box-shadow to take the arrow into account. Otherwise, the shadow would appear above the arrow.
      Note that ideally it would be -1 so the "hidden" part of the arrow would not overlap with content inside the tooltip. However the current spacing should prevent that from happening
  */
  z-index: 1;

  :before {
    background: ${({ backgroundColor }) => backgroundColor};
  }
`
