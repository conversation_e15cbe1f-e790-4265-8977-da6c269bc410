import * as React from 'react'
import styled from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import ErrorTimesIcon from './components/Icon/ErrorTimes'
import SuccessCheckIcon from './components/Icon/SuccessCheck'
import SnackbarCard from './components/Card'
import ClockIcon from './components/Icon/Clock'
import ExclamationCircleIcon from './components/Icon/ExclamationCircle'

type Props = {
  children: React.ReactNode
  variant: 'success' | 'error' | 'warning' | 'processing' // Others to be added
}

export default function Snackbar({ children, variant }: Props) {
  return (
    <StyledSnackbarCard>
      {(() => {
        switch (variant) {
          case 'error':
            return <ErrorTimesIcon />
          case 'success':
            return <SuccessCheckIcon />
          case 'processing':
            return <ClockIcon />
          case 'warning':
            return <ExclamationCircleIcon />
        }
      })()}
      <Details>{children}</Details>
    </StyledSnackbarCard>
  )
}

const StyledSnackbarCard = styled(SnackbarCard)`
  display: grid;
  grid-template-columns: auto 1fr;
  grid-column-gap: ${spacing[3]};
`

const Details = styled.div`
  display: flex;
  flex-direction: column;
`
