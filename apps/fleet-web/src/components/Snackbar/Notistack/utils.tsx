import { useCallback } from 'react'
import { useSnackbar, type SnackbarMessage, type OptionsObject } from 'notistack'
import { IconButton } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import type { RequireAtLeastOne } from 'type-fest'

export const useSnackbarWithCloseAction = () => {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar()

  const enqueueSnackbarWithCloseAction = useCallback(
    (message: SnackbarMessage, options: RequireAtLeastOne<OptionsObject, 'variant'>) =>
      enqueueSnackbar(message, {
        action: (snackbarId) => (
          <>
            <IconButton
              size="small"
              onClick={() => closeSnackbar(snackbarId)}
            >
              <CloseIcon
                fontSize="small"
                sx={{ color: 'white' }}
              />
            </IconButton>
          </>
        ),
        ...options,
      }),
    [closeSnackbar, enqueueSnackbar],
  )

  return {
    enqueueSnackbarWithCloseAction,
  }
}

export type UseSnackbarWithCloseActionReturnType = ReturnType<
  typeof useSnackbarWithCloseAction
>
