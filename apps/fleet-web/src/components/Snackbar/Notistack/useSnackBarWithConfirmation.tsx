import { useCallback, type MutableRefObject } from 'react'
import {
  useSnackbar,
  type SnackbarMessage,
  type OptionsObject,
  type SnackbarKey,
} from 'notistack'
import { Button } from '@karoo-ui/core'
import { ctIntl } from 'src/util-components/ctIntl'
import type { RequireAtLeastOne } from 'type-fest'

type CallbackProps = {
  cancelCallback?: () => void
  proceedCallback?: () => void
}

export const useSnackbarWithConfirmation = (
  // NOTE: we will save the opened snackbar keys
  openedSnackbarKeySet?: MutableRefObject<Set<SnackbarKey>>,
) => {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar()

  const enqueueSnackbarWithConfirmation = useCallback(
    (
      message: SnackbarMessage,
      options: RequireAtLeastOne<OptionsObject, 'variant'>,
      callbacks?: CallbackProps,
    ) => {
      closeSnackbar()
      const barId = enqueueSnackbar(message, {
        persist: true,
        action: (snackbarId) => (
          <>
            <Button
              variant="text"
              onClick={() => {
                typeof callbacks?.cancelCallback === 'function' &&
                  callbacks.cancelCallback()
                closeSnackbar(snackbarId)
              }}
              sx={{
                color: 'white',
              }}
            >
              {ctIntl.formatMessage({ id: 'Cancel' })}
            </Button>
            <Button
              sx={{
                color: 'white',
              }}
              variant="text"
              onClick={() => {
                typeof callbacks?.proceedCallback === 'function' &&
                  callbacks.proceedCallback()
                closeSnackbar(snackbarId)
              }}
            >
              {ctIntl.formatMessage({ id: 'Proceed' })}
            </Button>
          </>
        ),
        onClose: () => {
          if (openedSnackbarKeySet?.current && openedSnackbarKeySet.current.has(barId))
            openedSnackbarKeySet.current.delete(barId)
        },
        ...options,
      })
      openedSnackbarKeySet?.current?.add(barId)
    },
    [closeSnackbar, enqueueSnackbar, openedSnackbarKeySet],
  )

  return {
    enqueueSnackbarWithConfirmation,
  }
}

export type UseSnackbarWithConfirmationReturnType = ReturnType<
  typeof useSnackbarWithConfirmation
>
