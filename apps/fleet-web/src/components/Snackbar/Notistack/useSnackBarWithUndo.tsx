import { useCallback } from 'react'
import { useSnackbar, type SnackbarMessage, type OptionsObject } from 'notistack'
import { Button } from '@karoo-ui/core'
import { ctIntl } from 'src/util-components/ctIntl'
import type { RequireAtLeastOne } from 'type-fest'

type CallbackProps = {
  undoCallback?: () => void
}

export const useSnackbarWithUndo = () => {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar()

  const enqueueSnackbarWithUndo = useCallback(
    (
      message: SnackbarMessage,
      options: RequireAtLeastOne<OptionsObject, 'variant'>,
      callbacks?: CallbackProps,
    ) => {
      // allow only one undo snackbar currently
      closeSnackbar()
      enqueueSnackbar(message, {
        action: (snackbarId) => (
          <>
            <Button
              sx={{
                color: 'white',
              }}
              variant="text"
              onClick={() => {
                typeof callbacks?.undoCallback === 'function' &&
                  callbacks.undoCallback()
                closeSnackbar(snackbarId)
              }}
            >
              {ctIntl.formatMessage({ id: 'Undo' })}
            </Button>
          </>
        ),
        ...options,
      })
    },
    [closeSnackbar, enqueueSnackbar],
  )

  return {
    enqueueSnackbarWithUndo,
  }
}

export type UseSnackbarWithConfirmationReturnType = ReturnType<
  typeof useSnackbarWithUndo
>
