import styled from 'styled-components'
import { variables } from 'src/shared/components/styled/global-styles'

const Text = styled.span`
  line-height: 20px;
  font-size: 14px;
`

const Date = styled(Text)`
  color: ${variables.gray};
`

const Message = styled(Text)`
  color: white;
`

const MessageBold = styled(Text)`
  font-weight: bold;
`

const SnackbarText = Object.assign(Text, { Date, Message, MessageBold })

export default SnackbarText
