import styled from 'styled-components'
import { variables } from 'src/shared/components/styled/global-styles'

const SnackbarActionButton = styled.button`
  height: 20px;
  color: ${variables.orange};
  ${(props) => props.theme.typography.mixins.robotoMedium};
  font-size: 14px;
  letter-spacing: 0.5px;
  line-height: 20px;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  text-transform: uppercase;
  padding: 0;
`

export default SnackbarActionButton
