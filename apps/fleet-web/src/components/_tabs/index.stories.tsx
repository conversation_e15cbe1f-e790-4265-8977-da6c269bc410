import { useState } from 'react'
import Tabs from './index'

export default {
  component: Tabs,
}

//TODO: need to revisit components that is modules specific and move them out of storybook
//import TooltipTimepicker from './TooltipTimepicker'

const initialTabs = [
  { label: 'Work Hours', id: 'work', hasToolTip: false },
  { label: '24 Hour', id: 'day', hasToolTip: false },
]

//TODO: need to revisit components that is modules specific and move them out of storybook
// const TabsWithToolTip = () => {
//   const [tabs] = useState(initialTabs)
//   const [currentTab, setCurrentTab] = useState('work')
//
//   const handleClicked = (target: string) => {
//     setCurrentTab(target)
//   }
//
//   return (
//     <Tabs
//       onClick={handleClicked}
//       activeTab={currentTab}
//       tabs={tabs}
//       activeColor={'#F47735'}
//       tooltipProps={{
//         label: <TooltipTimepicker />,
//         placement: 'bottom',
//       }}
//     />
//   )
// }

export const Normal = () => {
  const [tabs] = useState(initialTabs)
  const [currentTab, setCurrentTab] = useState('work')

  const handleClicked = (target: string) => {
    setCurrentTab(target)
  }

  return (
    <Tabs
      onClick={handleClicked}
      activeTab={currentTab}
      tabs={tabs}
      activeColor={'#F47735'}
    />
  )
}
//TODO: need to revisit components that is modules specific and move them out of storybook
//export const tabsWithToolTip = () => <TabsWithToolTip />
