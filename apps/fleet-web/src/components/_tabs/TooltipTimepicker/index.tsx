import { memo, useState } from 'react'
import moment from 'moment-timezone'
import styled from 'styled-components'

//component
import HourRange from '../../_datepickers/HourRange'

//redux
import { connect } from 'react-redux'
import {
  getTaskTimelineWorkingHours,
  updateTasksTimelineWorkingHours,
  setFromHour,
  setToHour,
} from 'duxs/tasks/tasks'
import type { AppState } from 'src/root-reducer'

//utils
import { ctIntl } from 'src/util-components/ctIntl'
import { useDebouncedValue } from 'src/hooks'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'

type Props = ReturnType<typeof mapStateToProps> & typeof actionCreators

enum Timepicker {
  from = 'from',
  to = 'to',
}

function TooltipTimepicker({
  from,
  to,
  setFromHour,
  setToHour,
  updateTasksTimelineWorkingHours,
}: Props) {
  const [timelineStartTime, setTimelineStartTime] = useState(from)
  const [timelineClosingTime, setTimelineClosingTime] = useState(to)

  const debounceTimelineStartTime = useDebouncedValue(timelineStartTime, 500)
  const debounceTimelineClosingTime = useDebouncedValue(timelineClosingTime, 500)

  useEffectExceptOnMount(() => {
    updateTasksTimelineWorkingHours({
      openingTime: moment(debounceTimelineStartTime.toString(), 'LT').format(
        'HH:mm:ss',
      ),
      closingTime: moment(debounceTimelineClosingTime.toString(), 'LT').format(
        'HH:mm:ss',
      ),
    })
  }, [
    debounceTimelineStartTime,
    debounceTimelineClosingTime,
    updateTasksTimelineWorkingHours,
  ])

  const renderHourToString = (hour: number) =>
    moment({ hours: hour, minutes: 0, seconds: 0 }).format('HH:mm')

  const handleOnClickIncrement = (event: React.MouseEvent, target: string) => {
    event.stopPropagation()

    if (target === Timepicker.from) {
      const value = from > 23 ? 0 : from + 1
      // TODO @dev temporarily max to 5 hours comparison due to broken component
      if (value + 4 < to) {
        setFromHour(value)
        setTimelineStartTime(value)
      }
    } else {
      const value = to > 23 ? 0 : to + 1
      if (value > from) {
        setToHour(value)
        setTimelineClosingTime(value)
      }
    }
  }

  const handleOnClickDecrement = (event: React.MouseEvent, target: string) => {
    event.stopPropagation()
    if (target === Timepicker.from) {
      const value = from < 1 ? 23 : from - 1
      if (value < to) {
        setFromHour(value)
        setTimelineStartTime(value)
      }
    } else {
      const value = to < 1 ? 23 : to - 1
      // TODO @dev temporarily max to 5 hours comparison due to broken component
      if (value - 4 > from) {
        setToHour(value)
        setTimelineClosingTime(value)
      }
    }
  }

  return (
    <HourRangeContainer>
      <WordStyle>{ctIntl.formatMessage({ id: 'From' })}</WordStyle>
      <HourRange
        id={Timepicker.from}
        onClickIncrement={(event: React.MouseEvent) =>
          handleOnClickIncrement(event, Timepicker.from)
        }
        onClickDecrement={(event: React.MouseEvent) =>
          handleOnClickDecrement(event, Timepicker.from)
        }
        time={renderHourToString(from)}
      />

      <WordStyle>{ctIntl.formatMessage({ id: 'To' })}</WordStyle>
      <HourRange
        id={Timepicker.to}
        onClickIncrement={(event: React.MouseEvent) =>
          handleOnClickIncrement(event, Timepicker.to)
        }
        onClickDecrement={(event: React.MouseEvent) =>
          handleOnClickDecrement(event, Timepicker.to)
        }
        time={renderHourToString(to)}
      />
    </HourRangeContainer>
  )
}

const mapStateToProps = (state: AppState) => {
  const { tasksTimelineStartTime, tasksTimelineClosingTime } =
    getTaskTimelineWorkingHours(state)
  return {
    from: tasksTimelineStartTime,
    to: tasksTimelineClosingTime,
  }
}

const actionCreators = {
  setFromHour,
  setToHour,
  updateTasksTimelineWorkingHours,
}

export default memo(connect(mapStateToProps, actionCreators)(TooltipTimepicker))

const HourRangeContainer = styled.div`
  padding: 5px;
`

const WordStyle = styled.div`
  text-align: left;
  padding: 4px;
`
