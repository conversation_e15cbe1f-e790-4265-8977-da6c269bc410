import styled from 'styled-components'
import type { ComponentProps } from 'react'
import type ContainerTooltip from 'src/components/_popups/Tooltip/ContainerToolTip'

type PillProps = {
  active: boolean
  activeColor: string
  disabled: boolean
}

type Props = {
  tooltipProps?: Omit<ComponentProps<typeof ContainerTooltip>, 'children'>
}

export const StyledTabs = styled.div<Omit<Props, 'tooltipProps'>>`
  display: flex;
  align-items: center;
  position: relative;
`

export const StyledTab = styled.div<PillProps>(
  ({ active, activeColor, disabled }) => `

  border: 1px solid #ccc;
  background-color: ${active ? activeColor : 'white'};
  color: ${active ? 'white' : '#666'};
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
  font-family: 'Roboto', sans-serif;
  width: 120px;
  white-space: nowrap;
  height: 25px;
  padding: 0 32px;
  line-height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: ${disabled ? 'normal' : 'pointer'};
  opacity: ${disabled ? '0.75' : '1'};

  &:first-of-type {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-right: none;
  }

  &:last-of-type {
    border-left: none;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
  }
`,
)
