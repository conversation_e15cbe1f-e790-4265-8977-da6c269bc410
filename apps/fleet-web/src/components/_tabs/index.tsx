import type { ComponentProps, CSSProperties, MouseEvent } from 'react'

import { StyledTabs, StyledTab } from './styles'
import ContainerTooltip from '../_popups/Tooltip/ContainerToolTip'
import type { FixMeAny } from 'src/types'
import AlertsBadge from 'src/util-components/alerts-badge'
import { ctIntl } from 'src/util-components/ctIntl'

type Tab = {
  hasToolTip: boolean
  id: string
  label: string
  hasBadge?: boolean
}

type Props = {
  activeColor: string
  activeTab: string
  disabled?: boolean
  onClick: (target: string) => void
  style?: CSSProperties
  styledTabExtras?: CSSProperties
  tabs: Array<Tab>
  tooltipProps?: Omit<ComponentProps<typeof ContainerTooltip>, 'children'>
  badgeProps?: FixMeAny
}

function Tabs({
  activeColor,
  activeTab,
  disabled = false,
  onClick,
  style,
  styledTabExtras,
  tabs,
  tooltipProps,
  badgeProps,
}: Props) {
  const handleTabClick = (event: MouseEvent<HTMLDivElement>) => {
    if (disabled) return
    const target = event.target as HTMLDivElement
    if (target.id) onClick(target.id)
  }

  const renderContainer = (tab: Tab) => (
    <StyledTab
      key={tab.id}
      onClick={handleTabClick}
      id={tab.id}
      active={activeTab === tab.id}
      activeColor={activeColor}
      style={styledTabExtras}
      disabled={disabled}
    >
      <>
        {ctIntl.formatMessage({ id: `${tab.label}` })}
        {tab.hasBadge && activeTab !== tab.id && (
          <AlertsBadge
            count={badgeProps.badgeCount}
            style={{ top: 10, right: 12 }}
          />
        )}
      </>
    </StyledTab>
  )

  return (
    <StyledTabs style={style}>
      {tabs.map((tab) => {
        if (tab.hasToolTip) {
          return (
            <ContainerTooltip
              label={tooltipProps && tooltipProps.label}
              placement={tooltipProps && tooltipProps.placement}
              key={tab.id}
              active={activeTab === tab.id}
            >
              {renderContainer(tab)}
            </ContainerTooltip>
          )
        }
        return renderContainer(tab)
      })}
    </StyledTabs>
  )
}

export default Tabs
