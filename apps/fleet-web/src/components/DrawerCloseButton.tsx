import CloseIcon from '@mui/icons-material/Close'
import { Button, type ButtonProps } from '@karoo-ui/core'
import type { Except, SetRequired } from 'type-fest'

export type DrawerCloseButtonProps = SetRequired<
  Except<ButtonProps, 'variant' | 'color'>,
  'onClick'
> & { testId?: string }

export function DrawerCloseButton({
  sx,
  testId = 'drawer-close-button',
  ...props
}: DrawerCloseButtonProps) {
  return (
    <Button
      sx={[
        { width: 36, height: 36, minWidth: 36, minHeight: 36 },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      variant="outlined"
      color="secondary"
      data-cy={testId}
      {...props}
    >
      <CloseIcon sx={{ fontSize: 24, width: 24, height: 24 }} />
    </Button>
  )
}
