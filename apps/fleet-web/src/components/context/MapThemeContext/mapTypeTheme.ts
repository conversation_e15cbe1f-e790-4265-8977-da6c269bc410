import drivingImg from 'src/components/_map/_markers/Event/Icon/_icons/driving.png'
import excessiveIdlingImg from 'src/components/_map/_markers/Event/Icon/_icons/excessive-idling.png'
import harshAccelImg from 'src/components/_map/_markers/Event/Icon/_icons/harsh-accel.png'
import harshBrakingImg from 'src/components/_map/_markers/Event/Icon/_icons/harsh-braking.png'
import harshTurningImg from 'src/components/_map/_markers/Event/Icon/_icons/harsh-turning.png'
import idlingImg from 'src/components/_map/_markers/Event/Icon/_icons/idling.png'
import ignitionImg from 'src/components/_map/_markers/Event/Icon/_icons/ignition.png'
import maxSpeedImg from 'src/components/_map/_markers/Event/Icon/_icons/max-speed.png'
import speedingImg from 'src/components/_map/_markers/Event/Icon/_icons/speeding.png'
import type { MapThemeConfig } from './MapThemeContext'
import tripEndImg from './trip-end.png'

export const mapTypeTheme: MapThemeConfig = {
  components: {
    TripStartMarker: {
      backgroundColor: '#ffffff',
      borderColor: '#4a4a4a',
    },
    TripEndMarker: {
      tripEndImgSrc: tripEndImg,
    },
    PathLine: {
      strokeColor: '#4a4a4a',
    },
    EventMarker: {
      icons: {
        default: drivingImg,
        driving: drivingImg,
        'excessive-idling': excessiveIdlingImg,
        'harsh-accel': harshAccelImg,
        'harsh-braking': harshBrakingImg,
        'harsh-turning': harshTurningImg,
        idling: idlingImg,
        ignition: ignitionImg,
        'max-speed': maxSpeedImg,
        speeding: speedingImg,
        'excessive-rpm': harshTurningImg,
      },
    },
  },
}
