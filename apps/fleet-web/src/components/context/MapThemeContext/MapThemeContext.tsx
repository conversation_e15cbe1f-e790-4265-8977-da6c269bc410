import * as React from 'react'
import type { FixMeAny } from 'src/types'
import { mapTypeTheme } from './mapTypeTheme'
import { satelliteAndHybridTheme } from './satteliteAndHybridTheme'
import { MapTypeId } from 'src/types/extended/google-maps'

export interface MapThemeConfig {
  components: {
    TripStartMarker: {
      backgroundColor: string
      borderColor: string
    }
    TripEndMarker: {
      tripEndImgSrc: FixMeAny
    }
    PathLine: {
      strokeColor: string
    }
    EventMarker: {
      icons: {
        ignition: string
        driving: string
        'excessive-idling': string
        'harsh-accel': string
        'harsh-braking': string
        'harsh-turning': string
        idling: string
        'max-speed': string
        speeding: string
        'excessive-rpm': string
        default: string
      }
    }
  }
}

const MapThemeContext = React.createContext<MapThemeConfig | null>(null)

const useMapTheme = () => {
  const context = React.useContext(MapThemeContext)
  if (context === null) {
    throw new Error(`useMapTheme must be used within a MapThemeContextProvider`)
  }
  return context
}

type Props = {
  mapTypeId: google.maps.MapTypeId
  children: React.ReactNode
}

const MapThemeContextProvider = ({ mapTypeId, children }: Props) => (
  <MapThemeContext.Provider
    value={
      mapTypeId === MapTypeId.HYBRID || mapTypeId === MapTypeId.SATELLITE
        ? satelliteAndHybridTheme
        : mapTypeTheme
    }
  >
    {children}
  </MapThemeContext.Provider>
)
export { MapThemeContextProvider, useMapTheme }
