import * as React from 'react'
import { useTypedSelector } from 'src/redux-hooks'
import { getMapTypeId } from 'src/duxs/map'
import { MapThemeContextProvider } from 'src/components/context/MapThemeContext'

type Props = {
  children: React.ReactNode
}

/**
 * You shall prefer MapThemeContextProvider instead.
 *
 * This theme context is dependent on the store where it gets the mapTypeId from.
 */
const ConnectedMapThemeContextProvider = ({ children }: Props) => {
  const mapTypeId = useTypedSelector(getMapTypeId)
  return (
    <MapThemeContextProvider mapTypeId={mapTypeId}>{children}</MapThemeContextProvider>
  )
}

export { ConnectedMapThemeContextProvider }
