import drivingImg from 'src/components/_map/_markers/Event/Icon/_satteliteAndHybridIcons/driving.png'
import excessiveIdlingImg from 'src/components/_map/_markers/Event/Icon/_satteliteAndHybridIcons/excessive-idling.png'
import harshAccelImg from 'src/components/_map/_markers/Event/Icon/_satteliteAndHybridIcons/harsh-accel.png'
import harshBrakingImg from 'src/components/_map/_markers/Event/Icon/_satteliteAndHybridIcons/harsh-braking.png'
import harshTurningImg from 'src/components/_map/_markers/Event/Icon/_satteliteAndHybridIcons/harsh-turning.png'
import idlingImg from 'src/components/_map/_markers/Event/Icon/_satteliteAndHybridIcons/idling.png'
import ignitionImg from 'src/components/_map/_markers/Event/Icon/_satteliteAndHybridIcons/ignition.png'
import maxSpeedImg from 'src/components/_map/_markers/Event/Icon/_satteliteAndHybridIcons/max-speed.png'
import speedingImg from 'src/components/_map/_markers/Event/Icon/_satteliteAndHybridIcons/speeding.png'
import type { MapThemeConfig } from './MapThemeContext'
import tripEndImg from './sattelite-and-hybrid-trip-end.png'

export const satelliteAndHybridTheme: MapThemeConfig = {
  components: {
    TripStartMarker: {
      backgroundColor: 'white',
      borderColor: 'white',
    },
    TripEndMarker: {
      tripEndImgSrc: tripEndImg,
    },
    PathLine: {
      strokeColor: '#eaeaea',
    },
    EventMarker: {
      icons: {
        default: drivingImg,
        driving: drivingImg,
        'excessive-idling': excessiveIdlingImg,
        'harsh-accel': harshAccelImg,
        'harsh-braking': harshBrakingImg,
        'harsh-turning': harshTurningImg,
        idling: idlingImg,
        ignition: ignitionImg,
        'max-speed': maxSpeedImg,
        speeding: speedingImg,
        'excessive-rpm': harshTurningImg,
      },
    },
  },
}
