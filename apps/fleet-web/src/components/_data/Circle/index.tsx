import * as React from 'react'
import styled from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import Circle from '../../Circle'

const Container = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: ${spacing[2]};
`

const Title = styled.span`
  margin-top: ${spacing[2]};
  ${(props) => props.theme.typography.fontFamily('robotoCondensed')};
  font-size: 10px;
  color: #999;
  text-transform: uppercase;
`

export const BoldTitle = styled(Title)`
  color: #333;
`

const CircleContainer = styled.div`
  position: relative;
  display: flex;
`

const CircleInfo = styled.div`
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  height: 100%;
  width: 100%;
`

const CircleUnitLabel = styled.span`
  ${(props) => props.theme.typography.fontFamily('roboto')};
  font-size: 18px;
  color: #333;
  line-height: 14px;
`

const CircleTotalLabel = styled.span`
  margin-top: 3px;
  ${(props) => props.theme.typography.fontFamily('robotoCondensed')};
  font-size: 10px;
  color: #999;
  line-height: 10px;
`

type Props = {
  unit: { value: number; label: string }
  total: { value: number; label: string }
  title?: string | React.ReactNode
}

const CircleData = ({ unit, total, title }: Props) => (
  <Container>
    <CircleContainer>
      <Circle percentage={(unit.value * 100) / total.value} />
      <CircleInfo>
        <CircleUnitLabel>{unit.label}</CircleUnitLabel>
        <CircleTotalLabel>{total.label}</CircleTotalLabel>
      </CircleInfo>
    </CircleContainer>
    {title && <Title>{title}</Title>}
  </Container>
)

export default CircleData
