import * as React from 'react'
import HalfCircleData from '.'
import { select, number, text } from '@storybook/addon-knobs'

export default {
  component: HalfCircleData,
}

const directions: Record<
  string,
  React.ComponentProps<typeof HalfCircleData>['direction']
> = {
  Clockwise: 'clockwise',
  Anticlockwise: 'anticlockwise',
}

export const Normal = () => {
  const minValue = number('Min', 0)
  const maxValue = number('Max', 10)
  const unitValue = number('Unit Value', 5, {
    range: true,
    min: minValue,
    max: maxValue,
    step: 0.1,
  })
  const unitLabel = text('Unit Label', '5:00')
  const label = text('Label', 'Remaining driving time')
  const direction = select('Direction', directions, directions['Clockwise'])

  return (
    <HalfCircleData
      min={minValue}
      max={maxValue}
      unit={{
        value: unitValue,
        label: unitLabel,
      }}
      label={label}
      direction={direction}
    />
  )
}
