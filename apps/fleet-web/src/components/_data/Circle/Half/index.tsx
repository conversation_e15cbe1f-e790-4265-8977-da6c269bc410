import styled from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import HalfCircle from '../../../Circle/Half'

type Direction = 'clockwise' | 'anticlockwise'

type Props = {
  min: number
  max: number
  unit: { value: number; label: string }
  label: string
  direction?: Direction
}

const HalfCircleData = ({ min, max, unit, label, direction = 'clockwise' }: Props) => {
  const percentage = (unit.value * 100) / max

  return (
    <Container direction={direction}>
      <ConstraintValue>{min}</ConstraintValue>

      <HalfCircleWithLabelContainer>
        <HalfCircleContainer>
          <HalfCircle
            percentage={percentage}
            direction={direction}
          />
          <ValueLabel>{unit.label}</ValueLabel>
        </HalfCircleContainer>

        {label && <Label>{label}</Label>}
      </HalfCircleWithLabelContainer>

      <ConstraintValue>{max}</ConstraintValue>
    </Container>
  )
}

export default HalfCircleData

const Container = styled.div<{ direction: Direction }>`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: baseline;
  flex-direction: ${({ direction }) =>
    direction === 'clockwise' ? 'row' : 'row-reverse'};
  text-align: center;
`

const HalfCircleWithLabelContainer = styled.div`
  position: relative;
  width: min-content;
  margin: 0 2px 0 3px;
`

const HalfCircleContainer = styled.div`
  position: relative;
`

const ConstraintValue = styled.span`
  position: relative;
  top: -${spacing[3]};
  color: #999;
  font-size: 10px;
`

const ValueLabel = styled.span`
  position: absolute;
  left: 50%;
  top: 70%;
  transform: translate(-50%, -50%);
  color: #333;
  font-size: 34px;
  text-align: center;
`

const Label = styled.span`
  margin-top: ${spacing[1]};
  font-size: 10px;
  text-transform: uppercase;
  color: #999;
  ${(props) => props.theme.typography.fontFamily('robotoCondensed')};
`
