import CircleData from '.'
import { number, text } from '@storybook/addon-knobs'

export default {
  component: CircleData,
}

export const Normal = () => {
  const totalValue = number('Total Value', 162000)
  const totalLabel = text('Total Label', 'of 45:00')
  const unitValue = number('Unit Value', 90000, {
    range: true,
    min: 0,
    max: totalValue,
    step: 1,
  })
  const unitLabel = text('Unit Label', '25:00')
  const title = text('Title', 'Remaining driving time')

  return (
    <CircleData
      unit={{
        value: unitValue,
        label: unitLabel,
      }}
      total={{
        value: totalValue,
        label: totalLabel,
      }}
      title={title}
    />
  )
}
