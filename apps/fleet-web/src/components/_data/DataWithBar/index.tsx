import styled from 'styled-components'
import { variables } from 'src/shared/components/styled/global-styles'
import TimelineBar from 'src/components/_timeline/Bar'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import usePercentageValidation from 'src/hooks/usePercentageValidation'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'

type Props = {
  unit: {
    value: number
    DisplayedValue: string | JSX.Element
  }
  total: number
  label: string
  barColor: string
  className?: string
}

const DataWithBar = ({ unit, total, label, barColor, className }: Props) => {
  const validatedPercentage = usePercentageValidation((unit.value * 100) / total)

  return (
    <Container className={className}>
      <UnitContainer>
        {typeof unit.DisplayedValue !== 'string' ? (
          <>{unit.DisplayedValue}</>
        ) : (
          <UnitValue>{unit.DisplayedValue}</UnitValue>
        )}
      </UnitContainer>
      <BarContainer>
        <BarBackground />
        {unit.value > 0 && (
          <BarFragment
            color={barColor}
            pctLeft={0}
            pctWidth={validatedPercentage}
          />
        )}
      </BarContainer>
      <UnitLabel>{label}</UnitLabel>
    </Container>
  )
}

const UnitValue = styled.span`
  font-size: 18px;
  color: #333;
  margin-right: 5px;
  line-height: 15px; /* This is important to align with text wrapped */
`

type UnitValueWithTooltipProps = React.ComponentProps<typeof UnitValue> & {
  children: React.ReactNode
  tooltipProps: Omit<React.ComponentProps<typeof ArrowedTooltip>, 'children'>
}

const UnitValueWithTooltip = ({
  tooltipProps,
  children,
}: UnitValueWithTooltipProps) => (
  <ArrowedTooltip {...tooltipProps}>
    <UnitValue>{children}</UnitValue>
  </ArrowedTooltip>
)

const UnitLabel = styled.span`
  font-size: 10px;
  color: #999;
  text-transform: uppercase;
  ${(props) => props.theme.typography.fontFamily('robotoCondensed')};
`

export default Object.assign(DataWithBar, {
  UnitValue,
  UnitValueWithTooltip,
  UnitLabel,
})

const Container = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: min-content;
  gap: ${spacing[1]};
`

const UnitContainer = styled.div`
  display: flex;
  align-items: flex-end;
`

const BarContainer = styled.div`
  position: relative;
  height: 5px;
  width: 90px;
`

const BarBackground = styled.div`
  height: 100%;
  width: 100%;
  background-color: ${variables.gray20};
  border-radius: 2.5px;
`

const BarFragment = styled(TimelineBar.Fragment)`
  border-radius: 2.5px;
  border: none !important;
`
