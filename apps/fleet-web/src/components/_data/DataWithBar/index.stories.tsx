import DataWithBar from '.'
import { number, text, color } from '@storybook/addon-knobs'

export default {
  component: DataWithBar,
}

export const Normal = () => {
  const total = 36000
  const unitValue = number('Unit Value', 14400, {
    range: true,
    min: 0,
    max: total,
    step: 1,
  })
  const displayedValue = text('Displayed Value', '4:00')
  const label = text('Label', 'Driving time')
  const barColor = color('Bar Color', '#6BA238')

  return (
    <DataWithBar
      unit={{
        value: unitValue,
        DisplayedValue: displayedValue,
      }}
      total={total}
      label={label}
      barColor={barColor}
    />
  )
}
