import { forwardRef } from 'react'
import styled, { css } from 'styled-components'
import theme from 'src/components/_themes/tachograph'
import type { InfractionVariant } from './types'
import { typography } from 'src/shared/components/styled/global-styles/typography'

type Props = {
  value: number
  variant: InfractionVariant
  size?: 'normal' | 'large'
  onClick?: () => void
  className?: string
}

const Infraction = forwardRef<HTMLDivElement, Props>(function Infraction(
  { variant, value, size = 'normal', onClick, className },
  ref,
) {
  return (
    <Container
      ref={ref}
      variant={variant}
      size={size}
      onClick={onClick}
      className={className}
    >
      {value}
    </Container>
  )
})

export default Infraction

const Container = styled.div<Pick<Props, 'variant' | 'size'>>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: min-content;
  border-radius: ${({ size }) => (size === 'normal' ? '4px' : '8px')};
  padding: ${({ size }) => (size === 'normal' ? '4px' : '7px')};
  font-size: 12px;
  ${({ variant }) => {
    const baseCSS = css`
      background-color: ${theme.colors.infractions[variant]};
    `

    if (variant === 'blank') {
      return css`
        ${baseCSS}
        color: #666666;
        border: 1px solid #cccccc;
      `
    }

    return css`
      ${baseCSS}
      color: #FFFFFF;
    `
  }}
  ${({ size }) => {
    if (size === 'normal') {
      return css`
        height: 18px;
        min-width: 18px;
      `
    }

    return css`
      height: 28px;
      min-width: 28px;
    `
  }}
  ${typography.mixins.robotoBold};
`
