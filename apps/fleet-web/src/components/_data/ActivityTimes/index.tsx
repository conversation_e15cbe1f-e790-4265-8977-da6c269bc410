import styled from 'styled-components'
import theme from 'src/components/_themes/tachograph'
import { ctIntl } from 'src/util-components/ctIntl'
import DataWithBar from '../DataWithBar'
import HalfCircleData from '../Circle/Half'
import { getRemainingDrivingTimeDurationWithFormattedHourMinute } from 'src/modules/map-view/DriversMapView/Tachograph/utils'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import type { FetchDriverDayActivity } from 'src/modules/map-view/DriversMapView/Tachograph/api/useDriverDayActivityQuery'

type Props = {
  driverDayActivityData: FetchDriverDayActivity.Return
  className?: string
}

const ActivityTimesData = ({
  driverDayActivityData: { activity, isETachoDriver },
  className,
}: Props) => {
  const drivingTime = ctIntl.getDurationWithFormattedHourMinute(
    activity.timeStats.drivingTime,
  )
  const otherWorkTime = ctIntl.getDurationWithFormattedHourMinute(
    activity.timeStats.otherWorkTime,
  )
  const availableTime = ctIntl.getDurationWithFormattedHourMinute(
    activity.timeStats.availableTime,
  )
  const restTime = ctIntl.getDurationWithFormattedHourMinute(
    activity.timeStats.restTime,
  )
  const workForOtherEntitiesTime = ctIntl.getDurationWithFormattedHourMinute(
    activity.timeStats.workForOtherEntitiesTime,
  )
  const unknownTime = ctIntl.getDurationWithFormattedHourMinute(
    activity.timeStats.unknownTime,
  )
  const amplitudeTime = ctIntl.getDurationWithFormattedHourMinute(
    activity.timeStats.drivingTime + activity.timeStats.otherWorkTime,
  )
  const remainingDriveTime = getRemainingDrivingTimeDurationWithFormattedHourMinute(
    activity.timeStats.remainingDriveTime,
  )
  const limitTime = ctIntl.getDurationWithFormattedHourMinute(
    activity.timeStats.limitTime,
  )

  return (
    <ActivityTimesContainer className={className}>
      <HalfCircleData
        min={0}
        max={Math.floor(limitTime.hours)}
        unit={{
          value: remainingDriveTime.hours,
          label: remainingDriveTime.formatted,
        }}
        label={ctIntl.formatMessage({
          id: 'map.tachographDrivers.bottomPanel.driveTimeRemaining',
        })}
        direction="anticlockwise"
      />

      <DataWithBarContainer>
        <DataWithBar
          unit={{
            value: activity.timeStats.drivingTime,
            DisplayedValue: (
              <DataWithBar.UnitValueWithTooltip
                tooltipProps={{
                  label: (
                    <span>
                      {ctIntl.formatDuration(activity.timeStats.drivingTime, {
                        includeSeconds: true,
                      })}
                    </span>
                  ),
                  placement: 'top',
                }}
              >
                {drivingTime.formatted}
              </DataWithBar.UnitValueWithTooltip>
            ),
          }}
          total={activity.timeStats.totalTime}
          label={ctIntl.formatMessage({
            id: 'map.tachographDrivers.driverActivityStatus.DRIVING',
          })}
          barColor={theme.colors.tachographDriverActivity.DRIVING}
        />
        <DataWithBar
          unit={{
            value: activity.timeStats.otherWorkTime,
            DisplayedValue: (
              <DataWithBar.UnitValueWithTooltip
                tooltipProps={{
                  label: (
                    <span>
                      {ctIntl.formatDuration(activity.timeStats.otherWorkTime, {
                        includeSeconds: true,
                      })}
                    </span>
                  ),
                  placement: 'top',
                }}
              >
                {otherWorkTime.formatted}
              </DataWithBar.UnitValueWithTooltip>
            ),
          }}
          total={activity.timeStats.totalTime}
          label={ctIntl.formatMessage({
            id: 'map.tachographDrivers.driverActivityStatus.OTHER_WORK',
          })}
          barColor={theme.colors.tachographDriverActivity.OTHER_WORK}
        />
        <DataWithBar
          unit={{
            value: activity.timeStats.availableTime,
            DisplayedValue: (
              <DataWithBar.UnitValueWithTooltip
                tooltipProps={{
                  label: (
                    <span>
                      {ctIntl.formatDuration(activity.timeStats.availableTime, {
                        includeSeconds: true,
                      })}
                    </span>
                  ),
                  placement: 'top',
                }}
              >
                {availableTime.formatted}
              </DataWithBar.UnitValueWithTooltip>
            ),
          }}
          total={activity.timeStats.totalTime}
          label={ctIntl.formatMessage({
            id: 'map.tachographDrivers.driverActivityStatus.AVAILABLE',
          })}
          barColor={theme.colors.tachographDriverActivity.AVAILABLE}
        />
        <DataWithBar
          unit={{
            value: activity.timeStats.restTime,
            DisplayedValue: (
              <DataWithBar.UnitValueWithTooltip
                tooltipProps={{
                  label: (
                    <span>
                      {ctIntl.formatDuration(activity.timeStats.restTime, {
                        includeSeconds: true,
                      })}
                    </span>
                  ),
                  placement: 'top',
                }}
              >
                {restTime.formatted}
              </DataWithBar.UnitValueWithTooltip>
            ),
          }}
          total={activity.timeStats.totalTime}
          label={ctIntl.formatMessage({
            id: 'map.tachographDrivers.driverActivityStatus.REST',
          })}
          barColor={theme.colors.tachographDriverActivity.REST}
        />
        <DataWithBar
          unit={{
            value: activity.timeStats.unknownTime,
            DisplayedValue: (
              <DataWithBar.UnitValueWithTooltip
                tooltipProps={{
                  label: (
                    <span>
                      {ctIntl.formatDuration(activity.timeStats.unknownTime, {
                        includeSeconds: true,
                      })}
                    </span>
                  ),
                  placement: 'top',
                }}
              >
                {unknownTime.formatted}
              </DataWithBar.UnitValueWithTooltip>
            ),
          }}
          total={activity.timeStats.totalTime}
          label={ctIntl.formatMessage({
            id: 'map.tachographDrivers.driverActivityStatus.UNKNOWN',
          })}
          barColor={theme.colors.tachographDriverActivity.UNKNOWN}
        />
        <DataWithBar
          unit={{
            value: activity.timeStats.drivingTime + activity.timeStats.otherWorkTime,
            DisplayedValue: (
              <DataWithBar.UnitValueWithTooltip
                tooltipProps={{
                  label: (
                    <span>
                      {ctIntl.formatDuration(
                        activity.timeStats.drivingTime +
                          activity.timeStats.otherWorkTime,
                        {
                          includeSeconds: true,
                        },
                      )}
                    </span>
                  ),
                  placement: 'top',
                }}
              >
                {amplitudeTime.formatted}
              </DataWithBar.UnitValueWithTooltip>
            ),
          }}
          total={activity.timeStats.totalTime}
          label={ctIntl.formatMessage({
            id: 'map.tachographDrivers.driverActivityStatus.amplitude',
          })}
          barColor={theme.colors.tachographDriverActivity.DRIVING}
        />
        {isETachoDriver && (
          <DataWithBar
            unit={{
              value: activity.timeStats.workForOtherEntitiesTime,
              DisplayedValue: (
                <DataWithBar.UnitValueWithTooltip
                  tooltipProps={{
                    label: (
                      <span>
                        {ctIntl.formatDuration(
                          activity.timeStats.workForOtherEntitiesTime,
                          {
                            includeSeconds: true,
                          },
                        )}
                      </span>
                    ),
                    placement: 'top',
                  }}
                >
                  {workForOtherEntitiesTime.formatted}
                </DataWithBar.UnitValueWithTooltip>
              ),
            }}
            total={activity.timeStats.totalTime}
            label={ctIntl.formatMessage({
              id: 'map.tachographDrivers.driverActivityStatus.WORK_FOR_OTHER_ENTITIES',
            })}
            barColor={theme.colors.tachographDriverActivity.WORK_FOR_OTHER_ENTITIES}
          />
        )}
      </DataWithBarContainer>
    </ActivityTimesContainer>
  )
}

export default ActivityTimesData

const ActivityTimesContainer = styled.div`
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  gap: ${spacing[5]};
`

const DataWithBarContainer = styled.div`
  display: flex;
  flex: 1;
  flex-flow: row wrap;
  align-items: baseline;
  height: min-content;
  gap: ${spacing[2]} 30px;
`
