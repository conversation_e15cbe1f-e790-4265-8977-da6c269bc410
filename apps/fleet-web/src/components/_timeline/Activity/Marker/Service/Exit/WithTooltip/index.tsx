import * as React from 'react'
import styled from 'styled-components'

import { ctIntl } from 'src/util-components/ctIntl'
import ServiceExitMarker from '..'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import { getPctSeconds } from 'src/modules/map-view/DriversMapView/Tachograph/utils'

const ServiceExitMarkerWithToolTip = (
  props: React.ComponentProps<typeof ServiceExitMarker>,
) => {
  const endSeconds = getPctSeconds(props.pctLeft)
  const endTime = ctIntl.getDurationWithFormattedHourMinute(endSeconds, true)

  return (
    <ArrowedTooltip
      placement="top"
      label={
        <TooltipContainer>
          <span>
            {ctIntl.formatMessage({
              id: 'map.tachographDrivers.exitService',
            })}
          </span>
          <span>{endTime.formatted}</span>
        </TooltipContainer>
      }
    >
      {ServiceExitMarker({ ...props })}
    </ArrowedTooltip>
  )
}
export default ServiceExitMarkerWithToolTip

const TooltipContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`
