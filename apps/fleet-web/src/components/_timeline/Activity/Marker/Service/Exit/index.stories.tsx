import styled from 'styled-components'
import { number } from '@storybook/addon-knobs'
import ServiceExitMarker from '.'

export default {
  component: ServiceExitMarker,
}

export const Normal = () => {
  const pctLeft = number('PCT Left', 0, {
    range: true,
    min: 0,
    max: 100,
    step: 1,
  })

  return (
    <Container>
      <ServiceExitMarker pctLeft={pctLeft} />
    </Container>
  )
}

const Container = styled.div`
  position: relative;
  margin: 0px 20px;
`
