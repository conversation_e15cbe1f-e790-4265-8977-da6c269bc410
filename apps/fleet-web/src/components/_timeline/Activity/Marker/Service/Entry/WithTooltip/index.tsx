import * as React from 'react'
import styled from 'styled-components'

import { ctIntl } from 'src/util-components/ctIntl'
import ServiceEntryMarker from '..'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import { getPctSeconds } from 'src/modules/map-view/DriversMapView/Tachograph/utils'

const ServiceEntryMarkerWithToolTip = (
  props: React.ComponentProps<typeof ServiceEntryMarker>,
) => {
  const startSeconds = getPctSeconds(props.pctLeft)

  const startTime = ctIntl.getDurationWithFormattedHourMinute(startSeconds, true)

  return (
    <ArrowedTooltip
      placement="top"
      label={
        <TooltipContainer>
          <span>
            {ctIntl.formatMessage({
              id: 'map.tachographDrivers.entryService',
            })}
          </span>
          <span>{startTime.formatted}</span>
        </TooltipContainer>
      }
    >
      {ServiceEntryMarker({ ...props })}
    </ArrowedTooltip>
  )
}

export default ServiceEntryMarkerWithToolTip

const TooltipContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`
