import * as React from 'react'
import styled from 'styled-components'
import { variables } from 'src/shared/components/styled/global-styles'
import TimelineBar from '../Bar'
import ArrowedTooltip from '../../_popups/Tooltip/Arrowed'

type Props = {
  children?: React.ReactNode
  className?: string
}

const TimelineActivity = ({ children, className = '' }: Props) => (
  <TimelineActivityContainer className={className}>
    <TimelineActivityBackground />
    {children}
  </TimelineActivityContainer>
)
const TimelineActivityContainer = styled.div`
  display: flex;
  position: relative;
  height: 15px;
  width: 100%;
`

const TimelineActivityBackground = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${variables.gray20};
`

const TimelineActivityFragment = TimelineBar.Fragment

type WithTooltipProps = React.ComponentProps<typeof TimelineActivityFragment> & {
  tooltipProps: Omit<React.ComponentProps<typeof ArrowedTooltip>, 'children'>
}

const TimelineActivityFragmentWithTooltip = ({
  tooltipProps,
  ...props
}: WithTooltipProps) => (
  <ArrowedTooltip {...tooltipProps}>
    <TimelineActivityFragment {...props} />
  </ArrowedTooltip>
)

export default Object.assign(TimelineActivity, {
  Container: TimelineActivityContainer,
  Background: TimelineActivityBackground,
  Fragment: TimelineActivityFragment,
  FragmentWithTooltip: TimelineActivityFragmentWithTooltip,
})
