import { useContext } from 'react'
import * as React from 'react'
import styled from 'styled-components'
import Icon from 'src/components/Icon'
import type { FixMeAny } from 'src/types'

type Props = {
  children: React.ReactNode
  style: React.CSSProperties
  statusId: number
  title: string
  taskGroupId?: number | string
  event: Record<string, FixMeAny>
} & React.HTMLAttributes<HTMLDivElement>

type TooltipType = {
  visible: boolean
  x: number
  y: number
  task: FixMeAny
  isTaskStep: boolean
  taskStep: FixMeAny
  setTooltip?: React.Dispatch<React.SetStateAction<TooltipType>>
}

const defaultValue: TooltipType = {
  visible: false,
  x: 0,
  y: 0,
  task: {},
  taskStep: {},
  isTaskStep: false,
}

const TooltipContext = React.createContext(defaultValue)

type Fragmentprops = {
  left: number
  width: number
} & Pick<Props, 'statusId'>

// TODO: To check webpack aliases for storybook and reimport TASK_STATUS maps from utils folder
const TASK_STATUS_TO_ID = {
  CREATED: 0,
  ACCEPTED: 2,
  STARTED: 3,
  COMPLETED: 6,
  REJECTED: 12,
  IN_PROGRESS: 16,
  SUSPENDED: 17,
}

const TASK_STATUS_COLOR = {
  [TASK_STATUS_TO_ID.CREATED]: '#53B8C6',
  [TASK_STATUS_TO_ID.ACCEPTED]: 'lightBlue',
  [TASK_STATUS_TO_ID.STARTED]: 'blue',
  [TASK_STATUS_TO_ID.COMPLETED]: '#2DAB33',
  [TASK_STATUS_TO_ID.REJECTED]: '#CE5239',
  [TASK_STATUS_TO_ID.IN_PROGRESS]: '#53B8C6',
  [TASK_STATUS_TO_ID.SUSPENDED]: 'orange',
}

function TaskPill({ children, statusId, title, taskGroupId, style, event }: Props) {
  const isRejected = statusId === TASK_STATUS_TO_ID.REJECTED
  const tooltipContext = useContext(TooltipContext)

  // Hover In handler
  const onTaskMouseOver = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>,
    task: FixMeAny,
    visible: boolean,
  ) => {
    // Set mouse position
    const bounds = e.currentTarget.getBoundingClientRect()
    const x = bounds.x + bounds.width / 2 - 60
    const y = bounds.y + 50
    if (tooltipContext.setTooltip) {
      tooltipContext.setTooltip({
        ...tooltipContext,
        visible: visible,
        x: x,
        y: y,
        task: task,
      })
    }
  }
  // Hover Out handler
  const onTaskMouseOut = (
    _: React.MouseEvent<HTMLDivElement, MouseEvent>,
    visible: boolean,
  ) => {
    // Reset Mouse Pos
    if (tooltipContext.setTooltip) {
      tooltipContext.setTooltip({
        ...tooltipContext,
        visible: visible,
        x: 0,
        y: 0,
        task: {},
      })
    }
  }

  return (
    <PillsWrapper style={style}>
      <TaskContainer
        onMouseOver={(e) => onTaskMouseOver(e, event, true)}
        onMouseOut={(e) => onTaskMouseOut(e, false)}
      >
        <IconStyle color={TASK_STATUS_COLOR[statusId]}>
          <Icon
            icon={isRejected ? 'times' : 'check'}
            color={'white'}
          />
        </IconStyle>
        <Title>{title}</Title>
        <RepeatIcon>{taskGroupId && <Icon icon={['far', 'repeat']} />}</RepeatIcon>
      </TaskContainer>
      <IndicatorContainer>{children}</IndicatorContainer>
    </PillsWrapper>
  )
}

const PillsWrapper = styled.div`
  width: ${(props) => props.style && props.style.width}% !important;
  left: ${(props) => props.style && props.style.left}% !important;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
`

const TaskContainer = styled.div`
  border-radius: 8px;
  border: 1px solid #cccccc;
  background-color: #eeeeee;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 5px;
  width: 100%;
  font-size: calc(8px + 0.4vw);
  overflow: hidden;
`

const IconStyle = styled.div`
  background-color: ${(props) => props.color};
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 30px;
  min-width: 30px;
  z-index: 10;
`

const Title = styled.div`
  padding-left: 10px;
  font-family: Roboto, sans-serif;
  color: #333333;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
`

const RepeatIcon = styled.div`
  display: flex;
  margin-left: auto;
  padding-right: 10px;
`

const IndicatorContainer = styled.div`
  background-color: #eeeeee;
  border-radius: 11px;
  height: 14px;
  position: relative;
  border: 1px solid #cccccc;
  margin-top: 5px;
`

const IndicatorFragment = styled.div<Fragmentprops>`
  background-color: ${(props) => TASK_STATUS_COLOR[props.statusId]};
  left: ${(props) => props.left}%;
  width: ${(props) => props.width}%;
  position: absolute;
  border-radius: 11px;
  border: 1px solid ${(props) => TASK_STATUS_COLOR[props.statusId]};
  height: 100%;
`

export default Object.assign(TaskPill, {
  Indicator: IndicatorFragment,
})
export { TooltipContext }
