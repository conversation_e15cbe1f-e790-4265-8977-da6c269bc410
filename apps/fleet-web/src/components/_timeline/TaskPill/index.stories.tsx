import type { StyledComponentInnerOtherProps } from 'styled-components'
import TaskPill from './index'

export default {
  component: TaskPill,
}

export const Normal = () => (
  <TaskPill
    style={{ width: '30', left: '20' }}
    statusId={12}
    title={'Test Task'}
    taskGroupId={'12'}
    event={{}}
  >
    {indicators.map((indicator) => (
      <TaskPill.Indicator
        key={indicator.left}
        {...indicator}
      />
    ))}
  </TaskPill>
)

const indicators: Array<StyledComponentInnerOtherProps<typeof TaskPill.Indicator>> = [
  { statusId: 6, left: 0, width: 20 },
  { statusId: 12, left: 30, width: 30 },
]
