import { useCallback } from 'react'
import * as React from 'react'
import styled from 'styled-components'
import type moment from 'moment-timezone'
import { isEmpty } from 'lodash'

import { AutoSizer, List } from 'react-virtualized'

import { Grid } from '../SharedStyles'
import EventRow from '../EventRow'

import type { FixMeAny } from 'src/types'

type ReactDivMouseEvent = React.MouseEvent<HTMLDivElement, MouseEvent>

type Props = {
  cellCount: number
  users: Array<FixMeAny>
  startTime: moment.Moment | string
  hoverIndex: number | null
  onMouseOver: (event: ReactDivMouseEvent, index: number) => void
  onMouseOut: (event: ReactDivMouseEvent) => void
}

/**
 * Renders the entire table grid with rows, cells and events
 * @param cellCount
 * @param users
 * @param startTime
 * @constructor
 */
function EventGrid({ cellCount, users, startTime }: Props) {
  const renderLayout = useCallback(() => {
    const layoutRows = new Array(4).fill(NaN).map((_, i) => ({ id: i }))
    return layoutRows.map((row) => (
      <EventRow
        key={row.id}
        cellCount={8}
        startTime={startTime}
      />
    ))
  }, [startTime])

  const renderVirtualizedRows = (data: Array<FixMeAny>) => (
    <div>
      <AutoSizer disableHeight>
        {({ width }) => (
          <List
            rowCount={data.length}
            rowHeight={70}
            width={width}
            height={560}
            autoHeight={data.length < 8}
            rowRenderer={({ index, style }) => renderRow(data[index], style, index)}
          />
        )}
      </AutoSizer>
    </div>
  )

  const renderRow = useCallback(
    (user: FixMeAny, style: React.CSSProperties, index: number) => (
      <EventRow
        style={style}
        key={index}
        cellCount={cellCount}
        user={user}
        startTime={startTime}
      />
    ),
    [cellCount, startTime],
  )

  return (
    <MainGridContainer>
      {isEmpty(users) ? renderLayout() : renderVirtualizedRows(users)}
    </MainGridContainer>
  )
}

export default EventGrid

const MainGridContainer = styled(Grid)`
  width: 100%;
`
