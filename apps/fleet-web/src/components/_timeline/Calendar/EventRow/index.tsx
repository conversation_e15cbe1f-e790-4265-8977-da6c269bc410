import { memo, Fragment, useCallback, useContext } from 'react'
import * as React from 'react'
import styled from 'styled-components'
import moment from 'moment-timezone'
import { isEmpty } from 'lodash'

import { Row } from '../SharedStyles'
import EventCell from '../EventCell'
import EventContainer from '../EventContainer'
import TaskPill from '../../TaskPill'
import CommunicatorUser from 'src/components/_user/CommunicatorUser'

import type { FixMeAny } from 'src/types'
import { TooltipContext } from 'src/modules/tasks/components/task-tooltip'

const DATE_FORMAT = 'YYYY-MM-DD hh:mm:ss:Z'

type Props = {
  cellCount: number
  user?: Record<string, FixMeAny>
  startTime: moment.Moment | string
} & React.HTMLAttributes<HTMLDivElement>

/**
 * Calculate Percentage to nearest 2 decimal place
 * @param numerator
 * @param denominator
 * @returns string
 */
function calculatePercent(numerator: number, denominator: number): string {
  return ((numerator / denominator) * 100).toFixed(2)
}

/**
 * Renders the row of individual cells
 * @constructor
 */
function EventRow({ cellCount = 8, startTime, user, ...props }: Props) {
  const events = user?.tasks
  const tooltipContext = useContext(TooltipContext)
  const rowCells = cellCount + 1
  /**
   * Render Background Cells
   * @readonly
   */
  const renderCells = useCallback(
    () =>
      Array(rowCells)
        .fill(NaN)
        .map((_, i) => {
          if (user && i === 0) {
            return (
              <Fragment key={i}>
                <EventCell
                  key={user.id}
                  center
                  padding={false}
                >
                  <UserContainer>
                    <CommunicatorUser
                      name={user.userName}
                      vehicle={user.vehicleRegistration}
                      avatar={user.avatar}
                      mobileDevice={user.mobileDeviceDescription}
                      stars={user.stars}
                    />
                  </UserContainer>
                </EventCell>
                <Gutter />
              </Fragment>
            )
          }
          // eslint-disable-next-line react/no-array-index-key
          return <EventCell key={i} />
        }),
    [rowCells, user],
  )

  /**
   * Render Inline Events
   */
  const renderEvents = useCallback(
    () =>
      events &&
      events.map((event: FixMeAny) => {
        const endTime = moment(startTime).add(cellCount, 'hour')

        let taskStart = moment(event.scheduledStartTime, DATE_FORMAT)

        if (taskStart.isSameOrBefore(moment(startTime, DATE_FORMAT))) {
          taskStart = moment(startTime, DATE_FORMAT)
        } else if (taskStart.isAfter(endTime)) {
          return null
        }

        // Calculate Task Offset X
        const offsetDuration = taskStart.diff(moment(startTime, DATE_FORMAT))

        const offsetHours = moment.duration(offsetDuration).asHours()

        const offsetX = calculatePercent(offsetHours, cellCount)

        let taskEnd = moment(event.scheduledEndTime, DATE_FORMAT)
        if (taskEnd.isSameOrAfter(endTime)) {
          taskEnd = moment(endTime, DATE_FORMAT)
        } else if (taskEnd.isBefore(startTime)) {
          return null
        }

        // Calculate Task Width
        const taskDuration = taskEnd.diff(taskStart)
        const taskHours = moment.duration(taskDuration).asHours()
        const width = calculatePercent(taskHours, cellCount)

        // Render Task Steps
        const taskSteps = event.taskSteps.map((step: FixMeAny) => {
          const taskStepStart = moment(step.taskStepStartTime, DATE_FORMAT)
          const taskStepEnd = moment(step.taskStepEndTime, DATE_FORMAT)

          // Calculate Task Step Offset X
          const taskStepOffsetDuration = taskStepStart.diff(taskStart)
          const taskStepOffsetHours = moment.duration(taskStepOffsetDuration).asHours()
          const taskStepOffsetX = calculatePercent(taskStepOffsetHours, taskHours)

          // Calculate Task Step Width
          const taskStepDuration = taskStepEnd.diff(taskStepStart)
          const taskStepHours = moment.duration(taskStepDuration).asHours()
          const taskStepWidth = calculatePercent(taskStepHours, taskHours)

          return {
            location: step.customerAddress,
            taskStepStateText: step.taskStepStateText,
            taskStepDescription: step.taskStepDescription,
            taskStepStartTime: step.scheduledStartTime,
            taskStepEndTime: step.scheduledEndTime,
            taskStepId: step.taskStepId,
            statusId: step.taskStepStateId,
            left: taskStepOffsetX,
            width: taskStepWidth,
          }
        })

        // Hover In handler
        const onTaskMouseOver = (
          e: React.MouseEvent<HTMLDivElement, MouseEvent>,
          task: FixMeAny,
          visible: boolean,
          isTaskStep: boolean,
        ) => {
          // Set mouse position
          const bounds = e.currentTarget.getBoundingClientRect()
          const x = bounds.x + bounds.width / 4 - 90
          const y = bounds.y + 20
          if (tooltipContext.setTooltip) {
            tooltipContext.setTooltip({
              ...tooltipContext,
              visible: visible,
              isTaskStep: isTaskStep,
              x: x,
              y: y,
              taskStep: task,
            })
          }
        }
        // Hover Out handler
        const onTaskMouseOut = (
          _: React.MouseEvent<HTMLDivElement, MouseEvent>,
          visible: boolean,
          isTaskStep: boolean,
        ) => {
          // Reset Mouse Pos
          if (tooltipContext.setTooltip) {
            tooltipContext.setTooltip({
              ...tooltipContext,
              visible: visible,
              isTaskStep: isTaskStep,
              x: 0,
              y: 0,
              taskStep: {},
            })
          }
        }

        return (
          <TaskPill
            key={event.taskId}
            style={{ width, left: offsetX }}
            statusId={event.taskStateId}
            title={event.taskDescription}
            taskGroupId={event.taskGroupId}
            event={event}
          >
            {!isEmpty(taskSteps) &&
              taskSteps.map((step: (typeof taskSteps)[0]) => (
                <TaskPill.Indicator
                  key={step.taskStepId}
                  {...step}
                  onMouseOver={(e) => onTaskMouseOver(e, step, true, true)}
                  onMouseOut={(e) => onTaskMouseOut(e, false, false)}
                />
              ))}
          </TaskPill>
        )
      }),
    [events, startTime, cellCount, tooltipContext],
  )

  return (
    <Row {...props}>
      {renderCells()}
      {!isEmpty(events) && <EventContainer>{renderEvents()}</EventContainer>}
    </Row>
  )
}

export default memo(EventRow)

const Gutter = styled.div`
  width: 16px;
  border-left: 1px solid #dddddd;
  height: 100%;
  background: #f9f9f9;
`

const UserContainer = styled.div`
  width: 240px;
  overflow: hidden;
`
