import { useState, memo, useContext } from 'react'
import styled, { ThemeContext } from 'styled-components'
import { connect } from 'react-redux'
import type moment from 'moment-timezone'
import { ctIntl } from 'src/util-components/ctIntl'

import { setTimelineView } from 'duxs/tasks/tasks'
import EventCellHeader from 'src/components/_timeline/Calendar/EventCellHeader'
import { getTaskTimelineWorkingHours } from 'duxs/tasks/tasks'
import type { AppState } from 'src/root-reducer'

import Tabs from '../../../_tabs'
import TooltipTimepicker from '../../../_tabs/TooltipTimepicker'
import type { ComponentViews } from '../constants'

type Props = {
  cellCount: number
  startTime: moment.Moment | string
  from: number
  to: number
  currentView: ComponentViews
} & typeof actionCreators

function Header({
  handleViewChange,
  currentView,
  cellCount,
  startTime,
  from,
  to,
}: Props) {
  const [tabs] = useState([
    {
      label: ctIntl.formatMessage({ id: 'Work Hours' }),
      id: 'work',
      hasToolTip: true,
    },
    {
      label: ctIntl.formatMessage({ id: '24 Hour' }),
      id: 'day',
      hasToolTip: false,
    },
  ])

  const themeContext = useContext(ThemeContext)
  return (
    <HeaderContainer>
      <Tabs
        style={{ marginRight: 16 }}
        onClick={handleViewChange}
        activeTab={currentView}
        tabs={tabs}
        activeColor={themeContext.colors.styleActiveButtonsColour}
        tooltipProps={{
          label: <TooltipTimepicker />,
          placement: 'bottom',
        }}
      />
      <EventCellHeader
        cellCount={cellCount}
        startTime={startTime}
        from={from}
        to={to}
      />
    </HeaderContainer>
  )
}

const mapStateToProps = (state: AppState) => {
  const { tasksTimelineStartTime, tasksTimelineClosingTime } =
    getTaskTimelineWorkingHours(state)
  return {
    from: tasksTimelineStartTime,
    to: tasksTimelineClosingTime,
  }
}

const actionCreators = {
  handleViewChange: setTimelineView,
}

export default memo(connect(mapStateToProps, actionCreators)(Header))

const HeaderContainer = styled.div`
  display: flex;
`
