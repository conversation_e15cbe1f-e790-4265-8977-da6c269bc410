import * as React from 'react'
import styled from 'styled-components'

type Props = {
  children: React.ReactNode
}

/**
 * Renders the container to hold event pills
 * @param children
 * @constructor
 */
function EventContainer({ children }: Props) {
  return <Container>{children}</Container>
}

export default EventContainer

/**
 * Renders width of container minus user cell + gutter size
 * Offsets x by width of user cell + gutter size
 */
const Container = styled.div`
  position: absolute;
  width: calc(100% - 241px - 16px);
  height: 70px;
  margin-left: calc(241px + 16px);
`
