import { memo, useState } from 'react'
import * as React from 'react'
import type moment from 'moment-timezone'
import styled from 'styled-components'

import { MainView } from '../SharedStyles'
import EventGrid from '../EventGrid'

import TaskTimelineToolTip, {
  TooltipContext,
} from 'src/modules/tasks/components/task-tooltip'
import type { FixMeAny } from 'src/types'

type Props = {
  users: Array<FixMeAny>
  cellCount: number
  startTime: moment.Moment | string
} & React.HTMLAttributes<HTMLDivElement>

function CalendarView({ users, cellCount, startTime }: Props) {
  const [tooltip, setTooltip] = useState({
    visible: false,
    x: 0,
    y: 0,
    task: {},
    taskStep: {},
    isTaskStep: false,
  })

  const [hoverIndex, setHoverIndex] = useState<number | null>(null)

  const handleMouseOver = (
    _: React.MouseEvent<HTMLDivElement, MouseEvent>,
    index: number,
  ) => {
    setHoverIndex(index)
  }

  const handleMouseOut = (_: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    setHoverIndex(null)
  }

  const handleScrollEvent = (e: FixMeAny) => {
    if (e) {
      setTooltip({
        ...tooltip,
        visible: false,
      })
    }
  }

  return (
    <TooltipContext.Provider value={{ ...tooltip, setTooltip }}>
      <MainView onScroll={(e: FixMeAny) => handleScrollEvent(e)}>
        <GutterOverlayLayer />
        <EventGrid
          hoverIndex={hoverIndex}
          onMouseOver={handleMouseOver}
          onMouseOut={handleMouseOut}
          cellCount={cellCount}
          users={users}
          startTime={startTime}
        />
        {tooltip.visible && <TaskTimelineToolTip />}
      </MainView>
    </TooltipContext.Provider>
  )
}

export default memo(CalendarView)

const GutterOverlayLayer = styled.div`
  width: 15px;
  height: 105%;
  position: absolute;
  z-index: 100;
  background: #f9f9f9;
  top: -2.5%;
  left: 242px;
`
