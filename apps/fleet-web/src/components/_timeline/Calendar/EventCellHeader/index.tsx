import { useCallback } from 'react'
import moment from 'moment-timezone'

import { Row } from '../SharedStyles'
import EventCell from '../EventCell'
import styled from 'styled-components'

type Props = {
  cellCount: number
  startTime: moment.Moment | string
  from?: number
  to?: number
}

/**
 * Renders Cell Header to display hourly timestamps
 * @param cellCount
 * @param startTime
 * @constructor
 */
function EventCellHeader({ cellCount, startTime, from, to }: Props) {
  const renderHeader = useCallback(() => {
    const hoursArray: Array<string> = []

    const date = moment(startTime, 'YYYY/MM/DD').format('YYYY/MM/DD')
    let windowStartTime = moment(startTime, 'YYYY/MM/DD')

    if (from && to && cellCount !== 24) {
      windowStartTime = moment(startTime, 'YYYY/MM/DD').add(from, 'hour')
    }

    if (cellCount !== 24) {
      for (let i = 0; i < cellCount; i++) {
        const hour = moment(windowStartTime, 'YYYY/MM/DD')
          .add(i, 'hour')
          .format('HH:mm')
        hoursArray.push(hour)
      }
    } else {
      for (let i = 0; i < cellCount; i++) {
        if (i % 4 === 0) {
          const hour = moment(windowStartTime, 'YYYY/MM/DD')
            .add(i, 'hour')
            .format('HH:mm')
          hoursArray.push(hour)
        }
      }
    }

    return hoursArray.map((hours, i) => (
      <EventCell
        key={hours}
        largeTitle={i === 0 ? date : undefined}
        title={hours}
        border={false}
        padding={false}
      />
    ))
  }, [cellCount, from, startTime, to])

  return <HeaderRowContainer>{renderHeader()}</HeaderRowContainer>
}

export default EventCellHeader

const HeaderRowContainer = styled(Row)`
  width: 100%;
  pointer-events: none;
`
