import { useMemo } from 'react'
import { connect } from 'react-redux'
import styled from 'styled-components'
import moment from 'moment-timezone'

import CalendarView from './View'
import Header from './Header'

import type { AppState } from 'src/root-reducer'
import { getTaskTimelineViewState, getTaskTimelineWorkingHours } from 'duxs/tasks/tasks'
import type { FixMeAny } from 'src/types'

const DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss:Z'

type Props = {
  data: Array<FixMeAny>
  selectedDate: string
} & ReturnType<typeof mapStateToProps>

function Calendar({ data, selectedDate, view, workStart, workEnd }: Props) {
  const dayCount = 24
  const dayStart = moment().startOf('day')

  const workCount = useMemo(() => workEnd - workStart, [workStart, workEnd])
  const cellCount = useMemo(
    () => (view === 'work' ? workCount : dayCount),
    [view, workCount],
  )

  const workingTime = useMemo(
    () =>
      view === 'work'
        ? moment.utc(workStart * 3600 * 1000).format('HH:mm:ss')
        : dayStart.format('HH:mm:ss'),
    [dayStart, view, workStart],
  )

  const startTime = moment(`${selectedDate} ${workingTime}`, DATE_FORMAT).format(
    DATE_FORMAT,
  )

  const commonProps = {
    cellCount,
    startTime,
  }

  return (
    <Container>
      <Header
        currentView={view}
        {...commonProps}
      />
      <CalendarView
        users={data}
        {...commonProps}
      />
    </Container>
  )
}

const mapStateToProps = (state: AppState) => {
  const { view } = getTaskTimelineViewState(state)
  const { tasksTimelineStartTime, tasksTimelineClosingTime } =
    getTaskTimelineWorkingHours(state)
  return {
    view,
    workStart: tasksTimelineStartTime,
    workEnd: tasksTimelineClosingTime,
  }
}

export default connect(mapStateToProps)(Calendar)

const Container = styled.div`
  display: flex;
  flex-direction: column;
`
