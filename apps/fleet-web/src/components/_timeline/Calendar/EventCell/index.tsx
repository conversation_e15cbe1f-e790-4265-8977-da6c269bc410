import * as React from 'react'
import styled from 'styled-components'

type Props = {
  children?: React.ReactNode
  largeTitle?: string
  title?: string
  border?: boolean
  center?: boolean
  padding?: boolean
} & React.HTMLAttributes<HTMLDivElement>

function EventCell({
  children,
  title,
  largeTitle,
  border = true,
  center = false,
  padding = true,
  ...props
}: Props) {
  return (
    <Cell
      {...props}
      border={border}
      center={center}
      padding={padding}
    >
      {largeTitle && <CellLargeTitle>{largeTitle}</CellLargeTitle>}
      {title && <CellTitle>{title}</CellTitle>}
      <ChildWrapper>{children}</ChildWrapper>
    </Cell>
  )
}

export default EventCell

const Cell = styled.div<Props>`
  display: flex;
  flex-direction: column;
  flex: 1 1;
  justify-content: ${(props) => (props.center ? 'center' : 'flex-end')};
  min-height: 70px;
  border-top: ${(props) => (props.border ? '1px solid #dddddd' : 'none')};
  border-left: ${(props) => (props.border ? '1px solid #dddddd' : 'none')};
  font-family: 'Roboto Condensed', sans-serif;
  padding: ${(props) => (props.padding ? '8px 16px' : 0)};
`

const CellLargeTitle = styled.h2`
  margin: 0 0 5px 0;
  display: inline-block;
  font-size: 1em;
  font-weight: 500;
`

const CellTitle = styled.span`
  display: inline-block;
  font-size: 0.8em;
  color: #ababab;
  margin-bottom: 5px;
`

const ChildWrapper = styled.div`
  justify-self: center;
`
