import { forwardRef } from 'react'
import TaxiIcon from 'assets/svg/VehiclePopover/taxi.svg'
import type { SVGIconBaseProps } from 'src/components/Icon/BaseSVG'
import SVGIcon from 'src/components/Icon/SVGIcon'

type Props = {
  className?: string
} & SVGIconBaseProps

const Taxi = forwardRef<typeof SVGIcon, Props>(function Taxi(
  { height = 22, className = '', ...rest },
  ref,
) {
  return (
    <SVGIcon
      ref={ref}
      height={height}
      svg={TaxiIcon}
      className={className}
      {...rest}
    />
  )
})

export default Taxi
