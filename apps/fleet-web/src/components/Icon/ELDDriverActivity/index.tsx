import { match } from 'ts-pattern'
import type { BaseSymbol } from '../TachographDriverActivity/BaseSymbol'
import type { TimelineStatus } from 'src/modules/map-view/DriversMapView/ELD/types'
import StatusIcon from 'src/modules/map-view/DriversMapView/ELD/LeftPanel/Driver/StatusIcon'

type Props = {
  status: TimelineStatus
  className?: string
} & BaseSymbol

export default function ELDDriverActivityIcon({ status }: Props) {
  return match(status)
    .with('driving', 'driving-manual', () => (
      <StatusIcon
        status={'DRIVING'}
        size="small"
      />
    ))
    .with('on-duty', () => (
      <StatusIcon
        status={'ON DUTY'}
        size="small"
      />
    ))
    .with('sleeper-berth', () => (
      <StatusIcon
        status={'SLEEPER BERTH'}
        size="small"
      />
    ))
    .with('off-duty', () => (
      <StatusIcon
        status={'OFF DUTY'}
        size="small"
      />
    ))
    .with('pc', () => (
      <StatusIcon
        status={'UNKNOWN'}
        size="small"
      />
    ))
    .exhaustive()
}
