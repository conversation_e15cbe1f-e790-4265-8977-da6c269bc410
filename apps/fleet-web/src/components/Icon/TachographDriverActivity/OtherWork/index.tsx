import styled from 'styled-components'
import TachographIconBase from '../Base'
import TachographOtherWorkSymbol from './Symbol'
import theme from 'src/components/_themes/tachograph'
import type { BaseSymbol } from '../BaseSymbol'

const StyledTachographIconBase = styled(TachographIconBase)`
  background-color: ${theme.colors.tachographDriverActivity['OTHER_WORK']};
`

const TachographOtherWorkIcon = ({ size, color }: BaseSymbol) => (
  <StyledTachographIconBase>
    <TachographOtherWorkSymbol
      size={size}
      color={color}
    />
  </StyledTachographIconBase>
)

export default TachographOtherWorkIcon
