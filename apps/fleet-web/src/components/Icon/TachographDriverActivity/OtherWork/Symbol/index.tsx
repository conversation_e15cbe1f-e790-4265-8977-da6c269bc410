import OtherWorkTimeSymbol from 'src/../assets/svg/TachographDriverActivity/other_work_time.svg'
import TachographBaseSymbol, { type BaseSymbol } from '../../BaseSymbol'

type Props = {
  className?: string
} & BaseSymbol
const TachographOtherWorkSymbol = ({
  size = 'small',
  color = 'white',
  className = '',
}: Props) => (
  <TachographBaseSymbol
    size={size}
    color={color}
    svg={OtherWorkTimeSymbol}
    className={className}
  />
)

export default TachographOtherWorkSymbol
