import TachographOtherWorkSymbol from '.'
import { select } from '@storybook/addon-knobs'
import { storybookSizeOptions } from '../..'
import styled from 'styled-components'

export default {
  component: TachographOtherWorkSymbol,
}

export const Normal = () => {
  const size = select('Size', storybookSizeOptions, storybookSizeOptions['Small'])

  return (
    <Container>
      <TachographOtherWorkSymbol size={size} />
    </Container>
  )
}

const Container = styled.div`
  padding: 10px;
  height: 100%;
  width: 100%;
  background-color: black;
`
