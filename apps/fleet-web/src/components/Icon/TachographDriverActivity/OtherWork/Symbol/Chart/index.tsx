import OtherWorkTimeSymbol from 'src/../assets/svg/TachographDriverActivity/other_work_time.svg'
import type { BaseSymbol } from '../../../BaseSymbol'
import TachographBaseChartSymbol from '../../../BaseSymbol/Chart'

type Props = {
  className?: string
} & BaseSymbol

const TachographChartOtherWorkSymbol = ({
  size = 'big',
  color = 'grey',
  className = '',
}: Props) => (
  <TachographBaseChartSymbol
    size={size}
    color={color}
    svg={OtherWorkTimeSymbol}
    className={className}
  />
)

export default TachographChartOtherWorkSymbol
