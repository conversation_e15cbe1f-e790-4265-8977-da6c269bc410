import styled from 'styled-components'
import TachographIconBase from '../Base'
import TachographUnknownSymbol from './Symbol'
import theme from 'src/components/_themes/tachograph'
import type { BaseSymbol } from '../BaseSymbol'

const StyledTachographIconBase = styled(TachographIconBase)`
  background-color: ${theme.colors.tachographDriverActivity['UNKNOWN']};
`

const TachographUnknownIcon = ({ size, color }: BaseSymbol) => (
  <StyledTachographIconBase>
    <TachographUnknownSymbol
      size={size}
      color={color}
    />
  </StyledTachographIconBase>
)

export default TachographUnknownIcon
