import UnknownActivitySymbol from 'src/../assets/svg/TachographDriverActivity/unknown.svg'
import TachographBaseSymbol, { type BaseSymbol } from '../../BaseSymbol'

type Props = {
  className?: string
} & BaseSymbol

const TachographUnknownActivitySymbol = ({
  size = 'small',
  color = 'white',
  className = '',
}: Props) => (
  <TachographBaseSymbol
    size={size}
    color={color}
    svg={UnknownActivitySymbol}
    className={className}
  />
)

export default TachographUnknownActivitySymbol
