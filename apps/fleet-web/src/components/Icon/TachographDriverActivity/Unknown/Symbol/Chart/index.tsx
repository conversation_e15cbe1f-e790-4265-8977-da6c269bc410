import UnknownActivitySymbol from 'src/../assets/svg/TachographDriverActivity/unknown.svg'
import type { BaseSymbol } from '../../../BaseSymbol'
import TachographBaseChartSymbol from '../../../BaseSymbol/Chart'

type Props = {
  className?: string
} & BaseSymbol

const TachographChartUnknownActivitySymbol = ({
  size = 'big',
  color = 'grey',
  className = '',
}: Props) => (
  <TachographBaseChartSymbol
    size={size}
    color={color}
    svg={UnknownActivitySymbol}
    className={className}
  />
)

export default TachographChartUnknownActivitySymbol
