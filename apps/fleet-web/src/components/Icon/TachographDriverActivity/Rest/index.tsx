import styled from 'styled-components'
import TachographIconBase from '../Base'
import TachographRestSymbol from './Symbol'
import theme from 'src/components/_themes/tachograph'
import type { BaseSymbol } from '../BaseSymbol'

const StyledTachographIconBase = styled(TachographIconBase)`
  background-color: ${theme.colors.tachographDriverActivity['REST']};
`

const TachographRestIcon = ({ size, color }: BaseSymbol) => (
  <StyledTachographIconBase>
    <TachographRestSymbol
      size={size}
      color={color}
    />
  </StyledTachographIconBase>
)

export default TachographRestIcon
