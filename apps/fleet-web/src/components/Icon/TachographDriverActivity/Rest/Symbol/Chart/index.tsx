import RestTimeSymbol from 'src/../assets/svg/TachographDriverActivity/rest_time.svg'
import type { BaseSymbol } from '../../../BaseSymbol'
import TachographBaseChartSymbol from '../../../BaseSymbol/Chart'

type Props = {
  className?: string
} & BaseSymbol

const TachographChartRestSymbol = ({
  size = 'big',
  color = 'grey',
  className = '',
}: Props) => (
  <TachographBaseChartSymbol
    size={size}
    color={color}
    svg={RestTimeSymbol}
    className={className}
  />
)

export default TachographChartRestSymbol
