import RestTimeSymbol from 'src/../assets/svg/TachographDriverActivity/rest_time.svg'
import TachographBaseSymbol, { type BaseSymbol } from '../../BaseSymbol'

type Props = {
  className?: string
} & BaseSymbol

const TachographRestSymbol = ({
  size = 'small',
  color = 'white',
  className = '',
}: Props) => (
  <TachographBaseSymbol
    size={size}
    color={color}
    svg={RestTimeSymbol}
    className={className}
  />
)

export default TachographRestSymbol
