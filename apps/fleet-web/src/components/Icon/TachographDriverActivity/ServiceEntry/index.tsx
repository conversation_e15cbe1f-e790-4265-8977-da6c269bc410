import ServiceEntry from 'src/../assets/svg/TachographDriverActivity/entry_service.svg'
import type { SVGIconBaseProps } from '../../BaseSVG'
import SVGIcon from '../../SVGIcon'

type Props = {
  className?: string
} & SVGIconBaseProps

const TachographServiceEntrySymbol = ({
  height = '8',
  color = '#333333',
  className = '',
}: Props) => (
  <SVGIcon
    height={height}
    color={color}
    svg={ServiceEntry}
    className={className}
  />
)

export default TachographServiceEntrySymbol
