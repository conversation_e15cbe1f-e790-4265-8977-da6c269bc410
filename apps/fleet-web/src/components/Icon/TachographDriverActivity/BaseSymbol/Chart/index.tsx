import { symbolSizeMap, symbolColorMap, type BaseSymbol } from '..'
import SVGChartIcon from '../../../SVGIcon/Chart'

type Props = BaseSymbol & {
  svg: string
  className?: string
}

const TachographBaseChartSymbol = ({
  size = 'big',
  color = 'grey',
  svg,
  className = '',
}: Props) => (
  <SVGChartIcon
    height={symbolSizeMap[size]}
    width={symbolSizeMap[size]}
    color={symbolColorMap[color]}
    svg={svg}
    className={className}
  />
)

export default TachographBaseChartSymbol
