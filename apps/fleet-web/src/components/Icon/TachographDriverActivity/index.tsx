import * as React from 'react'
import type { BaseSymbol } from './BaseSymbol'
import type { TachographDriverActivity } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import TachographAvailableIcon from './Available'
import TachographUnknownIcon from './Unknown'
import TachographOtherWorkIcon from './OtherWork'
import TachographDrivingIcon from './Driving'
import TachographRestIcon from './Rest'
import TachographWorkForOtherEntitiesIcon from './WorkForOtherEntities'

type Props = {
  status: TachographDriverActivity
  className?: string
} & BaseSymbol

export default function TachographDriverActivityIcon({ status, ...rest }: Props) {
  switch (status) {
    case 'AVAILABLE':
      return <TachographAvailableIcon {...rest} />
    case 'DRIVING':
      return <TachographDrivingIcon {...rest} />
    case 'REST':
      return <TachographRestIcon {...rest} />
    case 'OTHER_WORK':
      return <TachographOtherWorkIcon {...rest} />
    case 'WORK_FOR_OTHER_ENTITIES':
      return <TachographWorkForOtherEntitiesIcon {...rest} />
    case 'UNKNOWN':
      return <TachographUnknownIcon {...rest} />
  }
}

export const storybookSizeOptions: Record<
  string,
  React.ComponentProps<typeof TachographDriverActivityIcon>['size']
> = {
  'Extra Small': 'extraSmall',
  Small: 'small',
  Medium: 'medium',
  Big: 'big',
  Max: 'max',
}
