import styled from 'styled-components'
import TachographIconBase from '../Base'
import TachographDrivingSymbol from './Symbol'
import theme from 'src/components/_themes/tachograph'
import type { BaseSymbol } from '../BaseSymbol'

const StyledTachographIconBase = styled(TachographIconBase)`
  background-color: ${theme.colors.tachographDriverActivity['DRIVING']};
`

const TachographDrivingIcon = ({ size, color }: BaseSymbol) => (
  <StyledTachographIconBase>
    <TachographDrivingSymbol
      size={size}
      color={color}
    />
  </StyledTachographIconBase>
)

export default TachographDrivingIcon
