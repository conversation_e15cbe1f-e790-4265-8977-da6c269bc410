import DrivingSymbol from 'src/../assets/svg/TachographDriverActivity/driving_time.svg'
import TachographBaseSymbol, { type BaseSymbol } from '../../BaseSymbol'

type Props = {
  className?: string
} & BaseSymbol

const TachographDrivingSymbol = ({
  size = 'small',
  color = 'white',
  className = '',
}: Props) => (
  <TachographBaseSymbol
    size={size}
    color={color}
    svg={DrivingSymbol}
    className={className}
  />
)

export default TachographDrivingSymbol
