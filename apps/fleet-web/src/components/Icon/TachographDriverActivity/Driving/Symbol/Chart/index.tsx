import DrivingSymbol from 'src/../assets/svg/TachographDriverActivity/driving_time.svg'
import type { BaseSymbol } from '../../../BaseSymbol'
import TachographBaseChartSymbol from '../../../BaseSymbol/Chart'

type Props = {
  className?: string
} & BaseSymbol

const TachographChartDrivingSymbol = ({
  size = 'big',
  color = 'grey',
  className = '',
}: Props) => (
  <TachographBaseChartSymbol
    size={size}
    color={color}
    svg={DrivingSymbol}
    className={className}
  />
)

export default TachographChartDrivingSymbol
