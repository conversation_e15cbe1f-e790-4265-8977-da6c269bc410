import styled from 'styled-components'
import TachographIconBase from '../Base'
import TachographAvailableSymbol from './Symbol'
import theme from 'src/components/_themes/tachograph'
import type { BaseSymbol } from '../BaseSymbol'

const StyledTachographIconBase = styled(TachographIconBase)`
  background-color: ${theme.colors.tachographDriverActivity['AVAILABLE']};
`

const TachographAvailableIcon = ({ size, color }: BaseSymbol) => (
  <StyledTachographIconBase>
    <TachographAvailableSymbol
      size={size}
      color={color}
    />
  </StyledTachographIconBase>
)

export default TachographAvailableIcon
