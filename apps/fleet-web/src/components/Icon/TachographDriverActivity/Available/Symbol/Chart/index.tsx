import AvailableSymbol from 'src/../assets/svg/TachographDriverActivity/available_time.svg'
import type { BaseSymbol } from '../../../BaseSymbol'
import TachographBaseChartSymbol from '../../../BaseSymbol/Chart'

type Props = {
  className?: string
} & BaseSymbol

const TachographChartAvailableSymbol = ({
  size = 'big',
  color = 'grey',
  className = '',
}: Props) => (
  <TachographBaseChartSymbol
    size={size}
    color={color}
    svg={AvailableSymbol}
    className={className}
  />
)

export default TachographChartAvailableSymbol
