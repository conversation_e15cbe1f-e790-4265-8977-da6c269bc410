import AvailableSymbol from 'src/../assets/svg/TachographDriverActivity/available_time.svg'
import TachographBaseSymbol, { type BaseSymbol } from '../../BaseSymbol'

type Props = {
  className?: string
} & BaseSymbol

const TachographAvailableSymbol = ({
  size = 'small',
  color = 'white',
  className = '',
}: Props) => (
  <TachographBaseSymbol
    size={size}
    color={color}
    svg={AvailableSymbol}
    className={className}
  />
)

export default TachographAvailableSymbol
