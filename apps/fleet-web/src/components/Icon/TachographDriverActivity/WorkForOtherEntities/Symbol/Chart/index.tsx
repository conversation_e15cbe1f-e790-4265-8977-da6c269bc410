import WorkForOtherEntitiesActivitySymbol from 'src/../assets/svg/TachographDriverActivity/work_for_other_entities.svg'
import type { BaseSymbol } from '../../../BaseSymbol'
import TachographBaseChartSymbol from '../../../BaseSymbol/Chart'

type Props = {
  className?: string
} & BaseSymbol

const TachographChartWorkForOtherEntitiesActivitySymbol = ({
  size = 'big',
  color = 'grey',
  className = '',
}: Props) => (
  <TachographBaseChartSymbol
    size={size}
    color={color}
    svg={WorkForOtherEntitiesActivitySymbol}
    className={className}
  />
)

export default TachographChartWorkForOtherEntitiesActivitySymbol
