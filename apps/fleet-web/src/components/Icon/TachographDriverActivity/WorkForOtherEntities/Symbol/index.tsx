import WorkForOtherEntitiesActivitySymbol from 'src/../assets/svg/TachographDriverActivity/work_for_other_entities.svg'
import TachographBaseSymbol, { type BaseSymbol } from '../../BaseSymbol'

type Props = {
  className?: string
} & BaseSymbol

const TachographWorkForOtherEntitiesActivitySymbol = ({
  size = 'small',
  color = 'white',
  className = '',
}: Props) => (
  <TachographBaseSymbol
    size={size}
    color={color}
    svg={WorkForOtherEntitiesActivitySymbol}
    className={className}
  />
)

export default TachographWorkForOtherEntitiesActivitySymbol
