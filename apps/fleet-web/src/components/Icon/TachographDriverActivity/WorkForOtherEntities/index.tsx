import styled from 'styled-components'
import TachographIconBase from '../Base'
import TachographWorkForOtherEntitiesSymbol from './Symbol'
import theme from 'src/components/_themes/tachograph'
import type { BaseSymbol } from '../BaseSymbol'

const StyledTachographIconBase = styled(TachographIconBase)`
  background-color: ${theme.colors.tachographDriverActivity['WORK_FOR_OTHER_ENTITIES']};
`

const TachographWorkForOtherEntitiesIcon = ({ size, color }: BaseSymbol) => (
  <StyledTachographIconBase>
    <TachographWorkForOtherEntitiesSymbol
      size={size}
      color={color}
    />
  </StyledTachographIconBase>
)

export default TachographWorkForOtherEntitiesIcon
