import ServiceExit from 'src/../assets/svg/TachographDriverActivity/service_exit.svg'
import type { SVGIconBaseProps } from '../../BaseSVG'
import SVGIcon from '../../SVGIcon'

type Props = {
  className?: string
} & SVGIconBaseProps

const TachographServiceExitSymbol = ({
  height = '8',
  color = '#333333',
  className = '',
}: Props) => (
  <SVGIcon
    height={height}
    color={color}
    svg={ServiceExit}
    className={className}
  />
)

export default TachographServiceExitSymbol
