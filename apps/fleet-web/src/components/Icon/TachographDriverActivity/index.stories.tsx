import * as React from 'react'
import TachographDriverActivityIcon, { storybookSizeOptions } from '.'
import { select } from '@storybook/addon-knobs'

export default {
  component: TachographDriverActivityIcon,
}

export const Normal = () => {
  const status = select('Status', statusOptions, statusOptions['Driving'])

  const size = select('Size', storybookSizeOptions, storybookSizeOptions['Small'])

  return (
    <TachographDriverActivityIcon
      status={status}
      size={size}
    />
  )
}

const statusOptions: Record<
  string,
  React.ComponentProps<typeof TachographDriverActivityIcon>['status']
> = {
  Available: 'AVAILABLE',
  Driving: 'DRIVING',
  'Other Work': 'OTHER_WORK',
  Rest: 'REST',
  'Unknown Activity': 'UNKNOWN',
}
