import { forwardRef } from 'react'
import GreenShieldIcon from 'assets/svg/shield_check_green.svg'
import type { SVGIconBaseProps } from '../../BaseSVG'
import SVGIcon from '../../SVGIcon'

type Props = {
  className?: string
} & SVGIconBaseProps

const GreenShield = forwardRef<typeof SVGIcon, Props>(function GreenShield(
  { height = 34, width = 36, className = '' },
  ref,
) {
  return (
    <SVGIcon
      ref={ref}
      height={height}
      width={width}
      svg={GreenShieldIcon}
      className={className}
    />
  )
})

export default GreenShield
