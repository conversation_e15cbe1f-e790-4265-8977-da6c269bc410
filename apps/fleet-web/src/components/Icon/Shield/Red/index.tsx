import { forwardRef } from 'react'
import RedShieldIcon from 'assets/svg/shield_times_red.svg'
import type { SVGIconBaseProps } from '../../BaseSVG'
import SVGIcon from '../../SVGIcon'

type Props = {
  className?: string
} & SVGIconBaseProps

const RedShield = forwardRef<typeof SVGIcon, Props>(function RedShield(
  { height = 34, width = 36, className },
  ref,
) {
  return (
    <SVGIcon
      ref={ref}
      height={height}
      width={width}
      svg={RedShieldIcon}
      className={className}
    />
  )
})

export default RedShield
