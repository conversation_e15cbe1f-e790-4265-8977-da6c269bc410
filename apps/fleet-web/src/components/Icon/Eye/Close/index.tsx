import { forwardRef } from 'react'
import CloseEyeIcon from 'assets/svg/eye-close.svg'
import type { SVGIconBaseProps } from '../../BaseSVG'
import SVGIcon from '../../SVGIcon'

type Props = {
  className?: string
} & SVGIconBaseProps

const CloseEye = forwardRef<typeof SVGIcon, Props>(function CloseEye(
  { width = '22', className = '' },
  ref,
) {
  return (
    <SVGIcon
      ref={ref}
      width={width}
      svg={CloseEyeIcon}
      className={className}
    />
  )
})

export default CloseEye
