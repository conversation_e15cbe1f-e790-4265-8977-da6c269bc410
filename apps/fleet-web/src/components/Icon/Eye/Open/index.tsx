import { forwardRef } from 'react'
import OpenEyeIcon from 'assets/svg/eye-open.svg'
import type { SVGIconBaseProps } from '../../BaseSVG'
import SVGIcon from '../../SVGIcon'

type Props = {
  className?: string
} & SVGIconBaseProps

const OpenEye = forwardRef<typeof SVGIcon, Props>(function OpenEye(
  { width = '22', className = '' },
  ref,
) {
  return (
    <SVGIcon
      ref={ref}
      width={width}
      svg={OpenEyeIcon}
      className={className}
    />
  )
})

export default OpenEye
