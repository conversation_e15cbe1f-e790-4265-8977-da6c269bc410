import BaseSVG, { type SVGIconBaseProps } from '../../BaseSVG'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

type Props = {
  svg: string
  className?: string
} & SVGIconBaseProps

const SVGChartIcon = ({
  height,
  width,
  color = '#333333',
  svg,
  className = '',
}: Props) => (
  <BaseSVG
    height={height}
    width={width}
    color={color}
    {...makeSanitizedInnerHtmlProp({ dirtyHtml: svg })}
    className={className}
  />
)

export default SVGChartIcon
