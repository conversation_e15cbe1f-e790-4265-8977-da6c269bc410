import { forwardRef } from 'react'
import BaseSVG, { type SVGIconBaseProps } from '../BaseSVG'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

type Props = {
  svg: string
  className?: string
} & SVGIconBaseProps

const SVGIcon = forwardRef<any, Props>(function SVGIcon(
  { height, width, color, svg, className = '' },
  ref,
) {
  return (
    <BaseSVG
      ref={ref}
      as="div"
      height={height}
      width={width}
      color={color}
      {...makeSanitizedInnerHtmlProp({ dirtyHtml: svg })}
      className={className}
    />
  )
})

export default SVGIcon
