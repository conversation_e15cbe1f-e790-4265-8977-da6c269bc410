import { forwardRef } from 'react'
import styled from 'styled-components'
import Icon from 'src/components/Icon'

type Props = {
  className?: string
}

export default forwardRef<typeof InactiveCameraIcon, Props>(function InactiveCamera(
  { className = '' },
  ref,
) {
  return (
    <InactiveCameraIcon
      icon="video-slash"
      className={className}
      ref={ref}
    />
  )
})

const InactiveCameraIcon = styled(Icon)`
  font-size: 22px;
  color: #ddd;
`
