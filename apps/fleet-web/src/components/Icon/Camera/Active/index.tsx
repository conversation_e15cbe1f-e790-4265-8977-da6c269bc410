import { forwardRef } from 'react'
import styled from 'styled-components'
import Icon from 'src/components/Icon'

type Props = {
  className?: string
}

export default forwardRef<typeof ActiveCameraIcon, Props>(function ActiveCamera(
  { className = '' },
  ref,
) {
  return (
    <ActiveCameraIcon
      icon="video"
      className={className}
      ref={ref}
    />
  )
})

const ActiveCameraIcon = styled(Icon)`
  font-size: 22px;
  color: #6ba238;
`
