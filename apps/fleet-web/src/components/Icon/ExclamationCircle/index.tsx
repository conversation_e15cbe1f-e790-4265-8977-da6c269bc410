import styled from 'styled-components'
import Icon from '..'

type Props = {
  className?: string
}

const ExclamationCircle = ({ className }: Props) => (
  <StyledExclamationCircle
    className={className}
    icon="exclamation-circle"
  />
)

export default ExclamationCircle

const StyledExclamationCircle = styled(Icon)`
  color: ${(props) => props.theme.colors.styleIconColour};
  font-size: 14px;
`
