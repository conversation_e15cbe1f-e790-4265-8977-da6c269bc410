import { forwardRef } from 'react'
import EngineFaultIcon from 'assets/svg/VehiclePopover/engine-faults.svg'
import type { SVGIconBaseProps } from 'src/components/Icon/BaseSVG'
import SVGIcon from 'src/components/Icon/SVGIcon'

type Props = {
  className?: string
} & SVGIconBaseProps

const EngineFaults = forwardRef<typeof SVGIcon, Props>(function EngineFaults(
  { height = 16, className = '', ...rest },
  ref,
) {
  return (
    <SVGIcon
      ref={ref}
      height={height}
      svg={EngineFaultIcon}
      className={className}
      {...rest}
    />
  )
})

export default EngineFaults
