import POI from 'assets/svg/POI.svg'
import type { SVGIconBaseProps } from '../BaseSVG'
import SVGIcon from '../SVGIcon'

type Props = {
  className?: string
} & SVGIconBaseProps

const PlaceMarkerIcon = ({
  height = '45',
  color = '#333333',
  className = '',
}: Props) => (
  <SVGIcon
    height={height}
    color={color}
    svg={POI}
    className={className}
  />
)

export default PlaceMarkerIcon
