import * as React from 'react'
import styled from 'styled-components'
import Icon from '..'

type Props = {
  direction: 'up' | 'down'
} & Omit<React.ComponentProps<typeof Icon>, 'icon'>

const CollapseIcon = ({ direction, ...rest }: Props) => (
  <StyledIcon
    icon={direction === 'up' ? 'chevron-up' : 'chevron-down'}
    {...rest}
  />
)

const StyledIcon = styled(Icon)`
  color: #666;
  cursor: pointer;
`

export default CollapseIcon
