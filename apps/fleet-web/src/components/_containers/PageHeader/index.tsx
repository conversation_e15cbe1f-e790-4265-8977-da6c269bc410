import { Grid, Typography } from '@karoo-ui/core'

type Props = {
  children: React.ReactNode
}

const PageHeader = ({ children }: Props) => (
  <Grid
    container
    direction="row"
    justifyContent="space-between"
    gridTemplateColumns="1fr auto"
    alignItems="center"
    spacing={2}
  >
    {children}
  </Grid>
)

const Title = ({ children }: Props) => (
  <Grid
    item
    xs="auto"
  >
    <Typography variant="h5">{children}</Typography>
  </Grid>
)

const ButtonsContainer = ({ children }: Props) => (
  <Grid
    item
    xs="auto"
    sx={{
      display: 'flex',
      flexWrap: 'wrap',
      gap: 1,
    }}
  >
    {children}
  </Grid>
)

export default Object.assign(PageHeader, {
  Title,
  ButtonsContainer,
})
