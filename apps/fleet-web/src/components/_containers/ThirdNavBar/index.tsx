import type { ComponentProps } from 'react'
import styled from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import Button from 'src/util-components/button-storeless'
import { ctIntl } from 'src/util-components/ctIntl'

/**
 * @deprecated
 *
 * Use PageHeader component that's already using mui instead
 */
const ThirdNavBar = styled.div`
  margin-bottom: ${spacing[4]};
`

/**
 * @deprecated
 *
 * Use PageHeader component that's already using mui instead
 */
const WithContentInBothEdges = styled(ThirdNavBar)`
  display: grid;
  grid-column-gap: ${spacing[4]};
  grid-template-columns: 1fr auto;
  justify-content: space-between;
`

const BaseGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${spacing[2]};
`

/**
 * @deprecated
 *
 * Use PageHeader.ButtonsContainer component that's already using mui instead
 */
const ButtonsGroup = styled(BaseGroup)``

/**
 * @deprecated
 *
 * Use PageHeader.Title component that's already using mui instead
 */
const FiltersGroup = styled(BaseGroup)``

const ClearButton = ({
  tooltipLabel = ctIntl.formatMessage({
    id: 'clearButton.tooltip.clearFilters',
  }),
  ...props
}: Omit<ComponentProps<typeof Button>, 'square' | 'icon'> & {
  tooltipLabel?: ComponentProps<typeof ArrowedTooltip>['label']
}) => (
  <ArrowedTooltip label={tooltipLabel}>
    <Button
      square
      icon={['fas', 'eraser']}
      {...props}
    />
  </ArrowedTooltip>
)

const ContentBothEdgesComponents = Object.assign(WithContentInBothEdges, {
  FiltersGroup,
  ButtonsGroup,
})

export default Object.assign(ThirdNavBar, {
  WithContentInBothEdges: ContentBothEdgesComponents,
  ClearButton,
})
