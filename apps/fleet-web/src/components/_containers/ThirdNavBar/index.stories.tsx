import { useState } from 'react'
import Button from 'src/util-components/button-storeless'
import ThirdNavBar from './index'
import SearchInput from 'src/components/_inputs/SearchInput'
import TableStat from 'src/components/_table/Stat'

export default {
  component: ThirdNavBar,
}

export const Normal = () => {
  const [searchTerm, setSearchTerm] = useState('')

  const handleSearchChange = (e: { currentTarget: { value: string } }) => {
    setSearchTerm(e.currentTarget.value)
  }

  const handleClearButtonClick = () => {
    setSearchTerm('')
  }

  const shouldClearButtonBeAvailable = () => searchTerm !== ''

  return (
    <ThirdNavBar.WithContentInBothEdges>
      <ThirdNavBar.WithContentInBothEdges.FiltersGroup>
        <SearchInput
          onChange={handleSearchChange}
          value={searchTerm}
        />
        <TableStat label="Drivers">420</TableStat>
        {shouldClearButtonBeAvailable() && (
          <ThirdNavBar.ClearButton onClick={handleClearButtonClick} />
        )}
      </ThirdNavBar.WithContentInBothEdges.FiltersGroup>
      <ThirdNavBar.WithContentInBothEdges.ButtonsGroup>
        <Button
          action
          label="Add Driver"
          icon="plus"
        />
      </ThirdNavBar.WithContentInBothEdges.ButtonsGroup>
    </ThirdNavBar.WithContentInBothEdges>
  )
}
