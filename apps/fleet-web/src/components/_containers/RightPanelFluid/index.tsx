import * as React from 'react'
import styled from 'styled-components'

import IconButton from 'src/util-components/icon-button'
import { variables } from 'src/shared/components/styled/global-styles'

type Props = {
  isOpen: boolean
  className?: string
  children: React.ReactNode
  onClosePanel: () => void
}

const RightPanelFluid = ({
  isOpen = false,
  className = '',
  children,
  onClosePanel,
}: Props) => {
  const handleClosePanel = () => {
    onClosePanel()
  }

  return (
    <Container className={`${className} ${isOpen ? 'is-open' : ''}`}>
      <CloseButton
        icon="times"
        tooltipMessage=""
        onClick={handleClosePanel}
      />
      {children}
    </Container>
  )
}

export default RightPanelFluid

const Container = styled.div`
  background-color: #fff;
  height: 100vh;
  position: fixed;
  right: -300px;
  top: 0;
  transition: right 0.4s ${variables.defaultAnimation};
  width: 300px;
  z-index: 1000;
  padding: 20px;

  &.is-open {
    box-shadow: 0 9px 14px 0 rgba(0, 0, 0, 0.5);
    right: 0;
  }
`

const CloseButton = styled(IconButton)`
  position: absolute;
  top: 20px;
  right: 20px;
  margin: 0;
`
