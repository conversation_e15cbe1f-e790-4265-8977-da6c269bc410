import styled, { keyframes } from 'styled-components'
import usePercentageValidation from 'src/hooks/usePercentageValidation'

type Direction = 'clockwise' | 'anticlockwise'

type Props = {
  percentage: number
  className?: string
  direction?: Direction
}

const HalfCircle = ({ percentage, className, direction = 'clockwise' }: Props) => {
  const validatedPercentage = usePercentageValidation(percentage)

  return (
    <Container className={className}>
      <OutsideCircle />
      <InsideCircle />
      <ProgressCircle
        percentage={validatedPercentage}
        direction={direction}
      />
    </Container>
  )
}

export default HalfCircle

const Container = styled.div`
  position: relative;
  height: 65px;
  overflow: hidden;
`

const OutsideCircle = styled.div`
  height: 130px;
  width: 130px;
  border-radius: 50%;
  border: 1px solid #ccc;
  border-bottom: 1px solid transparent;
  border-right: 1px solid transparent;
  transform: rotate(45deg);
`

const InsideCircle = styled(OutsideCircle)`
  position: absolute;
  height: 116px;
  width: 116px;
  top: 7px;
  left: 7px;
`

const ProgressCircle = styled(OutsideCircle)<{
  percentage: number
  direction: Direction
}>`
  position: absolute;
  top: 1px;
  left: 1px;
  height: 128px;
  width: 128px;
  border: 6px solid #6ba238;
  border-top: 6px solid transparent;
  border-left: 6px solid transparent;
  animation: ${({ percentage, direction }) => Rotate(percentage, direction)} 1s forwards;
`

const Rotate = (percentage: number, direction: Direction) => keyframes`
  from {
    transform: rotate(45deg);
  }
  to {
    transform:
      ${`rotate(${
        45 + (direction === 'clockwise' ? percentage : -percentage) * 1.8
      }deg)`};
  }
`
