import * as React from 'react'
import HalfCircle from '.'
import { select, number } from '@storybook/addon-knobs'

export default {
  component: HalfCircle,
}

const directions: Record<string, React.ComponentProps<typeof HalfCircle>['direction']> =
  {
    Clockwise: 'clockwise',
    Anticlockwise: 'anticlockwise',
  }

export const Normal = () => {
  const percentage = number('Percentage', 25, {
    range: true,
    min: 0,
    max: 100,
    step: 1,
  })
  const direction = select('Direction', directions, directions['Clockwise'])

  return (
    <HalfCircle
      percentage={percentage}
      direction={direction}
    />
  )
}
