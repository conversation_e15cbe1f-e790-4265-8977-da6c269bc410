import styled, { keyframes } from 'styled-components'
import usePercentageValidation from 'src/hooks/usePercentageValidation'

const CIRCLE_HEIGHT = 67
const CIRCLE_STROKE_WIDTH = 4
const CIRCLE_RADIUS = CIRCLE_HEIGHT / 2
const CIRCLE_PERIMETER = 2 * Math.PI * (CIRCLE_RADIUS - CIRCLE_STROKE_WIDTH / 2)

type Props = {
  percentage: number
}

const Circle = ({ percentage }: Props) => {
  const validatedPercentage = usePercentageValidation(percentage)

  return (
    <Container viewBox={`0 0 ${CIRCLE_HEIGHT} ${CIRCLE_HEIGHT}`}>
      <BackgroundCircle />
      <ProgressCircle percentage={validatedPercentage} />
    </Container>
  )
}

export default Circle

const Spin = (percentage: number) => keyframes`
  to {
    stroke-dashoffset: ${CIRCLE_PERIMETER - CIRCLE_PERIMETER * (percentage / 100)};
  }
`

const Container = styled.svg`
  width: ${CIRCLE_HEIGHT}px;
  height: ${CIRCLE_HEIGHT}px;
`

const BaseCircle = ({ className = '' }) => (
  <circle
    cx={CIRCLE_RADIUS}
    cy={CIRCLE_RADIUS}
    r={CIRCLE_RADIUS - CIRCLE_STROKE_WIDTH / 2}
    className={className}
  />
)

const BackgroundCircle = styled(BaseCircle)`
  fill: none;
  stroke: #d8d8d8;
  stroke-width: ${CIRCLE_STROKE_WIDTH}px;
`

const ProgressCircle = styled(BackgroundCircle)<{
  percentage: Props['percentage']
}>`
  stroke: #79ab3f;
  transform: rotate(-90deg) translateX(-${CIRCLE_HEIGHT}px);
  animation: ${({ percentage }) => Spin(percentage)} 1s forwards;
  stroke-dasharray: ${CIRCLE_PERIMETER};
`
