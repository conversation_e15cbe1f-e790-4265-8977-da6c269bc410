import * as React from 'react'
import DriverAvatarStatus from '.'
import { select } from '@storybook/addon-knobs'

export default {
  component: DriverAvatarStatus,
}

export const Normal = () => {
  const status = select('Status', statusOptions, statusOptions['Driving'])

  return (
    <DriverAvatarStatus
      avatar={null}
      status={status}
    />
  )
}

const statusOptions: Record<
  string,
  React.ComponentProps<typeof DriverAvatarStatus>['status']
> = {
  Available: 'AVAILABLE',
  Driving: 'DRIVING',
  'Other Work': 'OTHER_WORK',
  Rest: 'REST',
  'Unknown Activity': 'UNKNOWN',
}
