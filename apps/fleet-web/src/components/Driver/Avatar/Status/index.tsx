/* eslint-disable no-nested-ternary */
import * as React from 'react'
import styled, { css } from 'styled-components'

import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import DriverAvatar from '..'
import TachographAvailableIcon from '../../../Icon/TachographDriverActivity/Available'
import TachographDrivingIcon from '../../../Icon/TachographDriverActivity/Driving'
import TachographRestIcon from '../../../Icon/TachographDriverActivity/Rest'
import TachographOtherWorkIcon from '../../../Icon/TachographDriverActivity/OtherWork'
import TachographUnknownIcon from '../../../Icon/TachographDriverActivity/Unknown'
import theme from 'src/components/_themes/tachograph'

const StyledDriverAvatarStatus = styled(DriverAvatar)<Props>`
  ${({ status }) =>
    status &&
    css`
      border: 2px solid ${theme.colors.tachographDriverActivity[status]};
    `}
`

const DriverAvatarStatusContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`

const DriverTachographActivityContainer = styled.div`
  position: absolute;
  bottom: -0.2em;
  right: -0.2em;
`

type Props = {
  avatar: Driver['avatar']
  status: Driver['status']
  size?: React.ComponentProps<typeof TachographAvailableIcon>['size']
  className?: string
}

const DriverAvatarStatus = ({ avatar, status, size = 'small', className }: Props) => (
  <DriverAvatarStatusContainer>
    <StyledDriverAvatarStatus
      className={className}
      status={status}
      avatar={avatar}
    />
    <DriverTachographActivityContainer>
      {status &&
        (status === 'AVAILABLE' ? (
          <TachographAvailableIcon size={size} />
        ) : status === 'DRIVING' ? (
          <TachographDrivingIcon size={size} />
        ) : status === 'REST' ? (
          <TachographRestIcon size={size} />
        ) : status === 'OTHER_WORK' ? (
          <TachographOtherWorkIcon size={size} />
        ) : (
          <TachographUnknownIcon size={size} />
        ))}
    </DriverTachographActivityContainer>
  </DriverAvatarStatusContainer>
)

export default DriverAvatarStatus
