import type { ComponentProps } from 'react'
import styled from 'styled-components'
import DefaultAvatar from 'assets/svg/avatar.svg'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

const StyledDriverAvatar = styled.img`
  border-radius: 50%;
  height: 40px; /* img has border included in its size */
  width: 40px;
`

const StyledDriverAvatarSVG = styled.div`
  border-radius: 50%;
  display: flex;
  align-items: center;
  height: 40px;
  width: 40px;

  & svg {
    height: 100%;
    width: 100%;
  }
`

type Props = { avatar: Driver['avatar'] } & ComponentProps<typeof StyledDriverAvatar>

const DriverAvatar = ({ avatar, style, ...rest }: Props) =>
  avatar ? (
    <StyledDriverAvatar
      src={avatar}
      alt="Driver Avatar"
      {...rest}
      style={style}
    />
  ) : (
    <StyledDriverAvatarSVG
      {...makeSanitizedInnerHtmlProp({ dirtyHtml: DefaultAvatar })}
      style={style}
      {...rest}
    />
  )

export default DriverAvatar
