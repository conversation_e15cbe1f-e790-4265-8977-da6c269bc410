import styled, { css } from 'styled-components'

import DriverAvatar from '..'

type Props = {
  avatar: string | null
  className?: string
  color?: string | null
}

const DriverAvatarWithHalo = ({ avatar, color, className }: Props) => (
  <StyledContainer>
    <StyledDriverAvatarWithHalo
      avatar={avatar}
      className={className}
      color={color}
    />
  </StyledContainer>
)

export default DriverAvatarWithHalo

const StyledContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`

const StyledDriverAvatarWithHalo = styled(DriverAvatar)<Props>`
  ${({ color }) =>
    color &&
    css`
      border: 2px solid ${color};
    `}
`
