import * as React from 'react'
import { select } from '@storybook/addon-knobs'
import DriverAvatar<PERSON>ithHalo from '.'

export default {
  component: DriverAvatarWithHalo,
}

export const Normal = () => {
  const color = select('Color', colorOptions, colorOptions['green'])
  return (
    <DriverAvatarWithHalo
      avatar={null}
      color={color}
    />
  )
}

const colorOptions: Record<
  string,
  React.ComponentProps<typeof DriverAvatarWithHalo>['color']
> = {
  red: '#FF0000',
  green: '#008000',
  blue: '#0000FF',
}
