import FocusedDriverCard from '.'
import styled from 'styled-components'
import { text } from '@storybook/addon-knobs'

export default {
  component: FocusedDriverCard,
}

export const Normal = () => {
  const name = text('Name', 'Alpack Aswag')
  const vehicle = text('Vehicle', '12-QW-34')

  return (
    <Container>
      <FocusedDriverCard
        name={name}
        vehicle={vehicle}
      />
    </Container>
  )
}

const Container = styled.div`
  max-width: 300px;
`
