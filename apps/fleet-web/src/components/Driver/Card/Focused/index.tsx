import DriverCardBase from '../Base'
import FocusedDriverDetails from '../../Details/Focused'
import { styled } from '@karoo-ui/core'

const FocusedDriverCardBase = styled(DriverCardBase)`
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: #f47735;
`

type Props = {
  name: string
  vehicle: string | null
}

const FocusedDriverCard = ({ name, vehicle }: Props) => (
  <FocusedDriverCardBase>
    <FocusedDriverDetails
      name={name}
      vehicle={vehicle}
    />
  </FocusedDriverCardBase>
)

export default FocusedDriverCard
