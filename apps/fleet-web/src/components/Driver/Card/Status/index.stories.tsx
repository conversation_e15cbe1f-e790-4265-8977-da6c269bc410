import * as React from 'react'
import DriverCardStatus from '.'
import styled from 'styled-components'
import { select, text } from '@storybook/addon-knobs'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'

export default {
  component: DriverCardStatus,
}

export const Normal = () => {
  const status = select('Status', statusOptions, statusOptions['Driving'])
  const name = text('Name', 'Alpack Aswag')
  const vehicle = text('Vehicle', '12-QW-34')
  const role = select('Role', roleOptions, roleOptions['Driver'])

  return (
    <Container>
      <DriverCardStatus
        name={name}
        status={status}
        avatar={null}
        role={role}
        vehicle={vehicle}
        isETachoDriver
      />
    </Container>
  )
}

const Container = styled.div`
  max-width: 300px;
`

const statusOptions: Record<
  string,
  React.ComponentProps<typeof DriverCardStatus>['status']
> = {
  Available: 'AVAILABLE',
  Driving: 'DRIVING',
  'Other Work': 'OTHER_WORK',
  Rest: 'REST',
  'Unknown Activity': 'UNKNOWN',
}

const roleOptions: Record<string, Driver['role']> = {
  Driver: 'DRIVER',
  Codriver: 'CODRIVER',
  Null: null,
}
