import styled, { css } from 'styled-components'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import DriverDetailsStatus from '../../Details/Status'
import DriverAvatarStatus from '../../Avatar/Status'
import DriverAvatar from '../../Avatar'
import DriverCardBase from '../Base'
import { DriverInfo } from '../../Info'

const DriverCardStatusAvatarCSS = css`
  height: 50px; /* img has border included in its size */
  width: 50px;

  & svg {
    height: 48px; /* SVG has NO border included in its size */
    width: 48px;
  }
`

const StyledDriverAvatarStatus = styled(DriverAvatarStatus)`
  ${DriverCardStatusAvatarCSS};
`

const StyledDriverAvatar = styled(DriverAvatar)`
  ${DriverCardStatusAvatarCSS};
`

type Props = {
  name: string
  avatar: Driver['avatar']
  status: Driver['status']
  role: Driver['role']
  isETachoDriver: Driver['isETachoDriver']
  vehicle: string | null
}

const DriverCardStatus = ({
  name,
  avatar,
  vehicle,
  role,
  status,
  isETachoDriver,
}: Props) => (
  <DriverCardBase>
    <DriverInfo>
      {status ? (
        <StyledDriverAvatarStatus
          status={status}
          avatar={avatar}
          size="medium"
        />
      ) : (
        <StyledDriverAvatar avatar={avatar} />
      )}
      <DriverDetailsStatus
        vehicle={vehicle}
        name={name}
        role={role}
        isETachoDriver={isETachoDriver}
      />
    </DriverInfo>
  </DriverCardBase>
)

export default DriverCardStatus
