import * as React from 'react'
import Card from 'src/components/Card'
import { styled } from '@karoo-ui/core'

type Props = {
  children: React.ReactNode
  className?: string
}

const DriverCardBase = React.forwardRef<any, Props>(function DriverCardBase(
  { className, children },
  ref,
) {
  return (
    <StyledCard
      ref={ref}
      className={className}
    >
      {children}
    </StyledCard>
  )
})

export default DriverCardBase

const StyledCard = styled(Card)`
  background-color: white;
`
