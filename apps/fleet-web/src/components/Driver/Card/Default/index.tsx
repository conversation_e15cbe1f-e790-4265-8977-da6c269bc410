import { forwardRef } from 'react'
import type {
  Driver,
  TimelinePeriod,
} from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import DriverDetails from '../../Details/Default'
import DriverAvatarStatus from '../../Avatar/Status'
import DriverAvatar from '../../Avatar'
import DriverCardBase from '../Base'
import TimelineBar from '../../../_timeline/Bar'
import { getTimePeriodPct } from 'src/modules/map-view/DriversMapView/Tachograph/utils'
import theme from 'src/components/_themes/tachograph'
import styled from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { DriverInfo } from '../../Info'

type Props = {
  name: string
  avatar: Driver['avatar']
  status: Driver['status']
  role: Driver['role']
  isETachoDriver: Driver['isETachoDriver']
  vehicle: string | null
  activity: Array<TimelinePeriod>
}

const DriverCard = forwardRef<typeof DriverCardBase, Props>(function DriverCard(
  { name, avatar, status, vehicle, role, isETachoDriver, activity },
  ref,
) {
  return (
    <DriverCardBase ref={ref}>
      <DriverInfo>
        {status ? (
          <DriverAvatarStatus
            status={status}
            avatar={avatar}
          />
        ) : (
          <DriverAvatar avatar={avatar} />
        )}
        <DriverDetails
          name={name}
          vehicle={vehicle}
          role={role}
          isETachoDriver={isETachoDriver}
        />
      </DriverInfo>
      <DriverTimeline>
        {activity.map((period) => (
          <TimelineBar.Fragment
            key={`${period.startPct}_${period.endPct}_${period.status}`}
            color={theme.colors.tachographDriverActivity[period.status]}
            pctLeft={period.startPct}
            pctWidth={getTimePeriodPct(period)}
          />
        ))}
      </DriverTimeline>
    </DriverCardBase>
  )
})

export default DriverCard

const DriverTimeline = styled(TimelineBar)`
  margin-top: ${spacing[4]};
`
