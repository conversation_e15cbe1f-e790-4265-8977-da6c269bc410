import { forwardRef } from 'react'
import styled from 'styled-components'
import type {
  Driver,
  TimelinePeriod,
} from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import DriverDetails from '../../Details/Default'
import DriverAvatarStatus from '../../Avatar/Status'
import DriverAvatar from '../../Avatar'
import DriverCardBase from '../Base'
import { DriverInfo } from '../../Info'
import TimelineBar from '../../../_timeline/Bar'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import theme from 'src/components/_themes/tachograph'
import { getTimePeriodPct } from 'src/modules/map-view/DriversMapView/Tachograph/utils'

type Props = {
  name: string
  avatar: Driver['avatar']
  status: Driver['status']
  role: Driver['role']
  isETachoDriver: Driver['isETachoDriver']
  activity: Array<TimelinePeriod>
  vehicle: string | null
  className?: string
}

const DriverCardMinimal = forwardRef<typeof DriverCardBase, Props>(
  function DriverCardMinimal(
    { name, avatar, status, role, isETachoDriver, activity, vehicle, className },
    ref,
  ) {
    return (
      <DriverCardBase
        className={className}
        ref={ref}
      >
        <DriverInfo>
          {status ? (
            <DriverAvatarStatus
              status={status}
              avatar={avatar}
            />
          ) : (
            <DriverAvatar avatar={avatar} />
          )}
          <DriverDetails
            name={name}
            role={role}
            vehicle={vehicle}
            isETachoDriver={isETachoDriver}
          />
        </DriverInfo>
        <DriverTimeline>
          {activity.map((period) => (
            <TimelineBar.Fragment
              key={`${period.startPct}_${period.endPct}_${period.status}`}
              color={theme.colors.tachographDriverActivity[period.status]}
              pctLeft={period.startPct}
              pctWidth={getTimePeriodPct(period)}
            />
          ))}
        </DriverTimeline>
      </DriverCardBase>
    )
  },
)

export default DriverCardMinimal

const DriverTimeline = styled(TimelineBar)`
  margin-top: ${spacing[1]};
`
