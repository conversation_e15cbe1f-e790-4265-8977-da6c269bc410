import DriverIdCardIcon from 'src/components/Icon/DriverIdCard'
import styled from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'

const StyledDriverCardId = styled.div`
  display: flex;
  align-items: center;
  ${(props) => props.theme.typography.fontFamily('robotoCondensed')};
  font-size: 12px;
  color: #666;
`

const StyledDriverIdCardIcon = styled(DriverIdCardIcon)`
  margin-right: ${spacing[2]};
`

type Props = {
  className?: string
  cardId: string
}

const DriverTachographIdCard = ({ className, cardId }: Props) => (
  <StyledDriverCardId className={className}>
    <StyledDriverIdCardIcon />
    {cardId}
  </StyledDriverCardId>
)

export default DriverTachographIdCard
