import { Stack, styled } from '@karoo-ui/core'
import IntlTypography from 'src/util-components/IntlTypography'
import ETachoIcon from './ETachoIcon'

const Container = styled(Stack)`
  align-items: center;
  color: #666;
  flex-direction: row;
`

type Props = {
  className?: string
}

const ETachoDriver = ({ className }: Props) => (
  <Container className={className}>
    <ETachoIcon sx={{ mr: 1 }} />
    <IntlTypography
      msgProps={{ id: 'eTachoDriver' }}
      variant="caption"
      lineHeight="unset"
    />
  </Container>
)

export default ETachoDriver
