import { type ComponentProps, forwardRef } from 'react'
import PhoneAndroidRounded from '@mui/icons-material/PhoneAndroidRounded'

type Props = ComponentProps<typeof PhoneAndroidRounded>

const ETachoIcon = forwardRef<SVGSVGElement, Props>(function ETachoIcon(
  { sx, ...restProps },
  ref,
) {
  return (
    <PhoneAndroidRounded
      sx={{ height: '100%', width: '12px', ...sx }}
      viewBox="5 1 14 22"
      {...restProps}
      ref={ref}
    />
  )
})

export default ETachoIcon
