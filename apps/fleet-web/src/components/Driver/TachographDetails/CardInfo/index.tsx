import styled from 'styled-components'
import { styled as MuiStyled } from '@karoo-ui/core'
import { ctIntl } from 'src/util-components/ctIntl'
import DriverTachographCardId from '../CardId'
import Card from 'src/components/Card'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { connectedCtIntl } from 'src/util-components/connectedCtIntl'
import type { FetchDriverDetails } from 'src/modules/map-view/DriversMapView/Tachograph/api/useDriverDetailsQuery'
import ETachoDriver from '../ETachoDriver'

type Props = Pick<
  FetchDriverDetails.Return['details'],
  | 'cardId'
  | 'isETachoDriver'
  | 'expirationDate'
  | 'expirationDays'
  | 'lastDownloadDate'
  | 'nextDownloadDays'
  | 'legalLimitDate'
>

const DriverTachographCardInfo = ({
  cardId,
  isETachoDriver,
  expirationDate,
  expirationDays,
  lastDownloadDate,
  nextDownloadDays,
  legalLimitDate,
}: Props) => (
  <Container>
    {isETachoDriver ? (
      <StyledETachoDriver />
    ) : (
      <>
        <StyledDriverTachographCardId cardId={cardId} />
        <Section>
          <TitleColumn>
            {ctIntl.formatMessage({
              id: 'map.tachographDrivers.detailsPanel.detailsSection.expirationDate',
            })}
          </TitleColumn>
          <InfoColumn>
            {expirationDate !== null
              ? connectedCtIntl.formatDateWithHourMinute(expirationDate).formatted
              : ctIntl.formatMessage({ id: 'Unknown' })}
            <Label>
              {expirationDays !== null &&
                ctIntl.formatMessage(
                  {
                    id: 'map.tachographDrivers.detailsPanel.detailsSection.expirationDays',
                  },
                  {
                    values: {
                      count: expirationDays,
                    },
                  },
                )}
            </Label>
          </InfoColumn>
        </Section>

        <SectionSeparator />

        <Section>
          <TitleColumn>
            {ctIntl.formatMessage({
              id: 'map.tachographDrivers.detailsPanel.detailsSection.lastDownload',
            })}

            <Label>
              {legalLimitDate !== null &&
                ctIntl.formatMessage(
                  {
                    id: 'map.tachographDrivers.detailsPanel.detailsSection.legalLimitDate',
                  },
                  {
                    values: {
                      date: connectedCtIntl.formatDateWithHourMinute(legalLimitDate)
                        .formattedDate,
                    },
                  },
                )}
            </Label>
          </TitleColumn>
          <InfoColumn>
            {lastDownloadDate !== null
              ? connectedCtIntl.formatDateWithHourMinute(lastDownloadDate).formatted
              : ctIntl.formatMessage({
                  id: 'tachograph.file.neverDownloaded',
                })}
            <Label>
              {nextDownloadDays !== null &&
                ctIntl.formatMessage(
                  {
                    id: 'map.tachographDrivers.detailsPanel.detailsSection.nextDownload',
                  },
                  {
                    values: {
                      count: nextDownloadDays,
                    },
                  },
                )}
            </Label>
          </InfoColumn>
        </Section>
      </>
    )}
  </Container>
)

export default DriverTachographCardInfo

const Container = styled(Card)`
  display: flex;
  flex-direction: column;
`

const StyledDriverTachographCardId = styled(DriverTachographCardId)`
  margin-bottom: ${spacing[3]};
`

const StyledETachoDriver = MuiStyled(ETachoDriver)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  color: '#666',
}))

const Section = styled.div`
  display: flex;
  justify-content: space-between;
  color: #333;
  gap: 4px;
`

const SectionSeparator = styled.hr`
  height: 1px;
  width: 100%;
  margin: 8px 0;
  border: none;
  color: #9b9b9b;
  background-color: #9b9b9b;
  opacity: 0.4;
`

const Column = styled.div`
  display: flex;
  flex-direction: column;
`

const TitleColumn = styled(Column)`
  ${(props) => props.theme.typography.fontFamily('robotoCondensed')};
  font-size: 10px;
  text-transform: uppercase;
`

const InfoColumn = styled(Column)`
  font-size: 14px;
  text-align: right;
`

const Label = styled.span`
  ${(props) => props.theme.typography.fontFamily('robotoCondensed')};
  font-size: 10px;
  color: #999;
  text-transform: none;
`
