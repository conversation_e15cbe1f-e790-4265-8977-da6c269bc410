import { Stack, OverflowTypography, type SxProps } from '@karoo-ui/core'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import { ctIntl } from 'src/util-components/ctIntl'
import { Fragment } from 'react'

type Props = {
  role: Driver['role']
  isETachoDriver: Driver['isETachoDriver']
  sx?: SxProps
}

const DriverDetailsInfo = ({ role, isETachoDriver, sx }: Props) => {
  const driverDetails = []

  if (role === 'CODRIVER') {
    driverDetails.push(
      <OverflowTypography
        typographyProps={{ variant: 'caption', color: 'text.secondary', sx }}
      >
        {ctIntl.formatMessage({ id: 'map.tachographDrivers.coDriver' })}
      </OverflowTypography>,
    )
  }

  if (isETachoDriver) {
    driverDetails.push(
      <OverflowTypography
        typographyProps={{ variant: 'caption', color: 'text.secondary', sx }}
      >
        {ctIntl.formatMessage({ id: 'eTachoDriver' })}
      </OverflowTypography>,
    )
  }

  return (
    <Stack
      gap={0.5}
      alignItems="center"
      flexDirection="row"
      lineHeight="normal"
    >
      {driverDetails.flatMap((item, index) =>
        index === driverDetails.length - 1 ? (
          <span key={index}>{item}</span>
        ) : (
          <Fragment key={index}>
            <span>{item}</span>
            <span>|</span>
          </Fragment>
        ),
      )}
    </Stack>
  )
}

export default DriverDetailsInfo
