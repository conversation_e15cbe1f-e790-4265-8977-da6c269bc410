import FocusedDriverDetails from '.'
import styled from 'styled-components'
import { text } from '@storybook/addon-knobs'

export default {
  component: FocusedDriverDetails,
}

export const Normal = () => {
  const name = text('Name', 'Alpack Aswag')
  const vehicle = text('Vehicle', '12-QW-34')

  return (
    <Container>
      <FocusedDriverDetails
        name={name}
        vehicle={vehicle}
      />
    </Container>
  )
}

const Container = styled.div`
  max-width: max-content;
  background-color: #f47735;
`
