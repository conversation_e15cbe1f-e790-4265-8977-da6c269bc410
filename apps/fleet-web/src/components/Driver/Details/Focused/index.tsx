import styled from 'styled-components'
import DriverVehicle from 'src/components/Driver/Vehicle'
import DriverDetailsBase from '../Base'
import DriverName from 'src/components/Driver/Name'

const FocusedDriverName = styled(DriverName)`
  font-weight: bold;
  color: white;
`

const FocusedVehicleName = styled(DriverVehicle)`
  font-size: 10px;
  color: white;
`

type Props = {
  name: string
  vehicle: string | null
}

const FocusedDriverDetails = ({ name, vehicle }: Props) => (
  <DriverDetailsBase>
    <FocusedDriverName>{name}</FocusedDriverName>
    {vehicle !== null && <FocusedVehicleName>{vehicle}</FocusedVehicleName>}
  </DriverDetailsBase>
)

export default FocusedDriverDetails
