import { styled } from '@karoo-ui/core'
import DriverVehicle from 'src/components/Driver/Vehicle'
import DriverDetailsBase from '../Base'
import DriverName from 'src/components/Driver/Name'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import OverflowableTextTooltip from 'src/components/_popups/Tooltip/OverflowableText'
import DriverDetailsInfo from '../Info'

type Props = {
  name: string
  role: Driver['role']
  isETachoDriver: Driver['isETachoDriver']
  vehicle: string | null
}

const DriverDetails = ({ name, vehicle, role, isETachoDriver }: Props) => (
  <DriverDetailsBase>
    <DriverName>
      <OverflowableTextTooltip placement="top">{name}</OverflowableTextTooltip>
    </DriverName>
    {vehicle !== null && <BoldVehicleName>{vehicle}</BoldVehicleName>}
    <StyledDriverInfo
      role={role}
      isETachoDriver={isETachoDriver}
    />
  </DriverDetailsBase>
)

export default DriverDetails

const BoldVehicleName = styled(DriverVehicle)`
  font-weight: bold;
`

const StyledDriverInfo = styled(DriverDetailsInfo)({
  fontWeight: 'bold',
  fontSize: '10px',
})
