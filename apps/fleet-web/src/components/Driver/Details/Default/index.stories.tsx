import DriverDetails from '.'
import { select, text } from '@storybook/addon-knobs'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'

export default {
  component: DriverDetails,
}

export const Normal = () => {
  const name = text('Name', 'Alpack Aswag')
  const vehicle = text('Vehicle', '12-QW-34')
  const role = select('Role', roleOptions, roleOptions['Driver'])

  return (
    <DriverDetails
      name={name}
      vehicle={vehicle}
      role={role}
      isETachoDriver
    />
  )
}

const roleOptions: Record<string, Driver['role']> = {
  Driver: 'DRIVER',
  Codriver: 'CODRIVER',
  Null: null,
}
