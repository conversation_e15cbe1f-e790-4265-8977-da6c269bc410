import DriverName from '../../Name'
import DriverDetailsBase from '../Base'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import OverflowableTextTooltip from 'src/components/_popups/Tooltip/OverflowableText'
import styled from 'styled-components'
import { ctIntl } from 'src/util-components/ctIntl'
import DriverVehicle from '../../Vehicle'

type Props = {
  name: string
  role: Driver['role']
}

const DriverDetailsMinimal = ({ name, role }: Props) => (
  <DriverDetailsBase>
    <DriverName>
      <OverflowableTextTooltip placement="top">{name}</OverflowableTextTooltip>
    </DriverName>
    {role === 'CODRIVER' && (
      <DriverRole>
        {ctIntl.formatMessage({ id: 'map.tachographDrivers.coDriver' })}
      </DriverRole>
    )}
  </DriverDetailsBase>
)

export default DriverDetailsMinimal

const DriverRole = styled(DriverVehicle)`
  font-size: 14px;
  font-weight: normal;
  opacity: 0.8;
`
