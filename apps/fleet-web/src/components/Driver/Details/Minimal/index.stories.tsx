import DriverDetailsMinimal from '.'
import { select, text } from '@storybook/addon-knobs'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'

export default {
  component: DriverDetailsMinimal,
}

export const Normal = () => {
  const name = text('Name', 'Alpack Aswag')
  const role = select('Role', roleOptions, roleOptions['Driver'])

  return (
    <DriverDetailsMinimal
      name={name}
      role={role}
    />
  )
}

const roleOptions: Record<string, Driver['role']> = {
  Driver: 'DRIVER',
  Codriver: 'CODRIVER',
  Null: null,
}
