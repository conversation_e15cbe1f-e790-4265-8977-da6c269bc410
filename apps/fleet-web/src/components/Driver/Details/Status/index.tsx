import styled from 'styled-components'
import DriverName from '../../Name'
import DriverVehicle from '../../Vehicle'
import DriverDetailsBase from '../Base'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import OverflowableTextTooltip from 'src/components/_popups/Tooltip/OverflowableText'
import DriverDetailsInfo from '../Info'

type Props = {
  name: string
  role: Driver['role']
  isETachoDriver: Driver['isETachoDriver']
  vehicle: string | null
}

const DriverDetailsStatus = ({ name, vehicle, role, isETachoDriver }: Props) => (
  <DriverDetailsBase>
    <StatusDriverName>
      <OverflowableTextTooltip>{name}</OverflowableTextTooltip>
    </StatusDriverName>
    {vehicle !== null && <StatusDriverVehicle>{vehicle}</StatusDriverVehicle>}
    <StatusDriverInfo
      role={role}
      isETachoDriver={isETachoDriver}
    />
  </DriverDetailsBase>
)

export default DriverDetailsStatus

const StatusDriverName = styled(DriverName)`
  font-size: 18px;
  font-weight: bold;
`

const StatusDriverVehicle = styled(DriverVehicle)`
  font-size: 14px;
  font-weight: normal;
  opacity: 0.8;
`

const StatusDriverInfo = styled(DriverDetailsInfo)({
  fontSize: '14px',
  opacity: '0.8',
})
