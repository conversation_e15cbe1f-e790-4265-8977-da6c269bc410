import DriverDetailsStatus from '.'
import styled from 'styled-components'
import { select, text } from '@storybook/addon-knobs'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'

export default {
  component: DriverDetailsStatus,
}

export const Normal = () => {
  const name = text('Name', 'Alpack Aswag')
  const vehicle = text('Vehicle', '12-QW-34')
  const role = select('Role', roleOptions, roleOptions['Driver'])

  return (
    <Container>
      <DriverDetailsStatus
        name={name}
        role={role}
        vehicle={vehicle}
        isETachoDriver
      />
    </Container>
  )
}

const Container = styled.div`
  max-width: 300px;
`

const roleOptions: Record<string, Driver['role']> = {
  Driver: 'DRIVER',
  Codriver: 'CODRIVER',
  Null: null,
}
