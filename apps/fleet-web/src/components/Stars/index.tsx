import styled from 'styled-components'
import Star from '../Icon/Star'
import ActiveStar from '../Icon/Star/Active'

const StarsContainer = styled.div`
  display: flex;

  > :not(:last-child) {
    margin-right: 2px;
  }
`

type Props = {
  rating?: number
  maxRating?: number
  className?: string
}

const Stars = ({ rating = 0, maxRating = 3, className }: Props) => {
  const stars = []

  for (let i = 1; i <= maxRating; i++) {
    const isDisabled = rating < i

    stars.push(
      isDisabled ? (
        <Star key={`star_rating_${i}${rating}`} />
      ) : (
        <ActiveStar key={`star_rating_${i}${rating}`} />
      ),
    )
  }

  return <StarsContainer className={className}>{stars}</StarsContainer>
}

export default Stars
