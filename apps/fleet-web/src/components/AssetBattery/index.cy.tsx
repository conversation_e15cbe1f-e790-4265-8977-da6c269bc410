import { mount } from 'cypress/react'
import AssetBattery from './index'
import { colorToRgb } from 'src/util-functions/colors-utils'

describe('AssetBattery', () => {
  it('renders with strong orange when percentage is below 25%', () => {
    mount(
      <AssetBattery percentage={23}>
        {({ color }) => <p style={{ color }}></p>}
      </AssetBattery>,
    )
    cy.get('p').should('have.css', 'color', colorToRgb('#ce5239'))
  })

  it('renders with strong green when percentage is above 75%', () => {
    mount(
      <AssetBattery percentage={76}>
        {({ color }) => <p style={{ color }}></p>}
      </AssetBattery>,
    )
    cy.get('p').should('have.css', 'color', colorToRgb('#2dab33'))
  })
})
