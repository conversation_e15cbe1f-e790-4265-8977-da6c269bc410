import type { ReactNode } from 'react'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  LoadingButton,
  Stack,
  type DialogActionsProps,
  type DialogContentProps,
  type DialogProps,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import * as R from 'remeda'
import type { Except, SetRequired } from 'type-fest'

import { ctIntl } from 'src/util-components/ctIntl'

export type BaseModalProps = {
  onClose?: () => void
  onCancel?: () => void
  onConfirm?: () => void
  title?: string
  children: ReactNode
  cancelButtonLabel?: string
  confirmButtonLabel?: string
  confirmButtonVariant?: 'outlined' | 'contained'
  hasFooterButtons?: boolean
  hasCancelButton?: boolean
  cancelButtonDataTestId?: string
  confirmButtonDataTestId?: string
  isLoading?: boolean
} & DialogProps

function BaseModal({
  onClose = R.noop,
  onCancel = onClose,
  onConfirm = R.noop,
  title,
  children,
  cancelButtonLabel,
  confirmButton<PERSON>abel,
  confirmButtonVariant,
  hasFooterButtons = true,
  hasCancelButton = true,
  cancelButtonDataTestId,
  confirmButtonDataTestId,
  isLoading = false,
  ...props
}: BaseModalProps) {
  return (
    <DialogLocal
      onClose={onClose}
      {...props}
    >
      {title && (
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          px={3}
          pt={3}
        >
          <DialogTitle
            sx={{
              padding: 0,
            }}
          >
            {ctIntl.formatMessage({ id: title })}
          </DialogTitle>

          <IconButton
            onClick={onClose}
            color="secondary"
            size="small"
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </Stack>
      )}

      <DialogContentLocal>{children}</DialogContentLocal>

      {hasFooterButtons && (
        <DialogActionsLocal>
          {hasCancelButton && (
            <CancelButton
              dataTestId={cancelButtonDataTestId}
              onClick={onCancel}
              label={
                cancelButtonLabel
                  ? ctIntl.formatMessage({ id: cancelButtonLabel })
                  : null
              }
            />
          )}
          <ConfirmButton
            dataTestId={confirmButtonDataTestId}
            onClick={onConfirm}
            loading={isLoading}
            variant={confirmButtonVariant}
            label={
              confirmButtonLabel
                ? ctIntl.formatMessage({ id: confirmButtonLabel })
                : null
            }
          />
        </DialogActionsLocal>
      )}
    </DialogLocal>
  )
}

const Header = ({
  title,
  onCloseIconClick,
}: {
  title: string
  onCloseIconClick: () => void
}) => (
  <Stack
    direction="row"
    justifyContent="space-between"
    alignItems="center"
    px={3}
    pt={3}
  >
    <DialogTitle
      sx={{
        padding: 0,
      }}
    >
      {ctIntl.formatMessage({ id: title })}
    </DialogTitle>

    <IconButton
      onClick={onCloseIconClick}
      color="secondary"
      size="small"
    >
      <CloseIcon fontSize="small" />
    </IconButton>
  </Stack>
)

const DialogActionsLocal = (props: Except<DialogActionsProps, 'sx'>) => (
  <DialogActions
    sx={{
      borderTop: '1px solid',
      borderColor: 'divider',
      justifyContent: 'space-between',
      py: 2,
      px: 3,
    }}
    {...props}
  />
)

const DialogContentLocal = ({ sx = [], ...props }: DialogContentProps) => (
  <DialogContent
    sx={[
      {
        paddingX: 3,
      },
      ...(R.isArray(sx) ? sx : [sx]),
    ]}
    {...props}
  />
)

const DialogLocal = ({ PaperProps, ...props }: SetRequired<DialogProps, 'onClose'>) => {
  const paperPropsSx = PaperProps?.sx ?? []
  return (
    <Dialog
      maxWidth="md"
      PaperProps={{
        ...PaperProps,
        sx: [
          { minWidth: 500 },
          ...(R.isArray(paperPropsSx) ? paperPropsSx : [paperPropsSx]),
        ],
      }}
      {...props}
    />
  )
}

const CancelButton = ({
  dataTestId = 'BaseModal-cancel-button',
  onClick,
  label,
}: {
  dataTestId?: string
  onClick: () => void
  label?: string | null
}) => (
  <Button
    data-testid={dataTestId}
    onClick={onClick}
    color="secondary"
    variant="outlined"
    size="small"
  >
    {label ? label : ctIntl.formatMessage({ id: 'Cancel' })}
  </Button>
)

const ConfirmButton = ({
  dataTestId = 'BaseModal-confirm-button',
  onClick,
  label,
  variant = 'contained',
  loading,
}: {
  dataTestId?: string
  onClick: () => void
  label?: string | null
  variant?: 'outlined' | 'contained'
  loading: boolean
}) => (
  <LoadingButton
    data-testid={dataTestId}
    onClick={onClick}
    color="primary"
    variant={variant}
    size="small"
    loading={loading}
  >
    {label ? label : ctIntl.formatMessage({ id: 'Confirm' })}
  </LoadingButton>
)

export default Object.assign(BaseModal, {
  Header,
  DialogActions: DialogActionsLocal,
  DialogContent: DialogContentLocal,
  ActionCancelButton: CancelButton,
  ActionConfirmButton: ConfirmButton,
  Dialog: DialogLocal,
})
