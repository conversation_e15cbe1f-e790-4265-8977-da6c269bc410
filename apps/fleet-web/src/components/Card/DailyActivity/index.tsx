import type { ComponentProps } from 'react'
import styled, { css } from 'styled-components'

import TimelineBar from '../../_timeline/Bar'
import { variables } from 'src/shared/components/styled/global-styles'
import { ctIntl } from 'src/util-components/ctIntl'
import TachographDrivingIcon from 'src/components/Icon/TachographDriverActivity/Driving'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'

type Props = {
  isActive?: boolean
  weekDay: string
  shortMonthDay?: string
  drivingTime: string
  onClick?: React.ReactHTMLElement<HTMLDivElement>['props']['onClick']
  children: ComponentProps<typeof TimelineBar>['children']
  className?: string
}

export default function DailyActivityCard({
  isActive,
  weekDay,
  shortMonthDay,
  drivingTime,
  onClick,
  className,
  children,
}: Props) {
  return (
    <Card
      className={className}
      isActive={isActive}
      onClick={onClick}
    >
      <DayInfo>
        <WeekDay>{ctIntl.formatMessage({ id: weekDay })}</WeekDay>
        {shortMonthDay && <ShortMonthDay>{shortMonthDay}</ShortMonthDay>}
        <ArrowedTooltip
          label={ctIntl.formatMessage({
            id: 'map.tachographDrivers.driverActivityStatus.time.driving',
          })}
          placement="top"
        >
          <DrivingTime>
            <TachographDrivingIcon size="extraSmall" />
            <Time>{drivingTime}</Time>
          </DrivingTime>
        </ArrowedTooltip>
      </DayInfo>
      <StyledTimelineBar>{children}</StyledTimelineBar>
    </Card>
  )
}

const Card = styled.div<{ isActive?: boolean }>`
  display: flex;
  flex-direction: column;
  height: 78px;
  padding: 18px 10px;
  box-shadow: 0 1px 0 0 ${variables.gray30};
  ${(props) =>
    props.isActive &&
    css`
      border: 1px solid ${variables.orange};
    `}
`

const DayInfo = styled.div`
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 18px;
`

const WeekDay = styled.span`
  font-size: 16px;
  color: #333;
  ${(props) => props.theme.typography.mixins.robotoCondensedBold}
`

const ShortMonthDay = styled.span`
  margin: auto;
  right: 0;
  left: 0;
  position: absolute;
  width: max-content;
  font-size: 16px;
  color: #333;
  ${(props) => props.theme.typography.mixins.robotoCondensedMedium}
`

const DrivingTime = styled.div`
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  ${(props) => props.theme.typography.mixins.robotoCondensedBold}
`

const Time = styled.span`
  margin-left: ${spacing[1]};
`

const StyledTimelineBar = styled(TimelineBar)`
  margin-top: 4px;
`
