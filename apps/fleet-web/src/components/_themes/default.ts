import type { VehicleStatus } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import type { UserTaskStatus } from 'src/modules/tasks/map/left-panel/utils/types'
import { variables } from 'src/shared/components/styled/global-styles'

type Theme = {
  colors: {
    vehicleStatus: Record<VehicleStatus, string>
    userTaskStatus: Record<UserTaskStatus, string>
  }
}

const theme: Theme = {
  colors: {
    vehicleStatus: {
      DRIVING: variables.vehicleStatus.driving,
      SPEEDING: variables.violation,
      IDLING: variables.vehicleStatus.idling,
      IGNITION_OFF: variables.vehicleStatus.ignitionOff,
      NO_SIGNAL: variables.vehicleStatus.noSignal,
      STATIONARY: variables.vehicleStatus.driving,
      MAINTENANCE: variables.vehicleStatus.maintenance,
    },
    userTaskStatus: {
      COMPLETED: '#B5D55E',
      INPROGRESS: '#5099CE',
      OTHERS: '#EEEEEE',
    },
  },
}

export default theme
