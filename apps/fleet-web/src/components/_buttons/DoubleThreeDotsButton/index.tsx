import styled, { type StyledComponentPropsWithRef } from 'styled-components'
import MoreVerticalIcon from 'src/components/Icon/MoreIcon/MoreVerticalIcon'

type Props = StyledComponentPropsWithRef<typeof StyledButton>

export default function DoubleThreeDotsButton(props: Props) {
  return (
    <StyledButton {...props}>
      <MoreVerticalIcon />
      <MoreVerticalIcon />
    </StyledButton>
  )
}

const StyledButton = styled.button`
  display: flex;
  flex-wrap: nowrap;
  cursor: pointer;
  border: none;
  outline: none;
  background-color: unset;
  padding: 0 10px 0 0;

  svg {
    font-size: 16px;
  }

  &:hover {
    & > svg {
      color: #333;
      transition: all 0.3s ease-in-out;
    }
  }
`
