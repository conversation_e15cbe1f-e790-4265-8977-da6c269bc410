import * as React from 'react'
import styled from 'styled-components'
import Icon from 'src/components/Icon'
import AlertsBadge from 'src/util-components/alerts-badge'
import type { IconProp } from '@fortawesome/fontawesome-svg-core'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

type Props = React.ButtonHTMLAttributes<HTMLDivElement> & {
  active?: boolean
  alertsCount?: number
  color?: string
  backgroundColor?: string
  icon?: IconProp
  iconHtml?: React.ReactNode
  iconSvg?: string
  style?: React.CSSProperties
  activeColor?: string
  id?: React.HTMLAttributes<HTMLDivElement>['id']
  tooltipProps: Omit<React.ComponentProps<typeof ArrowedTooltip>, 'children'>
}

const IconButtonContainer = styled.div.attrs((props) => ({
  style: props.style,
}))<Omit<Props, 'tooltipProps'>>`
  position: relative;
  align-items: center;
  border: ${(props) => (props.backgroundColor ? 'none' : '1px solid #ccc')};
  border-radius: 8px;
  border-color: ${(props) => props.disabled && '#ccc'};
  box-sizing: border-box;
  color: ${(props) =>
    // eslint-disable-next-line no-nested-ternary
    props.active ? props.activeColor : props.disabled ? '#ccc' : '#666'};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  display: flex;
  flex-direction: column;
  font-size: 18px;
  height: 38px;
  justify-content: center;
  line-height: 38px;
  margin: 0 5px;
  text-align: center;
  width: 38px;
  background-color: ${(props) => props.backgroundColor};
  ${(props) => props.disabled && 'pointer-events: none'}

  &:active {
    background-color: ${(props) => !props.disabled && props.backgroundColor};
    border: ${(props) => !props.disabled && 'none'};
    color: ${(props) => !props.disabled && props.activeColor};
  }

  svg {
    pointer-events: none;
    width: 1.25em;
  }
`
/** This button is meant for use cases where icon is required within a call to action.
 */
function IconButton({
  alertsCount = 0,
  color,
  id,
  icon,
  iconHtml = null,
  iconSvg,
  tooltipProps,
  onClick,
  active,
  style,
  activeColor,
  backgroundColor,
  ...props
}: Props) {
  return (
    <ArrowedTooltip
      label={tooltipProps.label}
      placement={tooltipProps.placement}
    >
      <IconButtonContainer
        {...props}
        id={id}
        style={style}
        backgroundColor={backgroundColor}
        onClick={props.disabled ? undefined : onClick}
      >
        {iconSvg && (
          <svg
            {...makeSanitizedInnerHtmlProp({ dirtyHtml: iconSvg })}
            color={active ? activeColor : color}
            style={{ height: '50%' }}
          />
        )}
        {icon && (
          <Icon
            icon={icon}
            color={active ? activeColor : color}
          />
        )}
        {iconHtml}
        <AlertsBadge count={alertsCount} />
      </IconButtonContainer>
    </ArrowedTooltip>
  )
}

export default IconButton
