import type { ComponentProps } from 'react'
import IconButton from './index'
import type { IconProp } from '@fortawesome/fontawesome-svg-core'
import type ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'

export default {
  component: IconButton,
}

type Data = {
  id: number
  icon: IconProp
  handleClick: () => void
  alertsCount: number
  active: boolean
  disabled: boolean
  tooltipProps: Omit<ComponentProps<typeof ArrowedTooltip>, 'children'>
}

export const Normal = () => {
  const clickFn = (iconName: string) => alert(`${iconName} Button Clicked`)
  const data: Array<Data> = [
    {
      id: 0,
      icon: 'circle',
      handleClick: () => clickFn('Circle'),
      alertsCount: 0,
      active: false,
      disabled: false,
      tooltipProps: {
        label: 'Record',
        placement: 'right',
      },
    },
    {
      id: 1,
      icon: 'camera',
      handleClick: () => clickFn('Camera'),
      alertsCount: 0,
      active: true,
      disabled: false,
      tooltipProps: {
        label: 'Capture',
        placement: 'bottom',
      },
    },
    {
      id: 2,
      icon: 'trash',
      handleClick: () => clickFn('Trash'),
      alertsCount: 0,
      active: false,
      disabled: true,
      tooltipProps: {
        label: 'Delete',
        placement: 'left',
      },
    },
    {
      id: 3,
      icon: 'road',
      handleClick: () => clickFn('Road'),
      alertsCount: 12,
      active: false,
      disabled: false,
      tooltipProps: {
        label: 'Road Alerts',
        placement: 'left',
      },
    },
  ]

  return (
    <div style={{ display: 'flex' }}>
      {data.map((btn) => (
        <IconButton
          key={btn.id}
          alertsCount={btn.alertsCount}
          icon={btn.icon}
          onClick={btn.handleClick}
          active={btn.active}
          disabled={btn.disabled}
          tooltipProps={btn.tooltipProps}
        />
      ))}
    </div>
  )
}
