import styled, { type StyledComponentPropsWithRef } from 'styled-components'
import MoreVerticalIcon from 'src/components/Icon/MoreIcon/MoreVerticalIcon'

type Props = StyledComponentPropsWithRef<typeof StyledButton>

export default function MoreVerticalIconButton(props: Props) {
  return (
    <StyledButton {...props}>
      <MoreVerticalIcon />
    </StyledButton>
  )
}

const StyledButton = styled.button`
  cursor: pointer;
  border: none;
  outline: none;
  background-color: unset;
  padding: 0 10px;

  svg {
    font-size: 16px;
  }

  &:hover {
    & > svg {
      color: #333;
      transition: all 0.3s ease-in-out;
    }
  }
`
