import type { ComponentProps } from 'react'
import { DatePicker } from 'cartrack-ui-kit'
import styled, { css } from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'

const rangeElementCss = css`
  min-width: 150px;
`

const rangeElementCssLong = css`
  min-width: 250px;
`

const DateRangePicker = styled(
  ({
    placeholder = { from: 'Start Date', to: 'End Date' },
    className,
    ...props
  }: Omit<ComponentProps<typeof DatePicker>, 'isRange'>) => (
    <DatePicker
      {...props}
      disableMultipleInput={props.renderSingleInput}
      placeholder={placeholder}
      isRange
      extraClassNames={{
        containerClassNames: className,
        fromElement: 'range-from',
        toElement: 'range-to',
      }}
    />
  ),
)`
  & .range-from {
    margin-right: ${spacing[2]};
    ${(props) => (props.renderSingleInput ? rangeElementCssLong : rangeElementCss)};
  }

  & .range-to {
    padding-left: ${spacing[0]};
    ${rangeElementCss};
  }
`

export default DateRangePicker
