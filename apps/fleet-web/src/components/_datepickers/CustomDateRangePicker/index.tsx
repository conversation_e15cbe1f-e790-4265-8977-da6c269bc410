import type { ComponentProps } from 'react'
import CustomDatePicker from 'src/util-components/custom-date-picker'
import styled, { css } from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'

const rangeElementCss = css`
  min-width: 150px;
`

const rangeElementCssLong = css`
  min-width: 250px;
`

const CustomDateRangePicker = styled(
  ({
    placeholder = { from: 'Start Date', to: 'End Date' },
    className,
    fromExtraClassName = '',
    ...props
  }: Omit<ComponentProps<typeof CustomDatePicker>, 'isRange'>) => (
    <CustomDatePicker
      {...props}
      disableMultipleInput={props.renderSingleInput}
      placeholder={placeholder}
      isRange
      extraClassNames={{
        containerClassNames: className,
        fromElement: `range-from ${fromExtraClassName}`,
        toElement: 'range-to',
      }}
    />
  ),
)`
  & .range-from {
    ${(props) => (props.renderSingleInput ? rangeElementCssLong : rangeElementCss)};
  }

  & .range-to {
    padding-left: ${spacing[0]};
    ${rangeElementCss};
  }

  & .extendedInput {
    flex-basis: unset;
    max-width: unset;
  }
`

export default CustomDateRangePicker
