import type { MouseEvent } from 'react'
import styled from 'styled-components'

type Props = {
  onClickIncrement: (event: MouseEvent, target: string) => void
  onClickDecrement: (event: MouseEvent, target: string) => void
  time: number | string
  id: string
}

function HourRange({ onClickIncrement, onClickDecrement, time, id }: Props) {
  const handleTabClickIncrement = (event: MouseEvent<HTMLDivElement>) => {
    const target = event.target as HTMLDivElement
    onClickIncrement(event, target.id)
  }

  const handleTabClickDecrement = (event: MouseEvent<HTMLDivElement>) => {
    const target = event.target as HTMLDivElement
    onClickDecrement(event, target.id)
  }

  return (
    <Container>
      <ButtonContainer
        id={id}
        onClick={handleTabClickDecrement}
      >
        -
      </ButtonContainer>
      <TimeContainer> {time} </TimeContainer>
      <ButtonContainer
        id={id}
        onClick={handleTabClickIncrement}
      >
        +
      </ButtonContainer>
    </Container>
  )
}

export default HourRange

const Container = styled.div`
  display: flex;
  flex-direction: row;
  border: 1px solid #ccc;
  width: fit-content;
  justify-content: center;
  align-items: center;
`

const ButtonContainer = styled((props) => <button {...props} />)`
  border: none;
  font-size: 15px;
  font-weight: 700;
  color: #666666;
  width: 28px;
  height: 24px;
  background: white;
  font-family: 'FontAwesome', sans-serif;
`

const TimeContainer = styled.div`
  display: flex;
  border-left: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
  width: 64px;
  align-items: center;
  justify-content: center;
  font-family: 'Roboto', sans-serif;
  font-weight: bold;
  color: #666666;
  background: white;
  font-size: 12px;
  height: 24px;
`
