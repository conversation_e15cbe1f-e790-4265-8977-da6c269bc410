import { useState } from 'react'
import HourRange from './index'

export default {
  component: HourRange,
}

export const Normal = () => {
  const leftPad = (value: string, targetLength = 2) => {
    let output = value
    while (output.length < targetLength) {
      output = '0' + output
    }
    return output
  }

  const initialHourState = {
    hours: '08',
    minutes: '00',
    amOrPm: 'AM',
  }
  const [hourState, setHour] = useState(initialHourState)

  const handleOnClickIncrement = () => {
    const newTime = Number(hourState.hours) + 1

    if (Number(hourState.hours) >= 11 && Number(hourState.hours) <= 23) {
      setHour({
        ...hourState,
        hours: leftPad(newTime.toString()),
        amOrPm: 'PM',
      })
    } else {
      setHour({
        ...hourState,
        hours: leftPad(newTime.toString()),
        amOrPm: 'AM',
      })
    }

    if (Number(hourState.hours) >= 23) {
      setHour({ ...hourState, hours: '00', amOrPm: 'AM' })
    }
  }

  const handleOnClickDecrement = () => {
    const newTime = Number(hourState.hours) - 1
    if (Number(hourState.hours) > 12 && Number(hourState.hours) <= 23) {
      setHour({
        ...hourState,
        hours: leftPad(newTime.toString()),
        amOrPm: 'PM',
      })
    } else {
      setHour({
        ...hourState,
        hours: leftPad(newTime.toString()),
        amOrPm: 'AM',
      })
    }

    if (Number(hourState.hours) <= 1) {
      setHour({ ...hourState, hours: '00', amOrPm: 'AM' })
    }

    if (Number(hourState.hours) <= 0) {
      setHour({ ...hourState, hours: '23', amOrPm: 'PM' })
    }
  }

  return (
    <>
      <HourRange
        id={''}
        onClickIncrement={handleOnClickIncrement}
        onClickDecrement={handleOnClickDecrement}
        time={hourState.hours + ':' + hourState.minutes + hourState.amOrPm}
      />
    </>
  )
}
