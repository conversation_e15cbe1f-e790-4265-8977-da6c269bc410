import { Fragment, useState, useMemo } from 'react'
import * as React from 'react'
import L from 'leaflet'
import { Marker } from 'react-leaflet'
import MarkerClusterGroup from 'react-leaflet-markercluster'
import ReactDOMServer from 'react-dom/server'
import type { FixMeAny } from 'src/types'
import { generateSvgIcon } from 'cartrack-utils'

// RMM: Hacky solution to stopping events after initialization
let initFetch = false

type ClusterMarker = {
  position: L.LatLng
  markers: Array<FixMeAny>
}

type Props = {
  children?: React.ReactNode
  hoveredItem?: FixMeAny
  statusColors: Record<string, string>
}

function PieCluster({ children, hoveredItem, statusColors }: Props) {
  const [clusterMarkers, setClusterMarkers] = useState<Array<ClusterMarker>>([])

  const customClusterIcon = (cluster: L.MarkerCluster) =>
    L.divIcon({
      html:
        '<div class="OpenLayerPieCluster">' +
        ReactDOMServer.renderToString(generateSvgIcon(cluster, statusColors)) +
        '</div>',
      className: 'OpenLayerPieCluster',
      iconSize: L.point(60, 60, true),
    })

  const fetchLayerClusters = (layers: Array<L.MarkerCluster>) => {
    const markers: Array<ClusterMarker> = []
    layers.forEach((layer) => {
      if (layer.getChildCount && layer.getChildCount() > 1) {
        markers.push({
          position: layer.getLatLng(),
          markers: layer.getAllChildMarkers().map((m) => (m.options as FixMeAny).id),
        })
      }
    })
    setClusterMarkers(markers)
  }

  const initLayers = (instance: FixMeAny) => {
    if (instance && instance.leafletElement) {
      const leafletElement = instance.leafletElement
      const layers = leafletElement._featureGroup.getLayers()
      if (!initFetch && layers.length > 0) {
        initFetch = true
        fetchLayerClusters(layers)
      }
    }
  }

  const handleAnimationChange = (event: L.LeafletEvent) => {
    fetchLayerClusters(event.target._featureGroup.getLayers())
  }

  const hoveredCluster = useMemo(
    () => clusterMarkers.find((c) => c.markers.includes(hoveredItem)),
    [clusterMarkers, hoveredItem],
  )

  return (
    <Fragment>
      {hoveredItem && hoveredCluster && (
        <Marker
          position={hoveredCluster.position}
          icon={L.divIcon({
            html: ReactDOMServer.renderToString(
              <div className="OpenLayerPieCluster-glowing" />,
            ),
            className: 'OpenLayerPieCluster',
          })}
        ></Marker>
      )}
      <MarkerClusterGroup
        showCoverageOnHover={false}
        iconCreateFunction={(cluster) => customClusterIcon(cluster)}
        disableClusteringAtZoom={16}
        animate
        zoomToBoundsOnClick
        removeOutsideVisibleBounds
        ref={initLayers}
        onAnimationEnd={handleAnimationChange}
      >
        {children}
      </MarkerClusterGroup>
    </Fragment>
  )
}
export default PieCluster
