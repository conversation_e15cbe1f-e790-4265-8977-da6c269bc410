// TODO: workaround because react-leaflet-markercluster doens't have types for react-leaflet v3
declare module 'react-leaflet-markercluster' {
  import { Component } from 'react'
  import type * as Leaflet from 'leaflet'

  export type MarkerClusterGroupProps = Leaflet.MarkerClusterGroupOptions & {
    onAnimationEnd?: FixMeAny
    // ----- Fix compatibility with react 18 types https://github.com/DefinitelyTyped/DefinitelyTyped/pull/56210
    children?: React.ReactNode
  }
  export default class MarkerClusterGroup extends Component<MarkerClusterGroupProps> {}
}
