import { Fragment, useCallback, useEffect } from 'react'
import * as React from 'react'
import ReactDOMServer from 'react-dom/server'
import L from 'leaflet'
import MarkerClusterGroup, {
  type MarkerClusterGroupProps as ClusterProps,
} from 'react-leaflet-markercluster'
import type { FixMeAny } from 'src/types'

import { MarkerWithTooltip } from '../../Marker'

const iconAnchor: L.PointTuple = [17, 46]

type MarkerClusterGroupProps = {
  options: L.MarkerOptions & ClusterProps
  refreshClusters: () => void
}

let markerClusterGroup: MarkerClusterGroupProps | null = null

type Props = {
  children?: React.ReactNode
  icon: string
  tooltipLabel?: string
  showAllTooltips?: boolean
}

function BadgeCluster({
  children,
  icon,
  tooltipLabel,
  showAllTooltips = false,
}: Props) {
  const customClusterIcon = useCallback(
    (cluster: L.MarkerCluster) =>
      L.divIcon({
        html: ReactDOMServer.renderToString(
          <MarkerWithTooltip
            icon={icon}
            tooltipText={`${cluster.getChildCount()} ${tooltipLabel}`}
            triggerOnHover={!showAllTooltips}
          >
            <span className="BadgeCluster-number">{cluster.getChildCount()}</span>
          </MarkerWithTooltip>,
        ),
        className: 'BadgeCluster',
        iconAnchor: iconAnchor,
      }),
    [icon, showAllTooltips, tooltipLabel],
  )

  useEffect(() => {
    if (markerClusterGroup) {
      markerClusterGroup.options.iconCreateFunction = customClusterIcon
      markerClusterGroup.refreshClusters()
    }
  }, [showAllTooltips, customClusterIcon])

  return (
    <Fragment>
      <MarkerClusterGroup
        showCoverageOnHover={false}
        iconCreateFunction={customClusterIcon}
        disableClusteringAtZoom={16}
        animate
        zoomToBoundsOnClick
        removeOutsideVisibleBounds
        ref={(clusterGroup: FixMeAny) => {
          if (clusterGroup) {
            markerClusterGroup = clusterGroup.leafletElement
          }
        }}
      >
        {children}
      </MarkerClusterGroup>
    </Fragment>
  )
}

export default BadgeCluster
