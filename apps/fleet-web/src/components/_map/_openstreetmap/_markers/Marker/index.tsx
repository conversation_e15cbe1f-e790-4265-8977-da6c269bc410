import * as React from 'react'
import ReactDOMServer from 'react-dom/server'
import { Marker as LeafletMarker } from 'react-leaflet'
import L from 'leaflet'
import type { OSM } from '../../types'

export function MarkerWithTooltip({
  children,
  icon,
  tooltipText,
  triggerOnHover,
}: {
  children?: React.ReactNode
  icon?: string
  tooltipText?: string
  triggerOnHover?: boolean
}) {
  return (
    <div className={`MarkerWithTooltip${triggerOnHover ? '-hover' : ''}`}>
      {tooltipText && (
        <div className="MarkerWithTooltip-container">
          <div className="MarkerWithTooltip-content">
            <span>{tooltipText}</span>
          </div>
        </div>
      )}
      {icon && <i className={`icon-${icon}`} />}
      {children}
    </div>
  )
}

function Marker({
  id,
  className,
  colorName,
  html,
  customAnchor,
  isDraggable,
  name,
  onClick,
  onDragEnd,
  onHover,
  onMarkerContextMenu,
  zIndexOffset,
  position,
  withTooltip = false,
  alwaysShowTooltip = false,
  children,
  icon,
}: OSM.MarkerProps & {
  className?: string
  name?: string
  withTooltip?: boolean
  alwaysShowTooltip?: boolean
  customAnchor?: L.PointTuple
  children?: React.ReactNode
}) {
  const customMarkerHTML = L.divIcon({
    // eslint-disable-next-line no-nested-ternary
    html: withTooltip
      ? ReactDOMServer.renderToString(
          <MarkerWithTooltip
            icon={icon}
            tooltipText={name}
            triggerOnHover={!alwaysShowTooltip}
          >
            {children}
          </MarkerWithTooltip>,
        )
      : html
      ? html
      : `<i class="icon-${icon}"/>`,
    className: `${className} ${colorName ? colorName : ''}`,
    iconAnchor: customAnchor ?? [0, 0],
    iconSize: customAnchor ? undefined : [0, 0],
  })

  const handleMouseOver = () => {
    if (onHover && onHover.onMouseOver) {
      onHover.onMouseOver(id)
    }
  }

  const handleMouseOut = () => {
    if (onHover && onHover.onMouseOut) {
      onHover.onMouseOut()
    }
  }

  const extraProps = {
    className: `Marker-${id}`,
  }

  return (
    <LeafletMarker
      {...extraProps}
      position={[position.lat, position.lng]}
      icon={customMarkerHTML}
      draggable={isDraggable}
      eventHandlers={{
        click: () => (onClick ? onClick(id) : {}),
        contextmenu: (e: L.LeafletMouseEvent) => {
          e.originalEvent.stopPropagation()
          onMarkerContextMenu?.(id, { ...position, ...e.containerPoint })
        },
        dragend: onDragEnd ? onDragEnd : {},
        mouseover: handleMouseOver,
        mouseout: handleMouseOut,
      }}
      zIndexOffset={zIndexOffset}
      riseOnHover
    />
  )
}

export default Marker
