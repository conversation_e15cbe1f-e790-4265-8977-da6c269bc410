import { renderToString } from 'react-dom/server'
import { TripStartPointMarker } from 'src/components/_map/_markers/Trip/Start'
import { Marker as OpenLayerMarker } from 'src/components/_map/_openstreetmap'
import type { TripMarkerData } from 'src/modules/map-view/map/types'
import { useMapTheme } from 'src/components/context/MapThemeContext'

interface Props {
  position: { lat: number; lng: number }
  trip: TripMarkerData[0]
}

const TripStartPointMarkerOSM = ({ position, trip }: Props) => {
  const mapTheme = useMapTheme()
  return (
    <OpenLayerMarker
      id="tripStart"
      position={position}
      zIndexOffset={600} // Marker pane https://leafletjs.com/reference-1.7.1.html#map-markerpane
      html={renderToString(
        <TripStartPointMarker.Anchored
          number={trip.tripNumber}
          mapTheme={mapTheme}
        />,
      )}
    />
  )
}

export default TripStartPointMarkerOSM
