import { renderToString } from 'react-dom/server'
import { TripEndPointMarker } from 'src/components/_map/_markers/Trip/End'
import { Marker as OpenLayerMarker } from 'src/components/_map/_openstreetmap'
import { useMapTheme } from 'src/components/context/MapThemeContext'
interface Props {
  position: { lat: number; lng: number }
  alwaysShowTooltip?: boolean
}

const TripEndPointMarkerOSM = ({ position }: Props) => {
  const mapTheme = useMapTheme()
  return (
    <OpenLayerMarker
      id="tripEnd"
      position={position}
      zIndexOffset={600} // Marker pane https://leafletjs.com/reference-1.7.1.html#map-markerpane
      html={renderToString(<TripEndPointMarker.Anchored mapTheme={mapTheme} />)}
    />
  )
}

export default TripEndPointMarkerOSM
