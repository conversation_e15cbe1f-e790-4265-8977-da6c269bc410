import styled from 'styled-components'
import ReactDOMServer from 'react-dom/server'
import { Marker } from 'react-leaflet'
import L from 'leaflet'

import type { OSM } from '../../types'
import { vehicleTypes } from '../../constants'
import Anchor from 'src/components/_map/Anchor'
import VehicleMarkerInfo from 'src/components/_map/_markers/Vehicle/Label'
import { useTypedSelector } from 'src/redux-hooks'
import {
  getIsSomeMapVehicleLabelAvailable,
  getMapVehicleLabels,
  type MapVehicleLabels,
  getCarpoolSetting,
} from 'duxs/user'
import {
  useUserFormatLengthInKmOrMiles,
  useUserFormattedClock,
} from 'src/modules/components/connected'
import type { FixMeAny } from 'src/types'
import type { MapVehicle } from 'duxs/map-types'
import { vehicleMarkerBorderIcons } from 'cartrack-ui-kit'
import useShowingPositionUnreliableWarning from 'src/hooks/useShowingPositionUnreliableWarning'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'
import VehicleCarpoolStatusIcon from 'src/util-components/vehicle-carpool-status-icon'

type VehicleMarkerProps = {
  bearing?: number
  displayName?: string
  fade?: boolean
  isHovered?: boolean
  showVehicleLabels?: boolean
  onVehicleContextMenu?: (id: FixMeAny, obj: Record<string, FixMeAny>) => void
  showBearing?: boolean
  vehicleType: number
  vehicleStatus: string
  vehicleInfo: Pick<
    MapVehicle,
    | 'name'
    | 'defaultDriver'
    | 'driverName'
    | 'unitRawClock'
    | 'registration'
    | 'description'
    | 'description1'
    | 'description2'
    | 'odometer'
    | 'positionDescription'
    | 'gpsFixType'
    | 'statusClassName'
    | 'carpoolStatus'
  >
} & OSM.MarkerProps

function vehicleMarker(
  item: VehicleMarkerProps,
  mapVehicleLabels: MapVehicleLabels,
  shouldDisplayInfo: boolean,
  formatLengthInKmOrMiles: ReturnType<
    typeof useUserFormatLengthInKmOrMiles
  >['formatLengthInKmOrMiles'],
  formatClock: ReturnType<typeof useUserFormattedClock>['formatClock'],
  showingWarning: boolean,
  userHasCarpool: boolean,
) {
  const vehicle = item.vehicleInfo
  return L.divIcon({
    html: ReactDOMServer.renderToString(
      <Anchor point="center">
        <div
          className={`OpenLayerVehicleMarker ${item.colorName} ${
            item.isHovered ? 'Glowing' : ''
          } ${showingWarning ? 'dashed' : ''}`}
        >
          <i className={`icon-${vehicleTypes[item.vehicleType]}`} />
          {shouldDisplayInfo && (
            <VehicleMarkerInfo
              vehicleLabels={{
                name: mapVehicleLabels.name ? { value: vehicle.name } : false,
                registration: mapVehicleLabels.registration
                  ? { value: vehicle.registration }
                  : false,
                description: mapVehicleLabels.description
                  ? { value: vehicle.description }
                  : false,
                description1: mapVehicleLabels.description1
                  ? { value: vehicle.description1 }
                  : false,
                description2: mapVehicleLabels.description2
                  ? { value: vehicle.description2 }
                  : false,
                driver: mapVehicleLabels.driver
                  ? {
                      defaultDriver: vehicle.defaultDriver,
                      driverName: vehicle.driverName,
                    }
                  : false,
                location: mapVehicleLabels.location
                  ? {
                      gpsFixType: vehicle.gpsFixType,
                      positionDescription: vehicle.positionDescription,
                    }
                  : false,
                odometer: mapVehicleLabels.odometer
                  ? {
                      value: vehicle.odometer,
                      formatLengthInKmOrMiles,
                    }
                  : false,
                unitRawClock: mapVehicleLabels.unitRawClock
                  ? { value: vehicle.unitRawClock, formatClock }
                  : false,
              }}
              mapType="Here"
            />
          )}
        </div>
        {item.showBearing && (
          <BearingContainer bearing={item.bearing}>
            {/* <BearingBackground /> */}
            <BearingForeground />
            <BearingBorder
              {...makeSanitizedInnerHtmlProp({
                dirtyHtml:
                  vehicleMarkerBorderIcons[showingWarning ? 'dashed' : 'standard'],
              })}
            />
          </BearingContainer>
        )}
        {userHasCarpool && (
          <IconBox>
            <VehicleCarpoolStatusIcon
              statusClassName={item.vehicleInfo.statusClassName}
              carpoolStatus={item.vehicleInfo.carpoolStatus}
            />
          </IconBox>
        )}
      </Anchor>,
    ),
    iconAnchor: [0, 0],
    iconSize: [0, 0],
  })
}

function VehicleMarker(props: VehicleMarkerProps) {
  const {
    id,
    onClick,
    onHover,
    onVehicleContextMenu,
    position,
    fade = false,
    vehicleStatus,
    showVehicleLabels,
    vehicleInfo: { gpsFixType },
  } = props

  const mapVehicleLabels = useTypedSelector(getMapVehicleLabels)
  const isSomeMapVehicleLabelAvailable = useTypedSelector(
    getIsSomeMapVehicleLabelAvailable,
  )
  const userHasCarpool = useTypedSelector(getCarpoolSetting)
  const { formatLengthInKmOrMiles } = useUserFormatLengthInKmOrMiles()
  const { formatClock } = useUserFormattedClock()

  const shouldDisplayInfo = !!showVehicleLabels && isSomeMapVehicleLabelAvailable
  // add id and status for react-leaflet-markercluster
  const extraProps = {
    id: id,
    status: vehicleStatus,
  }

  const showingWarning = useShowingPositionUnreliableWarning(gpsFixType)

  return (
    <Marker
      {...extraProps}
      icon={vehicleMarker(
        props,
        mapVehicleLabels,
        shouldDisplayInfo,
        formatLengthInKmOrMiles,
        formatClock,
        showingWarning,
        userHasCarpool,
      )}
      eventHandlers={{
        click: () => {
          // Not working because on conflict with onMouseOver
          if (onClick) {
            onClick(id)
          }
        },
        contextmenu: (e: L.LeafletMouseEvent) => {
          e.originalEvent.stopPropagation()
          if (onVehicleContextMenu) {
            onVehicleContextMenu(id, { ...position, ...e.containerPoint })
          }
        },
        mouseover: () => {
          if (onHover?.onMouseOver) {
            onHover.onMouseOver(id)
          }
        },
        mouseout: () => {
          if (onHover?.onMouseOut) {
            onHover.onMouseOut()
          }
        },
      }}
      opacity={fade ? 0.8 : 1}
      position={[position.lat, position.lng]}
      zIndexOffset={fade ? 800 : 1000}
    />
  )
}

export default VehicleMarker

const BearingContainer = styled.div<{ bearing?: number }>`
  position: absolute;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  transform: ${({ bearing }) => bearing && `rotate(${bearing}deg)`};
`

const BearingForeground = styled.div`
  position: absolute;
  transform: translate(0px, -24px);
  border-bottom: 19px solid #999;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
`

const BearingBorder = styled.span`
  position: absolute;
  svg {
    width: 60px;
  }
`

const IconBox = styled.div`
  position: absolute;
  left: 21px;
  top: 20px;
  z-index: 1;
`
