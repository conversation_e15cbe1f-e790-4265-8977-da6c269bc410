import { PureComponent } from 'react'
import L from 'leaflet'
import { isEqual, upperFirst } from 'lodash'
import type { FixMeAny } from 'src/types'

import { defaultDrawingStopLabel, defaultShapeOptions } from './constants'
import type { OSM } from '../../types'
import { getCircleGeodesicCoords } from './DrawingUtils'

interface DrawingControlState {
  activeTool: string
  isCursorFree: boolean
  lastPoints?: FixMeAny
  points: Array<FixMeAny>
  toolType: string
}

interface DrawingControlProps {
  mapEvent: FixMeAny
  drawingStopLabel?: string
  fillColor?: string | null
  isEditing: boolean
  points?: Array<FixMeAny>
  onChange: (points: Array<FixMeAny>) => void
}

class DrawingControl extends PureComponent<DrawingControlProps, DrawingControlState> {
  constructor(props: DrawingControlProps) {
    super(props)

    this.map = props.mapEvent.target
    this.state = {
      activeTool: 'polygon',
      isCursorFree: Boolean(props.points && props.points.length > 0),
      lastPoints: [],
      points: props.points || [],
      toolType: 'polygon',
    }
  }

  componentDidMount() {
    this.map.doubleClickZoom.disable()
    this.onStartDrawingShape({ isCentered: true, points: this.props.points })
    window.addEventListener('keydown', this.onFinishDrawing)
  }

  componentDidUpdate(prevProps: DrawingControlProps, prevState: DrawingControlState) {
    if (this.baseShape && prevProps.isEditing !== this.props.isEditing) {
      const hasPoints = this.props.points && this.props.points.length > 0
      if (this.props.isEditing && hasPoints) {
        this.baseShape.makeDraggable() // Need to call makeDraggable() first before enableEdit()
        this.baseShape.enableEdit()
      } else if (this.props.isEditing && !hasPoints) {
        this.onStartDrawingShape({ isCentered: true, points: [] })
      } else {
        this.baseShape.disableEdit()

        if (this.state.lastPoints && this.state.lastPoints.length > 0) {
          this.setState({ lastPoints: [] })
        }
      }
    }

    if (this.baseShape && prevProps.fillColor !== this.props.fillColor) {
      this.baseShape.setStyle({ fillColor: this.props.fillColor })
    }

    if (this.state.points && !isEqual(prevState.points, this.state.points)) {
      this.props.onChange(this.state.points)
    }

    if (this.props.points && !isEqual(prevProps.points, this.props.points)) {
      this.props.onChange(this.props.points)
      this.onStartDrawingShape({
        isCentered: true,
        points: this.props.points,
      }) // To redraw the geofence
    }
  }

  componentWillUnmount() {
    if (this.baseShape) {
      this.baseShape.off('editable:drawing:end', this.onDrawingEnd, false)
      this.baseShape.off('editable:dragend', this.onDraggingEnd, false)
      this.baseShape.off('editable:vertex:dragend', this.onDraggingEnd, false)
      this.baseShape.off('editable:drawing:mousedown', this.onDrawingMousedown, false)
      window.removeEventListener('keydown', this.onFinishDrawing)
    }
  }

  private map: L.Map
  private baseShape?: FixMeAny

  onCenterToPolygon = () => {
    const bounds = this.baseShape.getBounds()
    const center = bounds.getCenter()
    this.map.fitBounds(bounds)
    this.map.panTo(center)
  }

  onChangeToolType = (toolType: string) => {
    this.onStartDrawingShape({ toolType })
    this.setState({ activeTool: toolType, toolType })
  }

  onClearAllPolygon = () => {
    this.baseShape.remove()
    this.setState({
      points: [],
      lastPoints: this.resolveLastPoints(this.state.points),
    })

    this.onStartDrawingShape({ toolType: this.state.activeTool })
  }

  onFinishDrawing = (event: FixMeAny) => {
    if (event.keyCode === 13) {
      this.baseShape.editor && this.baseShape.editor.cancelDrawing()
    }
  }

  onDraggingEnd = (event: FixMeAny) => {
    const points = this.resolveLatLngs(event.target._latlngs[0])
    this.setState({
      points,
      lastPoints: this.resolveLastPoints(this.state.points),
    })
  }

  onDrawingEnd = (event: FixMeAny) => {
    const { toolType } = this.state

    if (toolType === 'polygon') {
      const points =
        event.target._latlngs && this.resolveLatLngs(event.target._latlngs[0])
      this.setState({
        points,
        lastPoints: this.resolveLastPoints(this.state.points),
      })

      return false
    }

    let points: Array<FixMeAny> = []
    if (toolType === 'circle' && event.target._point) {
      const origin = event.layer.getLatLng()
      const radius = event.layer.getRadius()
      points = getCircleGeodesicCoords(origin, radius) // These are the points that make up the circle
    } else if (toolType === 'rectangle' && event.target._latlngs) {
      points = this.resolveLatLngs(event.target._latlngs[0])
    }

    // Redraw circle/reactangle as polygon to enable vertex editing
    this.onStartDrawingShape({ points })
    this.setState({
      points,
      toolType: 'polygon',
      lastPoints: this.resolveLastPoints(this.state.points),
    })

    return
  }

  onStartDrawingShape = ({
    isCentered = false,
    points = [],
    toolType = 'polygon',
  }: FixMeAny) => {
    const shapeOptions: OSM.ShapeProps = {
      ...defaultShapeOptions,
      draggable: this.props.isEditing,
      fillColor: this.props.fillColor as FixMeAny,
    }

    this.baseShape && this.baseShape.remove()

    if (points && points.length > 0) {
      this.baseShape = L.polygon(points, shapeOptions).addTo(this.map)
      isCentered && this.onCenterToPolygon()
    } else {
      const startShape = `start${upperFirst(toolType)}` // startCircle, startRectangle, or startPolygon
      this.baseShape = (this.map as any).editTools[startShape](null, shapeOptions)
    }

    this.onStartEventsSubscription(shapeOptions)
  }

  onStartEventsSubscription = (shapeOptions: OSM.ShapeProps) => {
    const { baseShape, onDraggingEnd, onDrawingEnd, onDrawingMousedown } = this

    if (baseShape) {
      this.props.isEditing ? baseShape.enableEdit() : baseShape.disableEdit()

      baseShape.on('editable:drawing:end', onDrawingEnd, false)
      baseShape.on('editable:drawing:mousedown', onDrawingMousedown, false)
      baseShape.on('editable:dragend', onDraggingEnd, false)
      baseShape.on('editable:vertex:dragend', onDraggingEnd, false)

      // Set style to guide lines
      if (baseShape.editor) {
        baseShape.editor.tools.forwardLineGuide.setStyle(shapeOptions)
        baseShape.editor.tools.backwardLineGuide.setStyle(shapeOptions)
      }
    }
  }

  onDrawingMousedown = (event: FixMeAny) => {
    const { points, activeTool } = this.state
    if (
      !points ||
      points.length > 0 ||
      activeTool === 'circle' ||
      activeTool === 'rectangle'
    ) {
      return false
    }

    const drawnPoints = this.resolveLatLngs(event.target._latlngs[0])
    if (drawnPoints.length > 2) {
      this.setState({
        lastPoints: this.resolveLastPoints(drawnPoints),
      })
    }

    return
  }

  onUndoLastClick = () => {
    const { lastPoints: prevLastPoints, points: prevPoints } = this.state
    if (prevPoints.length === 0 && prevLastPoints.length > 0) {
      this.baseShape.editor.pop() // Remove the last vertex
      const lastPoints = prevLastPoints.slice(0, -1)
      this.setState({ lastPoints })
    } else {
      const lastPoints = prevLastPoints.slice(0, -1)
      const points = prevLastPoints.slice(-1)[0]

      this.onStartDrawingShape({ points })
      this.setState({ lastPoints, points })
    }
  }

  resolveLastPoints = (newPoints: FixMeAny) =>
    newPoints && newPoints.length > 0
      ? [...this.state.lastPoints, newPoints]
      : this.state.lastPoints

  resolveLatLngs = (latlngs: FixMeAny) =>
    latlngs.map((latlng: FixMeAny) => ({ lat: latlng.lat, lng: latlng.lng }))

  render() {
    const { activeTool, lastPoints } = this.state
    return this.props.isEditing ? (
      <div
        id="DrawingControl-container"
        className="DrawingControl-container"
      >
        <span>
          <button
            className="DrawingControl-button-square"
            disabled={!lastPoints || lastPoints.length === 0}
            onClick={this.onUndoLastClick}
          >
            <i className="icon-undo" />
          </button>
          <button
            className="DrawingControl-button-square"
            onClick={this.onClearAllPolygon}
          >
            <i className="icon-times" />
          </button>
          <button
            className={`DrawingControl-button-square ${
              activeTool === 'circle' ? 'orange' : ''
            }`}
            onClick={() => this.onChangeToolType('circle')}
          >
            <i className="icon-circle" />
          </button>
          <button
            className={`DrawingControl-button-square ${
              activeTool === 'rectangle' ? 'orange' : ''
            }`}
            onClick={() => this.onChangeToolType('rectangle')}
          >
            <i className="icon-square" />
          </button>
          <button
            className={`DrawingControl-button-square ${
              activeTool === 'polygon' ? 'orange' : ''
            }`}
            onClick={() => this.onChangeToolType('polygon')}
          >
            <i className="icon-pencil" />
          </button>
        </span>
        <div className="DrawingControl-container-tip">
          {this.props.drawingStopLabel
            ? this.props.drawingStopLabel
            : defaultDrawingStopLabel}
        </div>
      </div>
    ) : null
  }
}

export default DrawingControl
