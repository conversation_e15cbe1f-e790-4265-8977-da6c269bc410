import L from 'leaflet'

const VincentyConstants = { a: 6378137, b: 6356752.3142, f: 1 / 298.257223563 }

type LatLng = {
  lat: number
  lng: number
}

type Destination = {
  latlng: LatLng
  angle: number
  radius: number
}

const destinationVincenty = ({ latlng, angle, radius }: Destination) => {
  // Based on OpenLayers.Util.destinationVincenty function
  // http://dev.openlayers.org/docs/files/OpenLayers/Util-js.html#Util.destinationVincenty
  // Rewritten to work with Leaflet

  const { a, b, f } = VincentyConstants
  const { lat: firstLat, lng: firstLng } = latlng
  const pi = Math.PI

  const alpha1 = (angle * pi) / 180 // Converts angle degrees to radius
  const sinAlpha1 = Math.sin(alpha1)
  const cosAlpha1 = Math.cos(alpha1)
  const tanU1 = (1 - f) * Math.tan((firstLat * pi) / 180) // Converts firstLat degrees to radius
  const cosU1 = 1 / Math.sqrt(1 + tanU1 * tanU1),
    sinU1 = tanU1 * cosU1
  const sigma1 = Math.atan2(tanU1, cosAlpha1)
  const sinAlpha = cosU1 * sinAlpha1
  const cosSqAlpha = 1 - sinAlpha * sinAlpha
  const uSq = (cosSqAlpha * (a * a - b * b)) / (b * b)
  const A = 1 + (uSq / 16384) * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)))
  const B = (uSq / 1024) * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)))

  let sigma = radius / (b * A),
    sigmaP = 2 * Math.PI
  const cos2SigmaM = Math.cos(2 * sigma1 + sigma)
  const sinSigma = Math.sin(sigma)
  const cosSigma = Math.cos(sigma)
  while (Math.abs(sigma - sigmaP) > 1e-12) {
    const deltaSigma =
      B *
      sinSigma *
      (cos2SigmaM +
        (B / 4) *
          (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
            (B / 6) *
              cos2SigmaM *
              (-3 + 4 * sinSigma * sinSigma) *
              (-3 + 4 * cos2SigmaM * cos2SigmaM)))
    sigmaP = sigma
    sigma = radius / (b * A) + deltaSigma
  }
  const tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1
  const lat2 = Math.atan2(
    sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,
    (1 - f) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp),
  )
  const lambda = Math.atan2(
    sinSigma * sinAlpha1,
    cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1,
  )
  const C = (f / 16) * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha))
  const lam =
    lambda -
    (1 - C) *
      f *
      sinAlpha *
      (sigma +
        C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)))
  const lamFunc = firstLng + (lam * 180) / pi // Converts lam radius to degrees
  const secondLat = (lat2 * 180) / pi // Converts lat2a radius to degrees

  return L.latLng(lamFunc, secondLat)
}

export const getCircleGeodesicCoords = (latlng: LatLng, radius: number) => {
  const points = []
  for (let i = 0; i < 36; i++) {
    const angle = (i * 360) / 36 + 0
    const newLatLng = destinationVincenty({ latlng, angle, radius })
    const geomPoint = L.latLng(newLatLng.lng, newLatLng.lat)

    points.push(geomPoint)
  }

  return points
}
