import { useEffect, useState } from 'react'
import * as React from 'react'
import { connect } from 'react-redux'
import {
  MapContainer as ReactLeafletMap,
  ZoomControl,
  type MapContainerProps as MapProps,
  useMap,
  useMapEvents,
} from 'react-leaflet'
import { isEqual } from 'lodash'
import { getContextMenuElement, setContextMenuElement } from 'duxs/map'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny } from 'src/types'
import { TileLayer } from '../index'
import type { Map as LeafletMap } from 'leaflet'

import 'leaflet/dist/leaflet.css'
import 'leaflet.pm'
import 'leaflet.pm/dist/leaflet.pm.css'
import 'leaflet-editable'
import 'leaflet-path-drag'
/**
 * FROM https://github.com/Leaflet/Leaflet.heat
   Even though we don't need this everywhere, the size of the plugin is small enough to justify a simpler approach of one import only (8 KB)
*/
import 'src/_third-party-libs-forks/leaflet-heat/leaflet-heat.js'
import type { OSM } from '../types'
import type { LatLngBoundsLiteral, LatLngExpression } from 'leaflet'
import type { MapApiProvider } from 'api/user/types'
import type { LeafletMapScheme } from '../configs/types'

type Props = {
  children?: React.ReactNode
  path?: string
  landmarks?: FixMeAny
  locale?: string
  mapApiProviderId?: Exclude<MapApiProvider, (typeof MapApiProvider)['GOOGLE']>
  maxBounds?: LatLngBoundsLiteral
  mapCustomStyle?: React.CSSProperties
  mapScheme?: LeafletMapScheme
  mapLayer?: 'traffic' | 'transit'
  editable?: boolean
  onMapClick?: (event: L.LeafletMouseEvent) => void
  onMapContextMenu?: (event: L.LeafletMouseEvent) => void
  onMapChange?: FixMeAny
  onMapLoaded?: MapProps['whenCreated']
  onMoveStart?: () => void
  options: Pick<MapProps, 'center' | 'zoom' | 'zoomControl' | 'minZoom' | 'maxZoom'>
  config?: OSM.MapConfig
  setMapRef?: (ref: FixMeAny) => void
} & ReturnType<typeof mapStateToProps> &
  typeof actionCreators

function Map({
  // Parent
  children,
  locale,
  mapApiProviderId,
  mapCustomStyle,
  mapScheme,
  mapLayer,
  maxBounds,
  onMapClick,
  onMapContextMenu,
  onMapChange,
  onMapLoaded,
  onMoveStart,
  options,
  config,
  editable,
  // State
  contextMenuElement,
  setContextMenuElement,
}: Props) {
  const [mapObject, setMapObject] = useState<LeafletMap | null>(null)
  const handleClick = (event: L.LeafletMouseEvent) => {
    setContextMenuElement(null)
    onMapClick?.(event)
  }

  const handleRightClick = (event: L.LeafletMouseEvent) => {
    if (!contextMenuElement || !isEqual(contextMenuElement.latlng, event.latlng)) {
      event.originalEvent.stopPropagation()
      setContextMenuElement({ type: 'map', latlng: event.latlng })
      onMapContextMenu?.(event)
    }
  }

  const customStyle = mapCustomStyle ? mapCustomStyle : ''
  const extraProps = {
    editable: !!editable,
  }

  return (
    <ReactLeafletMap
      {...extraProps}
      whenCreated={(map) => {
        setMapObject(map)
        if (onMapLoaded) {
          onMapLoaded(map)
        }
      }}
      preferCanvas
      center={options.center}
      minZoom={options.minZoom}
      maxZoom={options.maxZoom}
      zoom={options.zoom}
      zoomControl={false}
      attributionControl={false}
      style={{
        height: '100%',
        width: '100%',
        ...customStyle,
      }}
      maxBounds={maxBounds}
    >
      <ChangeView
        center={options.center}
        zoom={options.zoom}
        onMoveStart={onMoveStart ? onMoveStart : () => {}}
        onClick={handleClick}
        onContextMenu={handleRightClick}
        onMoveEnd={onMapChange ? onMapChange : () => {}}
      />
      <TileLayer
        mapProvider={mapApiProviderId}
        mapScheme={mapScheme}
        mapLayer={mapLayer}
        locale={locale}
        config={config}
        mapObject={mapObject}
      />
      {options.zoomControl && <ZoomControl position="bottomright" />}
      {children}
    </ReactLeafletMap>
  )
}

const mapStateToProps = (state: AppState) => ({
  contextMenuElement: getContextMenuElement(state),
})

const actionCreators = {
  setContextMenuElement,
}

export default connect(mapStateToProps, actionCreators)(Map)

const ChangeView = ({
  center,
  zoom,
  onMoveStart,
  onClick,
  onContextMenu,
  onMoveEnd,
}: {
  center?: LatLngExpression
  zoom?: number
  onMoveStart: FixMeAny
  onClick: FixMeAny
  onContextMenu: FixMeAny
  onMoveEnd: FixMeAny
}) => {
  const map = useMap()
  useEffect(() => {
    if (
      center &&
      zoom &&
      (map.getZoom() !== zoom || !isEqual(map.getCenter(), center))
    ) {
      map.setView(center, zoom)
    }
  }, [center, zoom, map])

  useMapEvents({
    click: onClick,
    contextmenu: onContextMenu,
    movestart: onMoveStart,
    moveend: onMoveEnd,
  })
  return null
}
