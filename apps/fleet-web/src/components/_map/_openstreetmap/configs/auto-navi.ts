import type { LeafletMapLayers } from './types'

export const autoNaviChineseMapLayers = {
  layers: {},
  schemes: {
    roadmap: {
      url: '//webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
      attribution:
        "&copy; <a href='http://osm.org/copyright'>OpenStreetMap</a> contributors",
      subdomains: ['1', '2', '3', '4'],
    },
    // satellite: {
    //   url: '//webst0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
    //   attribution:
    //     "&copy; <a href='http://osm.org/copyright'>OpenStreetMap</a> contributors",
    //   subdomains: ['1', '2', '3', '4'],
    // },
  },
} satisfies LeafletMapLayers
