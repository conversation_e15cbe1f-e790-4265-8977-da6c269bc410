import type { TileLayerProps } from 'react-leaflet'
import type { Except } from 'type-fest'

export type LeafletMapScheme = 'roadmap' | 'hybrid' | 'terrain' | 'satellite'

type StrictTileLayerProps = Except<TileLayerProps, 'attribution'> & {
  attribution: string
}

export interface LeafletMapLayers {
  layers: Partial<Record<'traffic' | 'transit', StrictTileLayerProps>>
  schemes: Partial<Record<LeafletMapScheme, StrictTileLayerProps>>
}
