import type { FixMeAny } from 'src/types'
import type { MarkerClusterGroupProps } from 'react-leaflet-markercluster'
import type { MarkerOptions } from 'leaflet'
import { FetchVehicles } from 'api/vehicles/types'
import type { Primitive, Except } from 'type-fest'
import { MapVehicle } from 'duxs/map-types'

declare namespace OSM {
  interface MapConfig {
    apiKey: string
  }

  interface HoverProps {
    onMouseOver: (id?: string | number) => void
    onMouseOut: () => void
  }

  interface PositionProps {
    lat: number
    lng: number
  }

  type MarkerProps = Except<MarkerOptions, 'onClick' | 'icon'> & {
    id?: string | number
    className?: string
    colorName?: string
    html?: string
    icon?: string
    isDraggable?: boolean
    onClick?: (id: string | number | undefined) => void
    onDragEnd?: FixMeAny
    onHover?: HoverProps
    onMarkerContextMenu?: (id: Primitive, obj: Record<string, FixMeAny>) => void
    position: PositionProps
  }

  interface ShapeProps extends L.PolylineOptions {
    draggable: boolean
    opacity: number
  }

  interface PieMarkerClusterGroupProps extends MarkerClusterGroupProps {
    onAnimationEnd?: FixMeAny
  }

  interface GeofenceProps {
    id?: string
    geometry: Array<FixMeAny>
    color?: string
    name?: string
  }
}
