import { useState } from 'react'
import { connect } from 'react-redux'
import { throttle } from 'lodash'
import { Marker, Polygon } from 'react-leaflet'
import L from 'leaflet'
import { setContextMenuElement } from 'duxs/map'
import type { FixMeAny } from 'src/types'
import type { OSM } from '../types'
import type { Primitive } from 'type-fest'

type Props = {
  item: OSM.GeofenceProps
  onGeofenceContextMenu?: (id: Primitive, obj: Record<string, FixMeAny>) => void
  showDisplayName: boolean
} & typeof actionCreators

const transformLatLng = (coords: FixMeAny) =>
  coords.map((latlng: FixMeAny) => Object.values(latlng))

const getGeofenceLabel = (label?: string) =>
  L.divIcon({
    html: `<span>${label}</span>`,
    className: 'Geofence-name',
  })

const getCentroid = (coords: FixMeAny) =>
  coords.reduce(
    (x: FixMeAny, y: FixMeAny) => [
      x[0] + y[0] / coords.length,
      x[1] + y[1] / coords.length,
    ],
    [0, 0],
  )

function Geofence({
  // Parent
  item,
  onGeofenceContextMenu,
  showDisplayName,
  // State
  setContextMenuElement,
}: Props) {
  const coords = transformLatLng(item.geometry)
  const center = getCentroid(coords)
  const label = getGeofenceLabel(item.name)
  const [isHovering, setHovering] = useState(false)

  const handleRightClick = (event: L.LeafletMouseEvent) => {
    event.originalEvent.stopPropagation()
    setContextMenuElement({ type: 'geofence', latlng: event.latlng })
    onGeofenceContextMenu?.(item.id, {
      ...event.latlng,
      ...event.containerPoint,
    })
  }

  const handleMouseOver = throttle(
    () => {
      setHovering(true)
    },
    5000,
    { leading: true },
  )

  const handleMouseOut = throttle(
    () => {
      setHovering(false)
    },
    5000,
    { leading: true },
  )

  return (
    <Polygon
      key={item.id}
      color="#333"
      fillColor={item.color}
      fillOpacity={0.5}
      opacity={0.5}
      positions={coords}
      weight={1}
      eventHandlers={{
        contextmenu: handleRightClick,
        mouseover: handleMouseOver,
        mouseout: handleMouseOut,
      }}
    >
      {(showDisplayName || isHovering) && (
        <Marker
          position={center}
          icon={label}
        />
      )}
    </Polygon>
  )
}

const actionCreators = {
  setContextMenuElement,
}

export default connect(null, actionCreators)(Geofence)
