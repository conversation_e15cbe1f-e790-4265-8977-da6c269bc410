import { memo } from 'react'
import { useMapFormatMeasuredDistance } from 'src/util-components/map/shared/useMapFormatMeasuredDistance'

type Props = {
  distanceInMeters: number
  measureLabel: string
  measureStopLabel: string
}

function MeasureOverlay({ distanceInMeters, measureLabel, measureStopLabel }: Props) {
  const { formatMapDistance } = useMapFormatMeasuredDistance()

  return (
    <div className="OpenLayersMeasureOverlay">
      <span>{measureLabel}: </span>
      <span>{formatMapDistance({ valueInMeters: distanceInMeters })}</span>
      <p className="OpenLayersMeasureOverlay-small">{measureStopLabel}</p>
    </div>
  )
}

export default memo(MeasureOverlay)
