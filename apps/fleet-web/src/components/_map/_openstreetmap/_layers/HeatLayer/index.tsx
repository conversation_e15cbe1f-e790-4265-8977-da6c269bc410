import L from 'leaflet'
import type { HeatMapOptions } from 'leaflet'
import { useEffect, useState } from 'react'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'

export type HeatLayerProps = {
  map: L.Map
  latlngs: Array<L.LatLng | L.HeatLatLngTuple>
  options: HeatMapOptions
}

export function HeatLayer({ latlngs, map, options }: HeatLayerProps) {
  const [layer] = useState(() => {
    const heatLayer = L.heatLayer(latlngs, options)

    heatLayer.addTo(map)
    return heatLayer
  })

  useEffect(
    () => () => {
      // Make sure the layer is removed from the map
      layer.remove()
    },
    [layer],
  )

  useEffectExceptOnMount(() => {
    // It's faster this way compared to re-creating the layer
    layer.setLatLngs(latlngs)
  }, [latlngs, layer])

  useEffectExceptOnMount(() => {
    // It's faster this way compared to re-creating the layer
    layer.setOptions(options)
  }, [layer, options])

  return null
}
