import type { PolylineProps } from 'react-leaflet'
import PathLine from '..'
import TripEndPointMarkerOSM from '../../../_markers/Trip/End'
import TripStartPointMarkerOSM from '../../../_markers/Trip/Start'
import type { TripMarkerData } from 'src/modules/map-view/map/types'

type Props = {
  bringToBack?: boolean
  bringToFront?: boolean
  vertices: Array<{ lat: number; lng: number }>
  trips?: TripMarkerData
} & Omit<PolylineProps, 'positions'>

export default function tripPathLineOSM({ trips = [], ...restProps }: Props) {
  const { vertices } = restProps

  return vertices.length > 0
    ? [
        <PathLine
          key="polyline"
          {...restProps}
        />,
        ...trips.map(
          (trip) =>
            trip.events[0] !== undefined && (
              <TripStartPointMarkerOSM
                key={`MarkerStart-${trip.tripId}`}
                position={{
                  lat: trip.events[0].lat,
                  lng: trip.events[0].lng,
                }}
                trip={trip}
              />
            ),
        ),

        <TripEndPointMarkerOSM
          key="tripEnd"
          position={{
            lat: vertices[vertices.length - 1].lat,
            lng: vertices[vertices.length - 1].lng,
          }}
        />,
      ]
    : []
}
