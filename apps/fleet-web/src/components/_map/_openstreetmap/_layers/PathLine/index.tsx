import { useRef } from 'react'
import { Polyline, type PolylineProps } from 'react-leaflet'
import type { Polyline as LeafletPolyline } from 'leaflet'
import type { LineString, MultiLineString } from 'geojson'
import { sharedPathLineOptions } from 'src/components/_map/_shared/config/PathLine'
import { useMapTheme } from 'src/components/context/MapThemeContext'

type Props = {
  bringToBack?: boolean
  bringToFront?: boolean
  vertices: Array<{ lat: number; lng: number }>
} & Omit<PolylineProps, 'positions'>

function PathLine({
  bringToBack = false,
  bringToFront = false,
  vertices,
  ...props
}: Props) {
  const pathRef = useRef<LeafletPolyline<LineString | MultiLineString, any>>(null)

  if (pathRef && pathRef.current) {
    if (bringToBack) {
      pathRef.current.bringToBack()
    }

    if (bringToFront) {
      pathRef.current.bringToFront()
    }
  }

  const mapTheme = useMapTheme()
  return vertices.length > 0 ? (
    <Polyline
      ref={pathRef}
      positions={vertices}
      {...{
        color: mapTheme.components.PathLine.strokeColor,
        weight: sharedPathLineOptions.strokeWeight,
      }}
      {...props}
    />
  ) : null
}

export default PathLine
