import { useEffect, useState } from 'react'
import L from 'leaflet'
import type { FixMeAny } from 'src/types'
import {
  defaultMeasureLabel,
  defaultMeasureStopLabel,
  guideLineOpts,
  pathLineOpts,
} from '../../constants'
import MeasureOverlay from '../MeasureOverlay'

type Props = {
  mapEvent: { target: L.Map }
  measureLabel?: string
  measureStopLabel?: string
  onEndMeasure?: () => void
}

function MeasurePathDistance({
  mapEvent,
  measureLabel,
  measureStopLabel,
  onEndMeasure,
}: Props) {
  const map = mapEvent.target
  const paths: Array<FixMeAny> = []

  const [distanceInMeters, setDistanceInMeters] = useState(0)
  const [showOverlay, setOverlayDisplay] = useState(true)

  const pathLines = new L.LayerGroup().addTo(map)
  const guideLine = new L.LayerGroup().addTo(map)

  const onMouseDown = (clickEvent: L.LeafletMouseEvent) => {
    guideLine.clearLayers()

    const lastPath = paths.slice(-1)[0]
    const newPath = Object.values(clickEvent.latlng) as L.LatLngTuple
    paths.push(newPath)

    L.polyline(paths, pathLineOpts).addTo(pathLines)

    if (lastPath && newPath) {
      const newDistanceInMeters = map.distance(lastPath, newPath)
      setDistanceInMeters((distance) => distance + newDistanceInMeters)
    }
  }

  const onMouseMove = (moveEvent: L.LeafletMouseEvent) => {
    if (paths.length <= 0) {
      return null
    }
    guideLine.clearLayers()
    L.polyline([...paths, Object.values(moveEvent.latlng)], guideLineOpts).addTo(
      guideLine,
    )

    return
  }

  const onKeydown = () => {
    map.off('move')
    map.off('mousedown')
    map.off('mousemove')
    map.removeLayer(pathLines)
    map.removeLayer(guideLine)
    setOverlayDisplay(false)
    onEndMeasure?.()
  }

  useEffect(() => {
    map.on('mousedown', onMouseDown, false)
    map.on('mousemove', onMouseMove, false)
    window.addEventListener('keydown', onKeydown)
    return () => {
      map.off('mousedown', onMouseDown, false)
      map.off('mousemove', onMouseMove, false)
      window.removeEventListener('keydown', onKeydown)
    }
    // This useEffect is intentionally should only run once
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return showOverlay ? (
    <MeasureOverlay
      distanceInMeters={distanceInMeters}
      measureLabel={measureLabel ? measureLabel : defaultMeasureLabel}
      measureStopLabel={measureStopLabel ? measureStopLabel : defaultMeasureStopLabel}
    />
  ) : null
}

export default MeasurePathDistance
