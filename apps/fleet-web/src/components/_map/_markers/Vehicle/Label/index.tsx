import { useRef, useEffect, useLayoutEffect } from 'react'
import styled, { css } from 'styled-components'
import { isNil } from 'lodash'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { ctIntl } from 'cartrack-ui-kit'
import type { useUserFormatLengthInKmOrMiles } from 'src/modules/components/connected'
import type { FetchVehicles } from 'api/vehicles/types'
import type { PositionDescription } from 'api/types'
import { UserFormattedPositionAddress } from 'src/modules/components/connected/UserFormattedPositionAddress'
import type { useUserFormattedClock } from 'src/modules/components/connected/UserFormattedClock'
import type { RequireAtLeastOne } from 'type-fest'
import { match } from 'ts-pattern'

type Props = {
  vehicleLabels: RequireAtLeastOne<{
    name?: false | { value: string }
    registration?: false | { value: string }
    description?: false | { value: string }
    description1?: false | { value: string }
    description2?: false | { value: string }
    odometer?:
      | false
      | {
          value: number | null
          formatLengthInKmOrMiles: ReturnType<
            typeof useUserFormatLengthInKmOrMiles
          >['formatLengthInKmOrMiles']
        }
    driver?:
      | false
      | {
          defaultDriver: string | null
          driverName: FetchVehicles.Vehicle['driver_name']
        }
    unitRawClock?:
      | false
      | {
          value: string | null
          formatClock: ReturnType<typeof useUserFormattedClock>['formatClock']
        }
    location?:
      | false
      | { positionDescription: string | PositionDescription; gpsFixType: number | null }
  }>
  mapType: 'Google' | 'Here'
}

/* !!!!!IMPORTANT!!!!!
   Currently this component is being rendered in here maps using ReactDOMServer.renderToString.
   This means that if we use redux inside this component (with connect, useSelector, useDispatch, etc) or in __any__ of the sub components we will have a crash in here maps.
   If you need the redux store for some reason, pass the values as props to this component instead.
*/
export default function VehicleMarkerInfo({ vehicleLabels, mapType }: Props) {
  const markerInfo = useRef<HTMLDivElement>(null)

  // useLayoutEffect is preferred but since here maps uses ReactDOMServer.renderToString, it throws an error when using it (https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85)
  // As a workaround, we use useEffect only when running "in the server" (when using ReactDOMServer.renderToString)

  const mapTypeGoogle = mapType === 'Google'

  const useEffectOrLayoutEffect = mapTypeGoogle ? useLayoutEffect : useEffect

  // This layout effect will make sure the marker position is calculated before painting to the browser
  useEffectOrLayoutEffect(
    () => {
      if (!markerInfo?.current) {
        return
      }
      const width = markerInfo.current.offsetWidth
      if (mapTypeGoogle) {
        markerInfo.current.style.left = `-${width / 2 - 15}px`
      }
    },
    /* We purposely don't add dependency array here because we want this effect to run on every render
      (since it's tricky to know exactly when the width of the marker has changed) */
  )

  const renderVehicleInfoText = (children: string, bold = false) => (
    <VehicleInfoText bold={bold}>
      <span title={children}>{children}</span>
    </VehicleInfoText>
  )

  const renderVehicleInfoSmallText = (
    children: string,
    bold = false,
    minWidth?: number,
  ) => (
    <SmallText
      bold={bold}
      minWidth={minWidth}
      title={children}
    >
      {children}
    </SmallText>
  )

  const maybeRenderVehicleLocation = () => {
    if (!vehicleLabels.location) {
      return
    }
    const {
      positionDescription: vehiclePositionDescription,
      gpsFixType: vehicleGpsFixType,
    } = vehicleLabels.location
    // positionDescription of type string is a TEMPORARY compatibility layer. The goal is to have PositionDescription
    const address: PositionDescription =
      typeof vehiclePositionDescription === 'string'
        ? {
            visibility: 'PUBLIC',
            principal: { description: vehiclePositionDescription },
            alternatives: {
              description_al: null,
            },
          }
        : vehiclePositionDescription

    const gpsFixType =
      typeof vehiclePositionDescription === 'string' ? 3 : vehicleGpsFixType

    return mapTypeGoogle ? (
      <UserFormattedPositionAddress
        address={address}
        gpsFixType={gpsFixType}
        statesRenderer={{
          emptyAddress: () => {
            const text = ctIntl.formatMessage({
              id: 'vehicleLabels.location.noProvided',
            })
            return (
              <SmallText
                minWidth={80}
                title={text}
              >
                {text}
              </SmallText>
            )
          },
          privateAddress: () => {
            const text = ctIntl.formatMessage({ id: 'Privacy Enabled' })
            return (
              <SmallText
                minWidth={80}
                title={text}
              >
                {text}
              </SmallText>
            )
          },
          publicAddress: ({ processedDescriptionText, processedJSX }) => (
            <SmallText
              minWidth={80}
              title={processedDescriptionText}
            >
              {processedJSX}
            </SmallText>
          ),
        }}
      />
    ) : (
      (renderVehicleInfoSmallText(
        match(address)
          .with({ visibility: 'PRIVATE' }, () =>
            ctIntl.formatMessage({ id: 'Privacy Enabled' }),
          )
          .with({ visibility: 'PUBLIC' }, (address) => address.principal.description)
          .with(null, () =>
            ctIntl.formatMessage({
              id: 'vehicleLabels.location.noProvided',
            }),
          )
          .exhaustive(),
      ),
      false,
      80)
    )
  }

  return (
    <VehicleLabelsSection ref={markerInfo}>
      <InfoSection>
        {vehicleLabels.registration &&
          renderVehicleInfoText(
            vehicleLabels.registration.value ||
              ctIntl.formatMessage({
                id: 'vehicleLabels.registration.noProvided',
              }),
            true,
          )}
        {vehicleLabels.name &&
          renderVehicleInfoText(
            vehicleLabels.name.value ||
              ctIntl.formatMessage({
                id: 'vehicleLabels.vehicleName.noProvided',
              }),
          )}
        {vehicleLabels.description &&
          renderVehicleInfoText(
            vehicleLabels.description.value ||
              ctIntl.formatMessage({
                id: 'vehicleLabels.description.noProvided',
              }),
          )}
        {vehicleLabels.description1 &&
          renderVehicleInfoText(
            vehicleLabels.description1.value ||
              ctIntl.formatMessage({
                id: 'vehicleLabels.description2.noProvided',
              }),
          )}
        {vehicleLabels.description2 &&
          renderVehicleInfoText(
            vehicleLabels.description2.value ||
              ctIntl.formatMessage({
                id: 'vehicleLabels.description3.noProvided',
              }),
          )}
        {vehicleLabels.odometer &&
          renderVehicleInfoText(
            !isNil(vehicleLabels.odometer.value)
              ? vehicleLabels.odometer.formatLengthInKmOrMiles({
                  valueInKm: vehicleLabels.odometer.value,
                  transformValueBeforeFormatting: Math.round,
                })
              : ctIntl.formatMessage({
                  id: 'vehicleLabels.odometer.noProvided',
                }),
          )}
      </InfoSection>
      {(vehicleLabels.driver ||
        vehicleLabels.unitRawClock ||
        vehicleLabels.location) && (
        <ExtraInfoSection>
          {vehicleLabels.driver &&
            (() => {
              const message = (() => {
                if (vehicleLabels.driver.driverName.status === 'UNDISCLOSED') {
                  return ctIntl.formatMessage({
                    id: 'vehicle.driverName.undisclosed',
                  })
                }
                if (vehicleLabels.driver.driverName.name) {
                  return vehicleLabels.driver.driverName.name
                }
                if (vehicleLabels.driver.defaultDriver) {
                  return vehicleLabels.driver.defaultDriver
                }
                return ctIntl.formatMessage({
                  id: 'vehicleLabels.driver.noAssigned',
                })
              })()

              return renderVehicleInfoSmallText(message)
            })()}
          {vehicleLabels.unitRawClock &&
            renderVehicleInfoSmallText(
              isNil(vehicleLabels.unitRawClock.value)
                ? ctIntl.formatMessage({
                    id: 'vehicleLabels.clock.noProvided',
                  })
                : vehicleLabels.unitRawClock.formatClock(
                    Number(vehicleLabels.unitRawClock.value),
                  ),
              true,
            )}
          {maybeRenderVehicleLocation()}
        </ExtraInfoSection>
      )}
    </VehicleLabelsSection>
  )
}

const VehicleLabelsSection = styled.div`
  position: absolute;
  padding: ${spacing[2]};
  bottom: 150%;
  transform-origin: center;
  transition: all 0.2s $toggleAnimation;
  z-index: 9;
  text-align: center;
  background-color: #333333;
  border-radius: 3px;

  &:before {
    border-top-color: #ddd;
    margin-left: -8px;
    border-width: 8px;
  }

  &:after,
  &:before {
    left: 50%;
    top: 100%;
    border: solid transparent;
    content: '';
    height: 0;
    pointer-events: none;
    position: absolute;
    width: 0;
  }

  &:after {
    border-top-color: #333333;
    margin-left: -7px;
    border-width: 7px;
  }
`

const VehicleInfoText = styled.div<{ bold?: boolean }>`
  color: #fff;
  font-size: 12px;
  line-height: 14px;
  & span {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 110px;
  }

  &:nth-child(odd) {
    text-align: left;
  }
  &:nth-child(even) {
    padding-left: ${spacing[2]};
    text-align: right;
  }
  ${({ bold }) =>
    bold &&
    css`
      font-weight: bold;
    `}
`

const InfoSection = styled.div`
  display: grid;
  grid-template-columns: repeat(2, auto);
`

const SmallText = styled.div<{ bold?: boolean; minWidth?: number }>`
  color: #fff;
  font-size: 10px;
  line-height: 11px;
  padding-top: ${spacing[2]};
  ${({ bold }) =>
    bold &&
    css`
      font-weight: bold;
    `}
  ${({ minWidth }) =>
    minWidth &&
    css`
      min-width: ${minWidth}px;
    `}
  &:nth-child(odd) {
    text-align: left;
  }
  &:nth-child(even) {
    padding-left: ${spacing[2]};
    text-align: right;
  }
`

const ExtraInfoSection = styled.div`
  margin-top: ${spacing[2]};
  padding: 0 ${spacing[2]} ${spacing[2]} ${spacing[2]};
  background-color: #666666;
  border-radius: 3px;
  display: grid;
  grid-template-columns: repeat(2, auto);
`
