import styled from 'styled-components'
import { select } from '@storybook/addon-knobs'
import ActivityStatusMarker from '.'
import type { TachographDriverActivity } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'

export default {
  component: ActivityStatusMarker,
}

export const Normal = () => {
  const status = select('Status', statusOptions, statusOptions['Driving'])

  return (
    <Container>
      <ActivityStatusMarker status={status} />
    </Container>
  )
}

const statusOptions: Record<string, TachographDriverActivity> = {
  Available: 'AVAILABLE',
  Driving: 'DRIVING',
  'Other Work': 'OTHER_WORK',
  Rest: 'REST',
  'Work for Other Entities': 'WORK_FOR_OTHER_ENTITIES',
  'Unknown Activity': 'UNKNOWN',
}

const Container = styled.div`
  position: relative;
  width: 30px;
  height: 60px;
`
