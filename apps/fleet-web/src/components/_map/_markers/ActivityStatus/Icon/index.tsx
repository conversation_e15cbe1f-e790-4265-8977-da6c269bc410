import { forwardRef } from 'react'
import type { TachographDriverActivity } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import BaseMarker from '../Base'
import AvailableMarker from './_icons/available.png'
import DrivingTimeMarker from './_icons/driving.png'
import OtherWorkTimeMarker from './_icons/other_work.png'
import RestTimeMarker from './_icons/rest.png'
import WorkForOtherEntitiesMarker from './_icons/work_for_other_entities.png'
import UnknownActivityMarker from './_icons/unknown.png'

type Props = {
  status: TachographDriverActivity
}

export default forwardRef<any, Props>(function ActivityStatusMarkerIcon(
  { status },
  ref,
) {
  return (
    <BaseMarker
      ref={ref}
      src={(() => {
        switch (status) {
          case 'AVAILABLE':
            return AvailableMarker
          case 'DRIVING':
            return DrivingTimeMarker
          case 'OTHER_WORK':
            return OtherWorkTimeMarker
          case 'REST':
            return RestTimeMarker
          case 'WORK_FOR_OTHER_ENTITIES':
            return WorkForOtherEntitiesMarker
          case 'UNKNOWN':
            return UnknownActivityMarker
        }
      })()}
    />
  )
})
