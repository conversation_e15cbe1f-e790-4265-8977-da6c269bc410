import { type ComponentProps, type HTMLProps, forwardRef } from 'react'
import styled from 'styled-components'
import type { MapChildComponentProps } from 'src/components/_map/types'
import TripAnchor from '../Anchor'
import type { MapThemeConfig } from 'src/components/context/MapThemeContext'

type BaseProps = { mapTheme: MapThemeConfig } & ComponentProps<typeof TripEndImg> &
  HTMLProps<HTMLImageElement>

type PropsWithGoogleMaps = BaseProps & MapChildComponentProps

type Props = BaseProps | PropsWithGoogleMaps

const Base = forwardRef<HTMLImageElement, Props>(function Base(
  { mapTheme, ...props },
  ref,
) {
  return (
    <TripEndImg
      ref={ref}
      src={mapTheme.components.TripEndMarker.tripEndImgSrc}
      {...props}
    />
  )
})

const Anchored = forwardRef<HTMLImageElement, Props>(function Anchored(props, ref) {
  return (
    <TripAnchor point="center">
      <Base
        {...props}
        ref={ref}
      />
    </TripAnchor>
  )
})

export const TripEndPointMarker = Object.assign(Base, { Anchored })

const TripEndImg = styled.img`
  height: 32px;
  position: absolute;
  transform: scale(1) translate(-30%, -80%);
  transform-origin: 100% 0%;
`
