import type { ComponentProps } from 'react'
import type {
  MapChildComponentProps,
  MapChildComponent,
} from 'src/components/_map/types'
import type ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import TripPopover from 'src/components/_popups/_popovers/Trip'
import { TripStartPointMarker } from '..'
import type { TripMarkerData } from 'src/modules/map-view/map/types'
import { MARKERS_TOOLTIP_Z_INDEX } from 'src/modules/map-view/DriversMapView/Tachograph/constants'
import { useMapTheme } from 'src/components/context/MapThemeContext'

type Props = {
  tooltipProps?: Omit<
    ComponentProps<typeof ArrowedTooltip>,
    'placement' | 'children' | 'label'
  >
  trip: TripMarkerData[0]
}

export default function TripStartPointMarkerWithTooltip({
  trip,
  tooltipProps = {},
}: Props) {
  const mapTheme = useMapTheme()
  return (
    <TripPopover
      {...{
        ...tooltipProps,
        zIndex: MARKERS_TOOLTIP_Z_INDEX,
      }}
      trip={trip}
    >
      <TripStartPointMarker.Anchored
        number={trip.tripNumber}
        mapTheme={mapTheme}
      />
    </TripPopover>
  )
}

export const MapChildTripStartPointMarkerWithTooltip: MapChildComponent<
  Props & MapChildComponentProps
> = ({ lat, lng, ...props }) => TripStartPointMarkerWithTooltip(props)
