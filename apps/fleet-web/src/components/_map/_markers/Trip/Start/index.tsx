import { type ComponentProps, forwardRef } from 'react'
import * as React from 'react'
import styled from 'styled-components'
import type { MapChildComponentProps } from 'src/components/_map/types'
import TripAnchor from '../Anchor'
import type { MapThemeConfig } from 'src/components/context/MapThemeContext'

type BaseProps = {
  number: number
  mapTheme: MapThemeConfig
} & ComponentProps<typeof TripStart> &
  React.HTMLProps<HTMLDivElement>

type PropsWithGoogleMaps = BaseProps & MapChildComponentProps

type Props = BaseProps | PropsWithGoogleMaps

const Base = forwardRef<HTMLDivElement, BaseProps>(function Base(
  { number, mapTheme, ...restProps }: Props,
  ref,
) {
  return (
    <TripStart
      {...restProps}
      ref={ref}
      {...mapTheme.components.TripStartMarker}
    >
      <TripNumber>{number}</TripNumber>
    </TripStart>
  )
})

const Anchored = forwardRef<HTMLDivElement, Props>(function Anchored(
  props: Props,
  ref,
) {
  return (
    <TripAnchor point="center">
      <Base
        {...props}
        ref={ref}
      />
    </TripAnchor>
  )
})

export const TripStartPointMarker = Object.assign(Base, { Anchored })

const TripStart = styled.div<MapThemeConfig['components']['TripStartMarker']>`
  display: flex;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  min-height: 21px;
  min-width: 21px;
  border: 2px solid ${(props) => props.borderColor};
  background-color: ${(props) => props.backgroundColor};
  border-radius: 50%;
`

const TripNumber = styled.span`
  color: black;
  font-size: 12px;
`
