import { type ComponentProps, forwardRef } from 'react'
import Anchor from 'src/components/_map/Anchor'
import Badge from 'src/components/_map/Badge'
import styled from 'styled-components'
import type {
  MapChildComponent,
  MapChildComponentProps,
} from 'src/components/_map/types'
import BaseMarker from '../../ActivityStatus/Base'
import ActivitiesCluster from './_icons/cluster_activities.png'
import { MARKERS_Z_INDEX } from 'src/modules/map-view/DriversMapView/Tachograph/constants'

type Props = {
  numPoints: number
}

export default function ActivitiesMarkerCluster({ numPoints }: Props) {
  return (
    <MarkerClusterAnchor point="bottom">
      <StyledBadge content={numPoints}>
        <BaseMarker src={ActivitiesCluster} />
      </StyledBadge>
    </MarkerClusterAnchor>
  )
}

export const MapChildActivitiesMarkerCluster: MapChildComponent<
  Props & MapChildComponentProps
> = ({ lat, lng, ...props }) => ActivitiesMarkerCluster(props)

const StyledBadge = styled(
  forwardRef<any, ComponentProps<typeof Badge> & { className?: string }>(
    function StyledBadge({ className, ...props }, ref) {
      return (
        <Badge
          ref={ref}
          classes={{ root: className, badge: 'badge' }}
          {...props}
        />
      )
    },
  ),
)`
  .badge {
    background-color: var(--styleSuperscriptCounterColour);
    color: white;
    top: 6px;
    right: -1px;
  }
`

const MarkerClusterAnchor = styled(Anchor)`
  z-index: ${MARKERS_Z_INDEX};
`
