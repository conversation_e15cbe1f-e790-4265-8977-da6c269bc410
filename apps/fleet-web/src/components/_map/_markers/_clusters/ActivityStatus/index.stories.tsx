import styled from 'styled-components'
import ActivityStatusMarkerCluster from '.'
import { number } from '@storybook/addon-knobs'

export default {
  component: ActivityStatusMarkerCluster,
}

export const Normal = () => {
  const numPoints = number('Clustered Items', 5)

  return (
    <Container>
      <ActivityStatusMarkerCluster numPoints={numPoints} />
    </Container>
  )
}

const Container = styled.div`
  position: relative;
  width: 30px;
  height: 60px;
`
