import { type ComponentProps, useState } from 'react'
import { ctIntl } from 'src/util-components/ctIntl'
import type { MapChildComponentProps, MapChildComponent } from '../../../../types'
import type ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import EyeTooltip from 'src/components/_popups/Tooltip/Eye'
import ActivitiesPopover from 'src/components/_popups/_popovers/Activities'
import type { StatusTimePeriodStarterEvent } from 'src/modules/map-view/DriversMapView/Tachograph/MapArea/ui-types'
import ActivitiesMarkerCluster from '..'

type Props = {
  tooltipProps: Omit<
    ComponentProps<typeof ArrowedTooltip>,
    'placement' | 'children' | 'label'
  >
  eyeIconProps?: ComponentProps<typeof EyeTooltip>['eyeIconProps']
  points: Array<StatusTimePeriodStarterEvent>
}

export default function ActivitiesMarkerClusterWithTooltip({
  points,
  tooltipProps,
  eyeIconProps = {},
}: Props) {
  const [executableInfoWindow, setExecutableInfoWindow] = useState<
    'tooltip' | 'popover'
  >('tooltip')

  const numPoints = points.length

  return (
    <EyeTooltip
      {...tooltipProps}
      eyeIconProps={{
        ...eyeIconProps,
        onClick: (e) => {
          eyeIconProps.onClick?.(e)
          setExecutableInfoWindow('popover')
        },
      }}
      label={ctIntl.formatMessage(
        { id: 'map.tachographDrivers.cluster.tooltip.message' },
        { values: { numPoints } },
      )}
      disabled={executableInfoWindow !== 'tooltip'}
    >
      <ActivitiesPopover
        {...tooltipProps}
        activities={points}
        onClickOutside={() => setExecutableInfoWindow('tooltip')}
        visible={executableInfoWindow === 'popover'}
      >
        {ActivitiesMarkerCluster({ numPoints })}
      </ActivitiesPopover>
    </EyeTooltip>
  )
}

export const MapChildActivitiesMarkerClusterWithTooltip: MapChildComponent<
  Props & MapChildComponentProps
> = ({ lat, lng, ...props }) => ActivitiesMarkerClusterWithTooltip(props)
