import { useMemo, type ComponentProps } from 'react'
import { Stack } from '@karoo-ui/core'
import { map } from 'lodash'
import type {
  ControlRoomGetIncidentList,
  IncidentEventId,
} from 'src/modules/alert-center/api/types'
import theme from 'src/components/_themes/ControlRoom'
import PieChartCluster from '../PieChart'
import { Cell } from 'recharts'
import styled from 'styled-components'
import PulseAnimation from 'src/components/_map/PulseAnimation'
import Anchor from '../../../Anchor'
import type { Except } from 'type-fest'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'

type Props = {
  points: Array<
    Except<ControlRoomGetIncidentList.Incidents[0], 'coords'> & {
      lat: number
      lng: number
    }
  >
  focusedIncidentId: IncidentEventId | null
  hoveredIncidentId: IncidentEventId | null
  tooltip?: string | null | undefined
  handleClusterClick?: () => void
} & Except<ComponentProps<typeof PieChartCluster>, 'children' | 'slices'>

const IncidentsPieChartCluster = ({
  points,
  focusedIncidentId,
  hoveredIncidentId,
  tooltip,
  handleClusterClick = () => {},
  ...rest
}: Props) => {
  const slices = useMemo(() => {
    const statusesCounter: Record<
      ControlRoomGetIncidentList.IncidentEventPriorityCode,
      number
    > = {
      HIGH: 0,
      LOW: 0,
      MEDIUM: 0,
    }

    points.forEach((point) => {
      statusesCounter[point.priority.code]++
    })

    return map(statusesCounter, (count, name) => ({
      name: name as ControlRoomGetIncidentList.IncidentEventPriorityCode,
      count,
    }))
  }, [points])

  const highlighted = useMemo(
    () =>
      points.some(
        (point) => point.id === focusedIncidentId || point.id === hoveredIncidentId,
      ),
    [points, focusedIncidentId, hoveredIncidentId],
  )

  return (
    <Anchor point="center">
      <ArrowedTooltip
        placement="top"
        label={tooltip}
      >
        <Stack onClick={handleClusterClick}>
          <PulseAnimation color={highlighted ? '#000' : null}>
            <StyledPieChartCluster
              slices={slices}
              {...rest}
            >
              {slices.map((obj) => (
                <Cell
                  key={obj.name}
                  fill={theme.colors.priority[obj.name]}
                />
              ))}
            </StyledPieChartCluster>
          </PulseAnimation>
        </Stack>
      </ArrowedTooltip>
    </Anchor>
  )
}

export default IncidentsPieChartCluster

const StyledPieChartCluster = styled(PieChartCluster)`
  cursor: pointer !important;
`
