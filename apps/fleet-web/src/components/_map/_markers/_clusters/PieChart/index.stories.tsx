import { select } from '@storybook/addon-knobs'
import { Cell } from 'recharts'
import type { FixMeAny } from 'src/types'
import PieChartCluster from '.'
import PulseAnimation from 'src/components/_map/PulseAnimation'

export default {
  component: PieChartCluster,
}

const slicesPropValues = {
  '1': [
    { name: '1', count: 3 },
    { name: '2', count: 4 },
  ],
  '3': [
    { name: '1', count: 200 },
    { name: '2', count: 200 },
    { name: '3', count: 400 },
  ],
  '5': [
    { name: '1', count: 200 },
    { name: '2', count: 200 },
    { name: '3', count: 400 },
    { name: '4', count: 10000 },
  ],
}

const colors = ['#144C21', '#6BA238', '#FFBD4D', '#ADD746']

export const Colored = () => {
  const slices = select(
    'Count - number of digits',
    slicesPropValues as FixMeAny,
    slicesPropValues['1'] as FixMeAny,
  ) as (typeof slicesPropValues)['1']

  return (
    <PieChartCluster slices={slices}>
      {slices.map((object, index) => (
        <Cell
          key={object.name}
          fill={colors[index % colors.length]}
        />
      ))}
    </PieChartCluster>
  )
}

export const WithPulseAnimation = () => (
  <PulseAnimation color={colors[0]}>{Colored()}</PulseAnimation>
)
