import { forwardRef, type ComponentPropsWithRef, useMemo } from 'react'
import * as React from 'react'
import { PieC<PERSON>, Pie, Label } from 'recharts'
import { sumBy, times } from 'lodash'
import styled, { type StyledComponentPropsWithRef } from 'styled-components'
import { typography } from 'src/shared/components/styled/global-styles/typography'
import { variables } from 'src/shared/components/styled/global-styles'
import type { MapChildComponentExternalProps } from 'src/components/_map/types'

type Props<Data> = {
  pieProps?: Omit<
    ComponentPropsWithRef<typeof Pie>,
    | 'dataKey'
    | 'innerRadius'
    | 'outerRadius'
    | 'startAngle'
    | 'endAngle'
    | 'isAnimationActive'
    | 'data'
    | 'children'
  >
  slices: Data
  children: React.ReactNode
} & Omit<ComponentPropsWithRef<typeof PieChart>, 'width' | 'height'>

type DataWithCount = Array<{ count: number }>

const PIE_DIAMETER = 52
const START_ANGLE = 90

/** For a total count with more than 3 digits, the cluster will be resized  */
const COUNT_DIGITS_THRESHOLD = 3

const PieChartCluster = forwardRef(function PieChartCluster<Data extends DataWithCount>(
  { slices, children, pieProps, ...props }: Props<Data>,
  ref: StyledComponentPropsWithRef<typeof StyledPieChart>['ref'],
) {
  const totalCount = useMemo(() => sumBy(slices, (obj) => obj.count), [slices])

  const totalCountDigits = totalCount.toString().length
  let diameter = PIE_DIAMETER

  if (totalCountDigits > COUNT_DIGITS_THRESHOLD) {
    times(totalCountDigits - COUNT_DIGITS_THRESHOLD, () => {
      /* Helps to maintain same spacing for the counter given an increase in the number of digits
         After some tests, this value was the best for the current font-size, etc.
      */
      diameter += 10
    })
  }

  const pieOuterRadius = diameter / 2
  const pieInnerRadius = pieOuterRadius * 0.68

  return (
    <StyledPieChart
      width={diameter}
      height={diameter}
      ref={ref}
      {...props}
    >
      <circle
        cx="50%"
        cy="50%"
        r={pieInnerRadius}
        fill="#fff"
      />
      <Pie
        dataKey="count"
        innerRadius={pieInnerRadius}
        outerRadius={pieOuterRadius}
        startAngle={START_ANGLE}
        endAngle={360 + START_ANGLE}
        isAnimationActive={false}
        data={slices}
        {...(pieProps || {})}
      >
        [{children},
        <Label
          key="totalCount"
          className="totalCount"
          position="center"
        >
          {totalCount}
        </Label>
        ]
      </Pie>
    </StyledPieChart>
  )
})

export default PieChartCluster

export const MapChildPieChartCluster = <D extends DataWithCount>({
  lat,
  lng,
  ...props
}: Props<D> & MapChildComponentExternalProps) => <PieChartCluster {...props} />

const StyledPieChart = styled(PieChart)`
  .totalCount {
    font-size: 16px;
    color: ${variables.gray80};
    ${typography.mixins.robotoCondensedBold};
  }
`
