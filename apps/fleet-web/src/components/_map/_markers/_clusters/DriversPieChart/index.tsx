import { useMemo, type ComponentProps } from 'react'
import { map } from 'lodash'
import type {
  MapChildComponent,
  MapChildComponentProps,
} from 'src/components/_map/types'
import type {
  DriverWithVehicle,
  TachographDriverActivity,
} from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import PieChartCluster from '../PieChart'
import { Cell } from 'recharts'
import theme from 'src/components/_themes/tachograph'
import styled from 'styled-components'
import PulseAnimation from 'src/components/_map/PulseAnimation'
import Anchor from '../../../Anchor'
import type { Except } from 'type-fest'

type Props = {
  points: Array<
    Except<DriverWithVehicle, 'lastAssignedVehicleEvent'> & {
      lat: number
      lng: number
    }
  >
  pulseAnimationProps: Except<ComponentProps<typeof PulseAnimation>, 'children'>
} & Except<ComponentProps<typeof PieChartCluster>, 'children' | 'slices'>

const DriversPieChartCluster = ({ points, pulseAnimationProps, ...rest }: Props) => {
  const slices = useMemo(() => {
    const statusesCounter: Record<TachographDriverActivity, number> = {
      AVAILABLE: 0,
      DRIVING: 0,
      OTHER_WORK: 0,
      REST: 0,
      UNKNOWN: 0,
      WORK_FOR_OTHER_ENTITIES: 0,
    }

    points.forEach((point) => {
      statusesCounter[point.status]++
    })

    return map(statusesCounter, (count, name) => ({
      name: name as TachographDriverActivity,
      count,
    }))
  }, [points])

  return (
    <Anchor point="center">
      <PulseAnimation {...pulseAnimationProps}>
        <StyledPieChartCluster
          slices={slices}
          {...rest}
        >
          {slices.map((obj) => (
            <Cell
              key={obj.name}
              fill={theme.colors.tachographDriverActivity[obj.name]}
            />
          ))}
        </StyledPieChartCluster>
      </PulseAnimation>
    </Anchor>
  )
}

export default DriversPieChartCluster

export const MapChildDriversPieChartCluster: MapChildComponent<
  Props & MapChildComponentProps
> = ({ lat, lng, ...props }) => <DriversPieChartCluster {...props} />

const StyledPieChartCluster = styled(PieChartCluster)`
  cursor: pointer !important;
`
