import type { ComponentProps } from 'react'
import type { MapChildComponentProps, MapChildComponent } from '../../../../types'
import type ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import DriversPieChartCluster from '..'
import DriversClusterPopover from 'src/components/_popups/_popovers/Drivers/Cluster'
import type { Except } from 'type-fest'

type Props = {
  tooltipProps: Except<
    ComponentProps<typeof ArrowedTooltip>,
    'placement' | 'children' | 'label'
  >
} & ComponentProps<typeof DriversPieChartCluster>

export default function DriversPieChartClusterWithTooltip({
  tooltipProps,
  ...rest
}: Props) {
  return (
    <DriversClusterPopover
      {...tooltipProps}
      drivers={rest.points}
    >
      {DriversPieChartCluster({ ...rest })}
    </DriversClusterPopover>
  )
}

export const MapChildDriversPieChartClusterWithTooltip: MapChildComponent<
  Props & MapChildComponentProps
> = ({ lat, lng, ...props }) => DriversPieChartClusterWithTooltip(props)
