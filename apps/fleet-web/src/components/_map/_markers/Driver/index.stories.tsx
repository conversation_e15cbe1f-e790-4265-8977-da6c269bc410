import styled from 'styled-components'
import { select } from '@storybook/addon-knobs'
import DriverMarker from '.'
import type {
  Driver,
  TachographDriverActivity,
} from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import { alpaca } from 'src/modules/tachograph/test_avatar'

export default {
  component: DriverMarker,
}

export const Normal = () => {
  const status = select('Status', statusOptions, statusOptions['Driving'])
  const role = select('Role', roleOptions, roleOptions['Driver'])

  return (
    <Container>
      <DriverMarker
        name="Alpac Aswag"
        status={status}
        avatar={alpaca}
        vehicle="23-12-GF"
        role={role}
        isETachoDriver
        tooltipProps={{}}
      />
    </Container>
  )
}

const statusOptions: Record<string, TachographDriverActivity> = {
  Available: 'AVAILABLE',
  Driving: 'DRIVING',
  'Other Work': 'OTHER_WORK',
  Rest: 'REST',
  'Work for Other Entities': 'WORK_FOR_OTHER_ENTITIES',
  'Unknown Activity': 'UNKNOWN',
}

const roleOptions: Record<string, Driver['role']> = {
  Driver: 'DRIVER',
  Codriver: 'CODRIVER',
  Null: null,
}

const Container = styled.div`
  position: relative;
  width: 30px;
  height: 60px;
`
