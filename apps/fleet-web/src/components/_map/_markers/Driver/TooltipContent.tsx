import styled from 'styled-components'
import { styled as MuiStyled } from '@karoo-ui/core'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import { variables } from 'src/shared/components/styled/global-styles'
import { typography } from 'src/shared/components/styled/global-styles/typography'
import { ctIntl } from 'src/util-components/ctIntl'
import TachographDriverActivityIcon from 'src/components/Icon/TachographDriverActivity'
import type {
  Driver,
  TachographDriverActivity,
} from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import DriverDetailsInfo from 'src/components/Driver/Details/Info'

type Props = {
  vehicle: string | null
  name: string
  role: Driver['role']
  isETachoDriver: Driver['isETachoDriver']
  status: TachographDriverActivity
}

export default function DriverMarkerTooltipContent({
  vehicle,
  status,
  name,
  role,
  isETachoDriver,
}: Props) {
  return (
    <Container>
      <Details>
        <Name>{name}</Name>

        {vehicle !== null && <RobotoRegularText>{vehicle}</RobotoRegularText>}
        <TooltipDriverInfo
          role={role}
          isETachoDriver={isETachoDriver}
        />
      </Details>
      <Separator />
      <Status>
        <RobotoRegularText>
          {ctIntl.formatMessage({
            id: `map.tachographDrivers.driverActivityStatus.${status}`,
          })}
        </RobotoRegularText>
        <TachographDriverActivityIcon status={status} />
      </Status>
    </Container>
  )
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 110px;
`

const Details = styled.div`
  display: grid;
  grid-template-columns: auto;
  justify-items: center;
  grid-row-gap: 2px;
`

const Text = styled.span`
  font-size: 10px;
`

const Name = styled(Text)`
  ${typography.mixins.robotoBold};
`

const RobotoRegularText = styled(Text)`
  ${typography.fontFamily('roboto')};
`

const Separator = styled.div`
  background-color: ${variables.gray40};
  height: 1px;
  margin: ${spacing[1]} 0;
`

const Status = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4px;
`

const TooltipDriverInfo = MuiStyled(DriverDetailsInfo)({
  fontSize: '11px',
  color: 'white',
})
