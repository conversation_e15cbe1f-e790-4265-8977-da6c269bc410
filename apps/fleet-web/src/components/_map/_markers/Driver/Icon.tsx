import styled from 'styled-components'
import type { TachographDriverActivity } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import type {
  MapChildComponent,
  MapChildComponentProps,
} from 'src/components/_map/types'
import type { Driver } from 'src/modules/map-view/DriversMapView/Tachograph/api/types'
import DriverAvatar from 'src/components/Driver/Avatar'
import theme from 'src/components/_themes/tachograph'
import PulseAnimation from 'src/components/_map/PulseAnimation'
import AvailableTimePin from './_icons/pin_available.png'
import DrivingTimePin from './_icons/pin_driving.png'
import OtherWorkTimePin from './_icons/pin_other_work.png'
import RestTimePin from './_icons/pin_rest.png'
import WorkForOtherEntitiesPin from './_icons/pin_work_for_other_entities.png'
import UnknownActivityPin from './_icons/pin_unknown.png'

type Props = {
  status: TachographDriverActivity
  avatar: Driver['avatar']
  showPulseAnimation?: boolean
  onClick?: () => void
}

export default function DriverMarkerIcon({
  status,
  avatar,
  showPulseAnimation = false,
  onClick,
}: Props) {
  return (
    <Container onClick={() => onClick && onClick()}>
      <StyledPulseAnimation
        color={
          showPulseAnimation ? theme.colors.tachographDriverActivity[status] : null
        }
      >
        <StyledDriverAvatar avatar={avatar} />
      </StyledPulseAnimation>
      <Icon
        src={(() => {
          switch (status) {
            case 'AVAILABLE':
              return AvailableTimePin
            case 'DRIVING':
              return DrivingTimePin
            case 'OTHER_WORK':
              return OtherWorkTimePin
            case 'REST':
              return RestTimePin
            case 'WORK_FOR_OTHER_ENTITIES':
              return WorkForOtherEntitiesPin
            case 'UNKNOWN':
              return UnknownActivityPin
          }
        })()}
      />
    </Container>
  )
}

export const MapChildDriverMarkerIcon: MapChildComponent<
  Props & MapChildComponentProps
> = ({ lat, lng, ...props }) => DriverMarkerIcon(props)

const Container = styled.div`
  display: inline-flex;
  position: relative;
  cursor: pointer;
`

const Icon = styled.img`
  position: relative;
  /** If this size remains, BaseMarker should be used instead */
  width: 30px;
  height: 40px;
`

const StyledPulseAnimation = styled(PulseAnimation)`
  position: absolute;
  width: 20px;
  height: 20px;
  right: 0;
  left: 0;
  margin: auto;
  top: 5px;
`

const StyledDriverAvatar = styled(DriverAvatar)`
  position: relative;
  width: 100%;
  height: 100%;
`
