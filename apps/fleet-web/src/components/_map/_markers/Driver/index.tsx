import type { ComponentProps } from 'react'
import styled from 'styled-components'

import type {
  MapChildComponentProps,
  MapChildComponent,
} from 'src/components/_map/types'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import DriverMarkerTooltipContent from './TooltipContent'
import DriverMarkerIcon from './Icon'
import Anchor from '../../Anchor'
import { DRIVER_MARKER_Z_INDEX } from 'src/modules/map-view/DriversMapView/Tachograph/constants'
import type { Except } from 'type-fest'

type Props = {
  tooltipProps?: Except<
    ComponentProps<typeof ArrowedTooltip>,
    'placement' | 'children' | 'label'
  >
} & ComponentProps<typeof DriverMarkerIcon> &
  ComponentProps<typeof DriverMarkerTooltipContent>

export default function DriverMarker({
  tooltipProps,
  status,
  avatar,
  role,
  isETachoDriver,
  vehicle,
  name,
  showPulseAnimation,
  onClick,
}: Props) {
  return (
    <ArrowedTooltip
      placement="bottom"
      label={
        <DriverMarkerTooltipContent
          name={name}
          role={role}
          isETachoDriver={isETachoDriver}
          vehicle={vehicle}
          status={status}
        />
      }
      {...tooltipProps}
    >
      <DriverAnchor point="bottom">
        {DriverMarkerIcon({ status, avatar, showPulseAnimation, onClick })}
      </DriverAnchor>
    </ArrowedTooltip>
  )
}

export const MapChildDriverMarker: MapChildComponent<
  Props & MapChildComponentProps
> = ({ lat, lng, ...props }) => DriverMarker(props)

const DriverAnchor = styled(Anchor)`
  z-index: ${DRIVER_MARKER_Z_INDEX};
`
