import styled from 'styled-components'
import type {
  MapChildComponentProps,
  MapChildComponent,
} from 'src/components/_map/types'
import Anchor from '../../Anchor'
import type { Except } from 'type-fest'
import theme from 'src/components/_themes/ControlRoom'
import type { ControlRoomGetIncidentList } from 'src/modules/alert-center/api/types'
import { getTranslatedIncidentPriorityCode } from 'src/modules/alert-center/utils'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'

import PinHigh from 'assets/control-room/pin-high.png'
import PinMedium from 'assets/control-room/pin-medium.png'
import PinLow from 'assets/control-room/pin-low.png'

const imagesMapping = {
  HIGH: PinHigh,
  MEDIUM: PinMedium,
  LOW: PinLow,
}

type Props = {
  incident: Except<ControlRoomGetIncidentList.Incidents[0], 'coords'> & {
    lat: number
    lng: number
  }
  isHiglighted: boolean
  onClick: () => void
}

export default function IncidentMarker({ incident, isHiglighted, onClick }: Props) {
  return (
    <IncidentAnchor
      key={incident.id}
      point="bottom"
    >
      {isHiglighted && <HighlightArea priority={incident.priority} />}
      <ArrowedTooltip
        placement="top"
        label={`${incident.vehicle.registration} - ${getTranslatedIncidentPriorityCode(
          incident.priority.code,
        )}`}
      >
        <MarkerAsImage
          src={imagesMapping[incident.priority.code]}
          onClick={onClick}
        />
      </ArrowedTooltip>
    </IncidentAnchor>
  )
}

export const MapChildIncidentMarker: MapChildComponent<
  Props & MapChildComponentProps
> = ({ lat, lng, ...props }) => IncidentMarker(props)

const MarkerAsImage = styled.img`
  width: 30px;
  height: 40px;
  position: relative;
`

const HighlightArea = styled.span<{
  priority: ControlRoomGetIncidentList.Incidents[0]['priority']
}>`
  height: 45px;
  width: 45px;
  background-color: ${({ priority }) => theme.colors.priority[priority.code]};
  opacity: 0.5;
  border-radius: 50%;
  position: absolute;
  left: -8px;
  top: 15px;
`
const IncidentAnchor = styled(Anchor)`
  z-index: 10;
`
