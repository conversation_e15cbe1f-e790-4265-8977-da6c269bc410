import { forwardRef, memo } from 'react'
import styled from 'styled-components'
import { MARKERS_Z_INDEX } from 'src/modules/map-view/DriversMapView/Tachograph/constants'
import type { EventStatusClassName } from 'src/modules/map-view/map/types'
import type { MapThemeConfig } from 'src/components/context/MapThemeContext'

const CIRCLE_EVENT_MARKERS = new Set<EventStatusClassName>([
  'ignition-on',
  'ignition-off',
  'idling',
  'excessive-idling',
])

type Props = {
  status?: EventStatusClassName
  mapTheme: MapThemeConfig
}

const EventMarkerIcon = forwardRef<HTMLImageElement, Props>(function EventMarkerIcon(
  { status, mapTheme },
  ref,
) {
  const icons = mapTheme.components.EventMarker.icons
  return (
    <EventMarkerImg
      ref={ref}
      status={status}
      src={((): string => {
        switch (status) {
          case 'ignition-on':
          case 'ignition-off':
            return icons.ignition
          case 'driving':
          case 'excessive-idling':
          case 'harsh-accel':
          case 'harsh-braking':
          case 'harsh-turning':
          case 'idling':
          case 'max-speed':
          case 'speeding':
          case 'excessive-rpm':
            return icons[status]
          default:
            return icons.default
        }
      })()}
    />
  )
})

export default memo(EventMarkerIcon)

const EventMarkerImg = styled.img<{ status: Props['status'] }>`
  height: ${({ status }) => (status && CIRCLE_EVENT_MARKERS.has(status) ? 12 : 10)}px;
  cursor: pointer;
  transform-origin: center;
  z-index: ${MARKERS_Z_INDEX};
`
