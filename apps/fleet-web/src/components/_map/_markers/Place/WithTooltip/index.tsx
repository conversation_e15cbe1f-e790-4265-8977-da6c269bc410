import type { ComponentProps } from 'react'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import PlaceMarker from '..'
import type { MapChildComponent, MapChildComponentProps } from '../../../types'

type Props = {
  tooltipProps: Omit<ComponentProps<typeof ArrowedTooltip>, 'placement' | 'children'>
}

const PlaceMarkerWithTooltip = ({ tooltipProps }: Props) => (
  <ArrowedTooltip
    placement="top"
    {...tooltipProps}
  >
    {PlaceMarker()}
  </ArrowedTooltip>
)

export default PlaceMarkerWithTooltip

export const MapChildPlaceMarkerWithTooltip: MapChildComponent<
  Props & MapChildComponentProps
> = ({ lat, lng, ...props }) => PlaceMarkerWithTooltip(props)
