import { forwardRef } from 'react'
import * as React from 'react'
import styled, { css } from 'styled-components'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'

type Props = {
  anchorOrigin?: 'topRight' // More may be added later
  classes?: { root?: string; badge?: string }
  component?: React.ElementType
  children: React.ReactNode
  content: React.ReactNode
}

export default forwardRef<typeof Root, Props>(function Badge(
  { component = 'span', content, classes, children, anchorOrigin = 'topRight' },
  ref,
) {
  return (
    <Root
      className={classes?.root}
      as={component}
      ref={ref}
    >
      {children}
      <StyledBadge
        className={classes?.badge}
        anchorOrigin={anchorOrigin}
      >
        {content}
      </StyledBadge>
    </Root>
  )
})

const Root = styled.span`
  position: relative;
  display: inline-flex;
`

const BaseBadge = styled.span`
  border-radius: 4px;
  width: min-content;
  padding: 2px ${spacing[1]};
`

const StyledBadge = styled(BaseBadge)<Pick<Props, 'anchorOrigin'>>`
  position: absolute;

  ${({ anchorOrigin }) => {
    switch (anchorOrigin) {
      case 'topRight':
        return css`
          top: 0;
          right: 0;
          transform: scale(1) translate(50%, -50%);
          transform-origin: 100% 0%;
        `
      default:
        return undefined
    }
  }}
`
