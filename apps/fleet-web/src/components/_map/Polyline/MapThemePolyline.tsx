import { useMapTheme } from 'src/components/context/MapThemeContext'
import type { Except } from 'type-fest'
import Polyline, { type PolylineProps } from '.'

export type MapThemePolylineProps = Except<PolylineProps, 'strokeColor'>

export default function MapThemePolyline(props: MapThemePolylineProps) {
  const mapTheme = useMapTheme()
  return (
    <Polyline
      strokeColor={mapTheme.components.PathLine.strokeColor}
      {...props}
    />
  )
}
