import { TripEndPointMarker } from '../../_markers/Trip/End'
import { MapChildTripStartPointMarkerWithTooltip } from 'src/components/_map/_markers/Trip/Start/WithTooltip'
import { useMapTheme } from 'src/components/context/MapThemeContext'
import tripPolylineBase, { type tripPolylineBaseProps } from './tripPolylineBase'
import type { Except } from 'type-fest'
import type { MapChildComponentProps, MapChildComponent } from '../../types'

type Props = Except<
  tripPolylineBaseProps,
  | 'StartPointMarker'
  | 'EndPointMarker'
  | 'StartPointMarkerProps'
  | 'EndPointMarkerProps'
>

/** Purposely written as a function instead of a component so that children components can get "lat" and "lng" props from the internal google-map-react algorithm */
export default function tripPolyline(props: Props) {
  return tripPolylineBase({
    ...props,
    StartPointMarker: MapChildTripStartPointMarkerWithTooltip,
    EndPointMarker: MapThemeTripEndPointMarker,
  })
}

const MapThemeTripEndPointMarker: MapChildComponent<MapChildComponentProps> = (
  props,
) => {
  const mapTheme = useMapTheme()
  return (
    <TripEndPointMarker.Anchored
      mapTheme={mapTheme}
      {...props}
    />
  )
}
