import { first, last, size } from 'lodash'
import type { MapsExtended } from 'src/types/extended/google-maps'
import type { TripMarkerData } from 'src/modules/map-view/map/types'
import type { MapChildComponent, MapChildComponentProps } from '../../types'
import type { Except } from 'type-fest'
import MapThemePolylineWithPathlineOptions from '../MapThemePolylineWithPathlineOptions'
import { Array_reduce } from 'src/util-functions/performance-critical-utils'

type EdgePointMarkerProps = MapChildComponentProps & {
  trip: TripMarkerData[number]
}

export type tripPolylineBaseProps = {
  mapObject: MapsExtended.MapObject
  path: Array<google.maps.LatLngLiteral>
  trips?: TripMarkerData
  StartPointMarker: MapChildComponent<EdgePointMarkerProps>
  StartPointMarkerProps?: EdgePointMarkerProps
  EndPointMarker: MapChildComponent<MapChildComponentProps>
  EndPointMarkerProps?: MapChildComponentProps
} & Except<google.maps.PolylineOptions, 'map' | 'path'>

/** Purposely written as a function instead of a component so that children components can get "lat" and "lng" props from the internal google-map-react algorithm */
export default function tripPolylineBase({
  mapObject,
  path,
  trips = [],
  StartPointMarker,
  EndPointMarker,
  StartPointMarkerProps,
  EndPointMarkerProps,
  ...polylineOptions
}: tripPolylineBaseProps) {
  const EdgePointMarkers =
    size(path) >= 2
      ? [
          ...Array_reduce(trips, [], (acc: Array<React.ReactNode>, trip) => {
            const firstEvent = first(trip.events)

            if (firstEvent !== undefined) {
              acc.push(
                <StartPointMarker
                  key={`MarkerStart-${trip.tripId}`}
                  lat={firstEvent.lat}
                  lng={firstEvent.lng}
                  trip={trip}
                  {...StartPointMarkerProps}
                />,
              )
            }

            return acc
          }),
          ...(() => {
            const lastPath = last(path)

            return lastPath !== undefined
              ? [
                  <EndPointMarker
                    key="MarkerEnd"
                    lat={lastPath.lat}
                    lng={lastPath.lng}
                    {...EndPointMarkerProps}
                  />,
                ]
              : []
          })(),
        ]
      : [] // Does not make sense to show markers

  return [
    <MapThemePolylineWithPathlineOptions
      key="polyline"
      mapObject={mapObject}
      path={path}
      {...polylineOptions}
    />,
    ...EdgePointMarkers,
  ]
}
