import { useEffect, useState } from 'react'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
import type { MapsExtended } from 'src/types/extended/google-maps'
import type { Except } from 'type-fest'

export type PolylineProps = {
  mapObject: MapsExtended.MapObject
} & Except<google.maps.PolylineOptions, 'map'>

export default function Polyline({
  mapObject,
  // PolylineOptions
  path,
  strokeColor,
  clickable,
  draggable,
  editable,
  geodesic,
  icons,
  strokeOpacity,
  strokeWeight,
  visible,
  zIndex,
}: PolylineProps) {
  const [pathLine, setPathLine] = useState(
    () =>
      new mapObject.maps.Polyline({
        path,
        strokeColor,
        clickable,
        draggable,
        editable,
        geodesic,
        icons,
        map: mapObject.map,
        strokeOpacity,
        strokeWeight,
        visible,
        zIndex,
      }),
  )

  useEffectExceptOnMount(() => {
    if (draggable != null) {
      pathLine.setDraggable(draggable)
    }
  }, [draggable, pathLine])

  useEffectExceptOnMount(() => {
    if (editable != null) {
      pathLine.setEditable(editable)
    }
  }, [editable, pathLine])

  useEffectExceptOnMount(() => {
    pathLine.setMap(mapObject.map)
  }, [mapObject.map, pathLine])

  useEffectExceptOnMount(() => {
    if (path) {
      // NOTE: FIXME: The old code `pathLine.setPath(path)` cause some polyline blurred problem
      // when another different path with bigger zoom level is chosen (zoom-in)
      // So we choose to nullish the map and recreate the polyline with the new path.
      pathLine.setMap(null)
      setPathLine(
        new mapObject.maps.Polyline({
          path,
          strokeColor,
          clickable,
          draggable,
          editable,
          geodesic,
          icons,
          map: mapObject.map,
          strokeOpacity,
          strokeWeight,
          visible,
          zIndex,
        }),
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [path])

  useEffectExceptOnMount(() => {
    if (visible != null) {
      pathLine.setVisible(visible)
    }
  }, [visible, pathLine])

  useEffectExceptOnMount(() => {
    pathLine.setOptions({
      strokeColor,
      clickable,
      geodesic,
      icons,
      strokeOpacity,
      strokeWeight,
      zIndex,
    })
  }, [
    pathLine,
    strokeColor,
    clickable,
    geodesic,
    icons,
    strokeOpacity,
    strokeWeight,
    zIndex,
  ])

  useEffect(
    () => () => {
      pathLine.setMap(null)
    },
    [pathLine],
  )

  return null
}
