import {
  forwardRef,
  type ElementType,
  cloneElement,
  type Ref,
  Children,
  type ReactNode,
  isValidElement,
  useMemo,
} from 'react'
import type { FixMeAny } from 'src/types'
import { combineRefs } from 'src/util-functions/dom-communication-utils'
import styled, { css } from 'styled-components'
import type { MapChildComponent, MapChildComponentProps } from '../types'

type Props = {
  children: ReactNode
  rootRef?: Ref<HTMLElement>
  point: 'center' | 'bottom'
  component?: ElementType
  className?: string
}

/** Puts an anchor on children node at the desired position.
 *
 *  __NOTE:__ This will place the element relative to the nearest parent with position: absolute | relative
 */
const Anchor = forwardRef<typeof Root, Props>(function Anchor(
  { children, point, rootRef, component = 'span', className },
  ref,
) {
  const childrenRef = (children as FixMeAny).ref

  // This useMemo prevent children from being rendered unnecessarily
  const cloneElementProps = useMemo(
    () => ({
      // Based on https://github.com/facebook/react/issues/8873#issuecomment-275423780
      ref: combineRefs(childrenRef, ref),
    }),
    [childrenRef, ref],
  )

  return (
    <Root
      ref={rootRef}
      anchorPoint={point}
      as={component}
      className={className}
    >
      {Children.count(children) === 1 && isValidElement(children)
        ? cloneElement(children, cloneElementProps)
        : children}
    </Root>
  )
})

export default Anchor

const Root = styled.span<{ anchorPoint: Props['point'] }>`
  position: absolute;
  display: inline-flex;

  ${({ anchorPoint }) => {
    switch (anchorPoint) {
      case 'bottom':
        return css`
          bottom: 0;
          right: 0;
          transform: scale(1) translate(50%);
          transform-origin: 100% 0%;
        `
      case 'center':
        return css`
          bottom: 0;
          right: 0;
          transform: scale(1) translate(50%, 50%);
          transform-origin: 100% 0%;
        `
    }
  }}
`

export const MapChildAnchor: MapChildComponent<Props & MapChildComponentProps> = ({
  lat,
  lng,
  ...props
}) => <Anchor {...props} />
