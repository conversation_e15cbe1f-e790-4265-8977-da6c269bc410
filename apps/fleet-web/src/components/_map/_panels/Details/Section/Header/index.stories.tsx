import { useState } from 'react'
import Header from './'
import CloseIcon from 'src/components/Icon/Close'

export default {
  component: Header,
}

export const Main = () => (
  <Header>
    <Header.Title msgProps={{ id: 'Section Header' }}></Header.Title>
  </Header>
)

export const Secondary = () => (
  <Header.Secondary>
    <Header.SecondaryTitle
      msgProps={{ id: 'Section Secondary Header Title' }}
    ></Header.SecondaryTitle>
    <CloseIcon />
  </Header.Secondary>
)

export const SecondaryCollapsable = () => {
  const [isCollapsableOpen, setIsCollapsableOpen] = useState(false)

  return (
    <Header.SecondaryCollapsable
      isOpen={isCollapsableOpen}
      onClick={() => setIsCollapsableOpen(!isCollapsableOpen)}
    >
      <span>Section Header</span>
    </Header.SecondaryCollapsable>
  )
}
