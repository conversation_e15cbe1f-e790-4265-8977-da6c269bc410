import type { ComponentProps } from 'react'
import { Box, styled } from '@karoo-ui/core'
import CollapseIcon from 'src/components/Icon/Collapse'
import IntlTypography, {
  type IntlTypographyProps,
} from 'src/util-components/IntlTypography'

const Header = styled(Box)(({ theme }) =>
  theme.unstable_sx({
    minHeight: '40px',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    px: 1.5,
    color: '#333',
    fontSize: '14px',
    backgroundColor: '#eee',
    border: 'none',
  }),
)

const Secondary = styled(Header)`
  min-height: 30px;
  color: #666;
`

const SecondaryCollapsable = styled(
  ({
    children,
    isOpen,
    ...rest
  }: { isOpen: boolean } & ComponentProps<typeof Secondary>) => (
    <Secondary {...rest}>
      {children}
      <CollapseIcon direction={isOpen ? 'up' : 'down'} />
    </Secondary>
  ),
)`
  cursor: pointer;
  border-bottom: 1px solid #ddd;
`

const Title = (props: IntlTypographyProps) => (
  <IntlTypography
    variant="subtitle2"
    lineHeight={1}
    {...props}
  />
)

const SecondaryTitle = (props: IntlTypographyProps) => (
  <Title
    color="secondary.light"
    {...props}
  />
)

export default Object.assign(Header, {
  Title,
  Secondary,
  SecondaryTitle,
  SecondaryCollapsable,
})
