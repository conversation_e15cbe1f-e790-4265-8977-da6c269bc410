import { keyframes, styled, Box } from '@karoo-ui/core'
import { variables } from 'src/shared/components/styled/global-styles'
import Section from './Section'

const openAnimation = keyframes`
  from {
    opacity: 0;
    left: -${variables.mapDetailsPanelLeftDistance};
  }

  to {
    opacity: 1;
    left: ${variables.mapDetailsPanelLeftDistance};
  }
`

const DetailsPanel = styled(Box)`
  display: flex;
  flex-direction: column;
  position: absolute;
  flex-shrink: 0;
  height: 100%;
  width: ${variables.mapLeftPanelWidth};
  top: 0;
  left: ${variables.mapDetailsPanelLeftDistance};
  z-index: 101;
  opacity: 1;
  background-color: white;
  box-shadow: 2px 0 8px 3px rgba(0, 0, 0, 0.2);
  animation: ${openAnimation} 0.3s ${variables.defaultAnimation};
`

export default Object.assign(DetailsPanel, {
  Section,
})
