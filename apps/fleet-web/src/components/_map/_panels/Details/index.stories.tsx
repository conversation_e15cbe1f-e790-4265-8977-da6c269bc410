import { useState } from 'react'
import { boolean } from '@storybook/addon-knobs'
import DetailsPanel from '.'
import CloseIcon from 'src/components/Icon/Close'

export default {
  component: DetailsPanel,
}

export const Normal = () => {
  const isPanelOpen = boolean('Panel Open', true)

  return isPanelOpen ? <DetailsPanelStory /> : null
}

const DetailsPanelStory = () => {
  const [isCollapsableOpen, setIsCollapsableOpen] = useState(false)

  return (
    <DetailsPanel>
      <DetailsPanel.Section>
        <DetailsPanel.Section.Header>
          <DetailsPanel.Section.Header.Title
            msgProps={{ id: 'Header Title' }}
          ></DetailsPanel.Section.Header.Title>
          <CloseIcon />
        </DetailsPanel.Section.Header>

        <DetailsPanel.Section.Body.MultiLayer>
          <span>Header Content</span>
        </DetailsPanel.Section.Body.MultiLayer>
      </DetailsPanel.Section>

      <DetailsPanel.Section>
        <DetailsPanel.Section.Header.SecondaryCollapsable
          isOpen={isCollapsableOpen}
          onClick={() => setIsCollapsableOpen(!isCollapsableOpen)}
        >
          <span>Secondary Header</span>
        </DetailsPanel.Section.Header.SecondaryCollapsable>

        {isCollapsableOpen && <span>Secondary Header Content</span>}
      </DetailsPanel.Section>

      <DetailsPanel.Section>
        <DetailsPanel.Section.Header>
          <span>Section Header</span>
        </DetailsPanel.Section.Header>

        <span>Section Content</span>
      </DetailsPanel.Section>
    </DetailsPanel>
  )
}
