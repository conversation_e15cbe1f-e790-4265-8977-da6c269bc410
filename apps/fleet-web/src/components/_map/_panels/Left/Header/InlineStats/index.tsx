import styled from 'styled-components'
import { FormattedMessage } from 'react-intl'

export const InlineStats = ({
  id,
  count,
  className,
}: {
  id: string
  count: number
  className?: string
}) => (
  <FormattedMessage
    id={id}
    values={{ count }}
  >
    {(txt) => (
      <Container className={className}>
        <Total>{count}</Total>
        <Text>{txt}</Text>
      </Container>
    )}
  </FormattedMessage>
)

const Total = styled.div`
  ${(props) => props.theme.typography.mixins.robotoBold};
  font-size: 14px;
  margin-right: 2px;
  color: #333333;
`

const Text = styled.div`
  ${(props) => props.theme.typography.fontFamily('roboto')};
  font-size: 12px;
  color: #666666;
  text-transform: uppercase;
`

const Container = styled.div`
  display: flex;
  align-items: baseline;
`
