import { InlineStats } from './InlineStats'
import { Box, styled } from '@karoo-ui/core'

const LeftPanelHeader = styled(Box)(({ theme }) =>
  theme.unstable_sx({
    display: 'flex',
    flexDirection: 'column',
    background: '#f9f9f9',
    borderBottom: '1px solid #dddddd',
    fontSize: '14px',
    left: 0,
    p: 1,
    gap: 1,
    width: '100%',
    zIndex: 100,
  }),
)

const ControlArea = styled(Box)`
  display: flex;
  align-items: center;
  flex-flow: row wrap;
  justify-content: space-between;
`

const ButtonsGroup = styled(Box)`
  display: flex;
  flex-flow: row nowrap;
`

export default Object.assign(LeftPanelHeader, {
  InlineStats,
  ButtonsGroup,
  ControlArea,
})
