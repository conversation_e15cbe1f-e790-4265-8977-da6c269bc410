import LeftPanelHeader from 'src/components/_map/_panels/Left/Header'
import SettingsRoundedIcon from '@mui/icons-material/SettingsRounded'
import { IconButton, Tooltip } from '@karoo-ui/core'
import { ctIntl } from 'src/util-components/ctIntl'

type Props = {
  onClick: () => void
}

const LeftPanelControlButtons = ({ onClick }: Props) => (
  <LeftPanelHeader.ButtonsGroup>
    <Tooltip
      title={ctIntl.formatMessage({ id: 'Settings' })}
      arrow
    >
      <IconButton
        size="small"
        onClick={onClick}
        sx={{ p: 0 }}
      >
        <SettingsRoundedIcon fontSize="small" />
      </IconButton>
    </Tooltip>
  </LeftPanelHeader.ButtonsGroup>
)

export default LeftPanelControlButtons
