import LeftPanel from '.'
import LeftPanelHeader from './Header'
import LeftPanelBody from './Body'
import { IconButton, Tooltip } from '@karoo-ui/core'
import SettingsRoundedIcon from '@mui/icons-material/SettingsRounded'

export default {
  component: LeftPanel,
}

export const Normal = () => (
  <LeftPanel>
    <LeftPanelHeader>
      <LeftPanelHeader.ControlArea>
        <LeftPanelHeader.InlineStats
          id="map.tachographDrivers.header.driversCount"
          count={5}
        />

        <LeftPanelHeader.ButtonsGroup>
          <Tooltip
            title="Filters"
            arrow
          >
            <IconButton
              size="small"
              sx={{ p: 0 }}
            >
              <SettingsRoundedIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </LeftPanelHeader.ButtonsGroup>
      </LeftPanelHeader.ControlArea>
    </LeftPanelHeader>

    <LeftPanelBody>Left Panel Body</LeftPanelBody>
  </LeftPanel>
)
