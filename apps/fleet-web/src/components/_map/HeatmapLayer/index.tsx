import { useEffect, useState } from 'react'
import useEffectExceptOnMount from 'src/hooks/useEffectExceptOnMount'
import type { MapsExtended } from 'src/types/extended/google-maps'
import type { Except } from 'type-fest'

export type HeatmapLayerComponentProps = {
  map: MapsExtended.MapObject['map']
  factory: typeof google.maps.visualization.HeatmapLayer
  data: NonNullable<google.maps.visualization.HeatmapLayerOptions['data']>
} & Except<google.maps.visualization.HeatmapLayerOptions, 'map' | 'data'>

export function HeatmapLayerComponent({
  map,
  factory,
  // Options
  data,
  dissipating,
  gradient,
  maxIntensity,
  opacity,
  radius,
}: HeatmapLayerComponentProps) {
  const [layer] = useState(
    () =>
      new factory({
        map,
        data,
        dissipating,
        gradient,
        maxIntensity,
        opacity,
        radius,
      }),
  )

  useEffect(
    () => () => {
      layer.setMap(null)
    },
    [layer],
  )

  // We have a separate useEffect for data because it's more likely to change
  // And we don't want to re-create the layer every time
  useEffectExceptOnMount(() => {
    layer.setData(data)
  }, [layer, data])

  useEffectExceptOnMount(() => {
    layer.setOptions({
      dissipating,
      gradient,
      maxIntensity,
      opacity,
      radius,
    })
  }, [layer, data, dissipating, gradient, maxIntensity, opacity, radius])

  return null
}
