import type { ComponentType } from 'react'
/** google-map-react internal algorithm searches for children components with these props and places them in the map.
 * https://github.com/google-map-react/google-map-react/tree/1e133e84fdf54c701ab318c546a72372884f5cd8#works-with-your-components
 */
export type MapChildComponentExternalProps = google.maps.LatLngLiteral

export type MapChildComponentInternalProps = {
  $hover: boolean
  $prerender: boolean
}

export type MapChildComponentProps = MapChildComponentExternalProps &
  MapChildComponentInternalProps

export type MapChildComponent<Props extends MapChildComponentExternalProps> =
  ComponentType<Omit<Props, keyof MapChildComponentInternalProps>>
