import { useState } from 'react'
import * as React from 'react'
import { MapTypeId } from 'src/types/extended/google-maps'
import MapTypeTabs from '.'

export default {
  component: MapTypeTabs,
}

export const Normal = () => (
  <StateFul>
    {({ typeId, setTypeId }) => (
      <MapTypeTabs
        activeTypeId={typeId}
        onTypeIdClick={setTypeId}
      />
    )}
  </StateFul>
)

// Utils
const StateFul = ({
  children,
}: {
  children: (data: {
    typeId: google.maps.MapTypeId
    setTypeId: React.Dispatch<React.SetStateAction<google.maps.MapTypeId>>
  }) => React.ReactElement
}) => {
  const [typeId, setTypeId] = useState<google.maps.MapTypeId>(MapTypeId.ROADMAP)

  return children({ typeId, setTypeId })
}
