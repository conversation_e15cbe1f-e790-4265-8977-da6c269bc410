import RoadMapIconButton from 'src/components/_map/_controls/RoadMapIconButton'
import HybridIconButton from 'src/components/_map/_controls/HybridIconButton'
import SatelliteIconButton from 'src/components/_map/_controls/SatelliteIconButton'
import { MapTypeId } from 'src/types/extended/google-maps'
import MapTypeButtonGroup from '../MapTypeButtonGroup'

type Props = {
  activeTypeId: google.maps.MapTypeId
  onTypeIdClick: (typeId: google.maps.MapTypeId) => any
  className?: string
}

export default function MapTypeTabs({ activeTypeId, onTypeIdClick, className }: Props) {
  return (
    <MapTypeButtonGroup className={className}>
      <RoadMapIconButton
        isActive={activeTypeId === MapTypeId.ROADMAP}
        onClick={() => onTypeIdClick(MapTypeId.ROADMAP)}
      />
      <HybridIconButton
        isActive={activeTypeId === MapTypeId.HYBRID}
        onClick={() => onTypeIdClick(MapTypeId.HYBRID)}
      />
      <SatelliteIconButton
        isActive={activeTypeId === MapTypeId.SATELLITE}
        onClick={() => onTypeIdClick(MapTypeId.SATELLITE)}
      />
    </MapTypeButtonGroup>
  )
}
