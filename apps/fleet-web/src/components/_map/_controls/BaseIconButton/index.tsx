import styled, { css, type StyledComponentPropsWithRef } from 'styled-components'
import { isString } from 'lodash'
import type { ComponentProps } from 'react'
import { has } from 'lodash'
import Icon from 'src/components/Icon'
import ArrowedTooltip from 'src/components/_popups/Tooltip/Arrowed'
import { variables } from 'src/shared/components/styled/global-styles'
import { ctIntl } from 'src/util-components/ctIntl'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

type ContainerProps = { isActive?: boolean; disabled?: boolean }

type HtmlIcon = { html: any }
type Props = {
  icon: ComponentProps<typeof Icon>['icon'] | HtmlIcon
  /** As per design requirements you must provide a tooltip for this type of button */
  tooltipProps: Omit<ComponentProps<typeof ArrowedTooltip>, 'children'>
} & StyledComponentPropsWithRef<typeof Container> &
  ContainerProps

export default function BaseIconButton({
  icon,
  tooltipProps: { label, ...restTooltipProps },
  ...rest
}: Props) {
  return (
    <ArrowedTooltip
      label={isString(label) ? ctIntl.formatMessage({ id: label }) : label}
      {...restTooltipProps}
    >
      <Container {...rest}>
        {isCustomIcon(icon) ? (
          <svg {...makeSanitizedInnerHtmlProp({ dirtyHtml: icon.html })} />
        ) : (
          <Icon icon={icon} />
        )}
      </Container>
    </ArrowedTooltip>
  )
}

const isCustomIcon = (icon: Props['icon']): icon is HtmlIcon => has(icon, 'html')

export const baseIconBackgroundColor = '#ffffff'

const Container = styled.div<ContainerProps>`
  display: flex;
  justify-content: center;
  align-items: center;
  color: #666;
  cursor: pointer;
  font-size: 16px;
  background-color: ${baseIconBackgroundColor};
  width: 40px;
  height: 40px;
  text-align: center;

  ${({ theme, isActive }) => {
    const activeCSS = css`
      background-color: ${theme.colors.styleButtonDefaultColourActive};
      border: none !important;
      color: ${theme.colors.styleButtonDefaultTextColourActive};
    `
    return css`
      ${isActive && activeCSS};
      :active {
        ${activeCSS}
      }
    `
  }};

  ${(props) =>
    props.disabled &&
    css`
      border-color: ${variables.gray40};
      color: ${variables.gray40};
      cursor: default;
    `};

  &&& > svg {
    pointer-events: none;
    width: 1em;
  }
`
