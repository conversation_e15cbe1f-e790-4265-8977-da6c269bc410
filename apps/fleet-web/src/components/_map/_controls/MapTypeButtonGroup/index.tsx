import styled from 'styled-components'
import { variables } from 'src/shared/components/styled/global-styles'
import HorizontalButtonGroup from '../HorizontalButtonGroup'

const MapTypeButtonGroup = styled(HorizontalButtonGroup)`
  box-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.1);

  > * {
    border-top-width: 1px;
    border-top-style: solid;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-color: ${variables.gray30};

    :not(:last-child) {
      position: relative;

      :after {
        content: '';
        position: absolute;
        right: 0;
        height: 100%;
        background: ${variables.gray30};
        width: 1px;
      }
    }
  }
`

export default MapTypeButtonGroup
