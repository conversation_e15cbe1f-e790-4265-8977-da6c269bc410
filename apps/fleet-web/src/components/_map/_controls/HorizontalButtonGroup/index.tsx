import styled from 'styled-components'
import BaseButtonGroup, { groupRadius } from '../BaseButtonGroup'

const HorizontalButtonGroup = styled(BaseButtonGroup)`
  display: inline-flex;

  > * {
    :first-child {
      border-top-left-radius: ${groupRadius};
      border-bottom-left-radius: ${groupRadius};
    }

    :last-child {
      border-top-right-radius: ${groupRadius};
      border-bottom-right-radius: ${groupRadius};
    }
  }
`

export default HorizontalButtonGroup
