import styled from 'styled-components'
import { variables } from 'src/shared/components/styled/global-styles'
import { baseIconBackgroundColor } from 'src/components/_map/_controls/BaseIconButton'
import VerticalButtonGroup from '../VerticalButtonGroup'

const buttonsSeparatorHeight = '1px'

const MapNavigationButtonGroup = styled(VerticalButtonGroup)`
  display: inline-flex;
  box-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.2);

  > * {
    /** Contains the separator itself */
    :not(:last-child) {
      position: relative;

      ::after {
        content: '';
        position: absolute;
        bottom: -${buttonsSeparatorHeight};
        height: ${buttonsSeparatorHeight};
        background: ${variables.gray20};
        width: 30px;
        z-index: 1;
      }
    }

    /** Contains the layer for the separator (has the same base color as the buttons).
        This way, when the button is in active state, the separator does not overlap with active state color.

        NOTE: This solution does the trick but it couples this wrapper with a specific type of button. SHOULD BE IMPROVED in the future
    */
    :not(:first-child) {
      position: relative;

      ::before {
        content: '';
        position: absolute;
        top: 0;
        height: ${buttonsSeparatorHeight};
        background: ${baseIconBackgroundColor};
        width: 100%;
        z-index: 0;
      }
    }
  }
`

export default MapNavigationButtonGroup
