import styled from 'styled-components'
import BaseButtonGroup, { groupRadius } from '../BaseButtonGroup'

const VerticalButtonGroup = styled(BaseButtonGroup)`
  display: flex;
  flex-direction: column;

  > * {
    :first-child {
      border-top-left-radius: ${groupRadius};
      border-top-right-radius: ${groupRadius};
    }

    :last-child {
      border-bottom-left-radius: ${groupRadius};
      border-bottom-right-radius: ${groupRadius};
    }
  }
`

export default VerticalButtonGroup
