import DownloadFilledIcon from '@mui/icons-material/Download'
import { Paper, type PaperProps } from '@karoo-ui/core'
import type { Except } from 'type-fest'

type Props = Except<PaperProps, 'children' | 'sx'>

export function DownloadMediaButton(props: Props) {
  return (
    <Paper
      sx={(theme) => ({
        cursor: 'pointer',
        pointerEvents: 'all',
        display: 'flex',
        background: theme.palette.secondary.main,
        width: 30,
        height: 30,
        alignItems: 'center',
        justifyContent: 'center',
      })}
      {...props}
    >
      <DownloadFilledIcon
        fontSize="small"
        sx={{ fill: 'white' }}
      />
    </Paper>
  )
}
