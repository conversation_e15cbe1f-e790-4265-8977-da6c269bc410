import CommunicatorUser from './index'
import { select, text } from '@storybook/addon-knobs'

export default {
  component: CommunicatorUser,
}

export const Normal = () => {
  const name = text('Name', 'Alpack Aswag')
  const vehicle = text('Vehicle', '12-QW-34')
  const stars = select('Stars', starsOptions, starsOptions['2'])
  const mobileDevice = text('MobileDevice', '')

  return (
    <CommunicatorUser
      name={name}
      avatar={null}
      vehicle={vehicle}
      mobileDevice={mobileDevice}
      stars={stars}
    />
  )
}

const starsOptions = {
  0: 0,
  1: 1,
  2: 2,
  3: 3,
}
