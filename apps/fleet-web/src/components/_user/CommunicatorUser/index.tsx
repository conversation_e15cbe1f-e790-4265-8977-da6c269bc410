import styled from 'styled-components'

import DriverAvatarWithHalo from 'src/components/Driver/Avatar/WithHalo'
import Stars from 'src/components/Stars'

type Props = {
  avatar?: string | null
  avatarHaloColor?: string | null
  name: string
  mobileDevice?: string
  stars?: number
  vehicle?: string
}

function CommunicatorUser({
  avatar = null,
  avatarHaloColor,
  name,
  mobileDevice,
  stars,
  vehicle,
}: Props) {
  return (
    <Container>
      <DriverAvatarWithHalo
        avatar={avatar}
        color={avatarHaloColor}
      />
      <DetailContainer>
        <CommunicatorName>{name}</CommunicatorName>
        {vehicle && (
          <>
            <CommunicatorVehicleOrMobile>{vehicle}</CommunicatorVehicleOrMobile>
            <Stars rating={stars} />
          </>
        )}
        {!vehicle && mobileDevice && (
          <CommunicatorVehicleOrMobile>{mobileDevice}</CommunicatorVehicleOrMobile>
        )}
      </DetailContainer>
    </Container>
  )
}

export default CommunicatorUser

const Container = styled.div`
  align-items: center;
  display: flex;
  flex-direction: row;
  padding: 8px 16px;
  width: 100%;
`
const DetailContainer = styled.div`
  display: flex;
  flex-direction: column;
  font-family: Roboto, sans-serif;
  color: #333;
  font-size: 14px;
  padding: 5px;
`

const CommunicatorName = styled.div`
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`
const CommunicatorVehicleOrMobile = styled.div`
  color: #999999;
  font-weight: bold;
  font-size: 10px;
  padding: 2px 0;
`
