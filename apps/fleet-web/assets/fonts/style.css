@font-face {
  font-family: 'icomoon';
  src: url('icomoon.eot?zgd3pd');
  src: url('icomoon.eot?zgd3pd#iefix') format('embedded-opentype'),
    url('icomoon.ttf?zgd3pd') format('truetype'),
    url('icomoon.woff?zgd3pd') format('woff'),
    url('icomoon.svg?zgd3pd#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^='icon-'],
[class*=' icon-'] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-ambulance:before {
  content: '\e900';
  color: #666;
}
.icon-fire-truck:before {
  content: '\e901';
  color: #666;
}
.icon-pickup-truck:before {
  content: '\e902';
  color: #666;
}
.icon-water-truck:before {
  content: '\e903';
  color: #666;
}
.icon-4x4:before {
  content: '\e904';
}
.icon-boat:before {
  content: '\e905';
}
.icon-bus:before {
  content: '\e906';
}
.icon-crane:before {
  content: '\e907';
}
.icon-generator:before {
  content: '\e908';
}
.icon-golf-car:before {
  content: '\e909';
}
.icon-large-machine:before {
  content: '\e90a';
}
.icon-large-truck:before {
  content: '\e90b';
}
.icon-mobile-crane:before {
  content: '\e90c';
}
.icon-motorbike:before {
  content: '\e90d';
}
.icon-sedan-car:before {
  content: '\e90e';
}
.icon-small-car:before {
  content: '\e90f';
}
.icon-small-machine:before {
  content: '\e910';
}
.icon-small-truck:before {
  content: '\e911';
}
.icon-static-pump:before {
  content: '\e912';
}
.icon-truck-concrete-pump:before {
  content: '\e913';
}
.icon-truck-mixer:before {
  content: '\e914';
}
.icon-van:before {
  content: '\e915';
}
.icon-map-marker:before {
  content: '\e916';
}
.icon-warning:before {
  content: '\e917';
}
.icon-pencil:before {
  content: '\e918';
}
.icon-square:before {
  content: '\e919';
}
.icon-circle:before {
  content: '\e91a';
}
.icon-times:before {
  content: '\e91b';
}
.icon-undo:before {
  content: '\e91c';
}
.icon-flag-solid:before {
  content: '\e91d';
}
.icon-flag-checkered:before {
  content: '\e91e';
}
