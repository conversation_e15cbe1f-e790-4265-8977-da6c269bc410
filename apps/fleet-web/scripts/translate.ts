/* eslint-disable no-console */
import * as fs from 'node:fs'
import * as path from 'node:path'
import { URLSearchParams } from 'node:url'

const API_URL = 'https://api.poeditor.com/v2/'
const API_TOKEN = 'c9b05301c2c1f644efde75602a938228'
const PROJ_ID = '202577'
const PUSH_PATH = path.resolve(__dirname, '../locales/')
const EXPORT = false

type POEditorPossibleLangCode =
  | 'ar-ae'
  | 'en-us'
  | 'en-za'
  | 'es'
  | 'es-mx'
  | 'fr'
  | 'id'
  | 'ja'
  | 'ms'
  | 'pl'
  | 'pt_pt'
  | 'pt'
  | 'th'
  | 'vi'
  | 'zh-CN'
  | 'zh-hk'
  | 'km'
  | 'fil'
  | 'en-nz'
  | 'en-sg'

const ROOT_FALLBACK_LANG_CODE = 'en-us' satisfies POEditorPossibleLangCode

const LANGUAGE_CODE_LOCALE_FILENAME_MAP = {
  'ar-ae': {
    fileKey: 'ar',
    emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE,
  },
  'en-us': { fileKey: 'en', emptyTranslationsFallbackLangCode: null },
  'en-za': {
    fileKey: 'en-ZA',
    emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE,
  },
  es: { fileKey: 'es', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  'es-mx': {
    fileKey: 'es-MX',
    emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE,
  },
  fr: { fileKey: 'fr', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  id: { fileKey: 'id', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  ja: { fileKey: 'ja-JP', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  ms: { fileKey: 'ms', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  pl: { fileKey: 'pl', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  pt_pt: {
    fileKey: 'pt-MZ',
    emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE,
  },
  pt: { fileKey: 'pt-PT', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  th: { fileKey: 'th', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  vi: { fileKey: 'vi', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  'zh-CN': {
    fileKey: 'zh',
    emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE,
  },
  'zh-hk': {
    fileKey: 'zh-Hans-HK',
    emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE,
  },
  km: { fileKey: 'km', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  fil: { fileKey: 'fil', emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE },
  'en-nz': {
    fileKey: 'en-NZ',
    emptyTranslationsFallbackLangCode: ROOT_FALLBACK_LANG_CODE,
  },
  'en-sg': { fileKey: 'en-SG', emptyTranslationsFallbackLangCode: 'en-za' },
} as const satisfies Record<
  POEditorPossibleLangCode,
  {
    fileKey: string
    emptyTranslationsFallbackLangCode: POEditorPossibleLangCode | null
  }
>

const fallbackLanguagesCodes = new Set<POEditorPossibleLangCode>()
// Make sure en-us is always the first fallback language because it is itself a fallback for the other "fallback languages"
fallbackLanguagesCodes.add(ROOT_FALLBACK_LANG_CODE)
Object.values(LANGUAGE_CODE_LOCALE_FILENAME_MAP).forEach((value) => {
  if (value.emptyTranslationsFallbackLangCode) {
    fallbackLanguagesCodes.add(value.emptyTranslationsFallbackLangCode)
  }
})

type RequestedPOEditorLangCode = keyof typeof LANGUAGE_CODE_LOCALE_FILENAME_MAP

async function doApiCall(endPoint: string, parameters: Record<string, string>) {
  const response = await fetch(API_URL + endPoint, {
    method: 'POST',
    body: new URLSearchParams(parameters),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
  return await response.json()
}

function replaceLineSeparator(str: string) {
  // Sometimes, the translators add tabs and this gets confused with a line separator, which causes issues in json. We replace them with spaces
  return str.replace(/\u2028/g, ' ')
}

async function downloadFile({
  languageUrl,
  languageCode,
  downloadPath = PUSH_PATH,
  emptyTranslationsFallbackJson,
}: {
  languageUrl: string
  languageCode: RequestedPOEditorLangCode
  downloadPath?: string
  emptyTranslationsFallbackJson: Record<string, unknown> | undefined
}): Promise<Record<string, unknown>> {
  const response = await fetch(languageUrl)
  const rawData: Record<string, unknown> = await response.json()
  const extension = downloadPath === PUSH_PATH ? 'json' : 'po'
  const fileName = getTranslationFileName(downloadPath, languageCode, extension)
  const dir = path.dirname(fileName)
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }

  if (emptyTranslationsFallbackJson) {
    for (const [key, value] of Object.entries(rawData)) {
      if (value === '') {
        rawData[key] = emptyTranslationsFallbackJson[key]
      }
    }
  }

  fs.writeFileSync(
    fileName,
    JSON.stringify(
      rawData,
      (_, value) => (typeof value === 'string' ? replaceLineSeparator(value) : value),
      2,
    ),
  )

  return rawData
}

async function fetchTranslationLanguages() {
  const languageList = await doApiCall('languages/list', {
    api_token: API_TOKEN,
    id: PROJ_ID,
  })
  return languageList.result.languages as Array<{ name: string; code: string }>
}

async function fetchLanguageExportLink(languageCode: string, asJson = true) {
  const exportType = asJson ? 'key_value_json' : 'po'
  const downloadResponse = await doApiCall('projects/export', {
    api_token: API_TOKEN,
    id: PROJ_ID,
    language: languageCode,
    type: exportType,
  })
  return downloadResponse.result.url
}

function getTranslationFileName(
  downloadPath: string,
  languageCode: RequestedPOEditorLangCode,
  extension = 'json',
) {
  return path.join(
    downloadPath,
    `${LANGUAGE_CODE_LOCALE_FILENAME_MAP[languageCode].fileKey}.${extension}`,
  )
}

async function downloadLanguageFile({
  languageCode,
  emptyTranslationsFallbackJson,
}: {
  languageCode: RequestedPOEditorLangCode
  emptyTranslationsFallbackJson: Record<string, unknown> | undefined
}) {
  const languageUrl = await fetchLanguageExportLink(languageCode, !EXPORT)

  if (EXPORT) {
    return await downloadFile({
      languageUrl,
      languageCode,
      downloadPath: path.join(PUSH_PATH, 'lang_export'),
      emptyTranslationsFallbackJson,
    })
  }
  return await downloadFile({
    languageUrl,
    languageCode,
    emptyTranslationsFallbackJson,
  })
}

async function main() {
  const projectLanguages = await fetchTranslationLanguages()

  if (EXPORT) {
    const exportPath = path.join(PUSH_PATH, 'lang_export')
    if (!fs.existsSync(exportPath)) {
      fs.mkdirSync(exportPath, { recursive: true })
    }
  }

  const translationsFallbackLangsDownloadedJson = new Map<
    RequestedPOEditorLangCode,
    Record<string, unknown>
  >()

  console.log(`Downloading fallback languages by priority...`)
  for (const fallbackLangCode of fallbackLanguagesCodes) {
    const found = projectLanguages.find((lang) => lang.code === fallbackLangCode)
    if (!found) {
      throw new Error(`[ERROR] Fallback language not found for ${fallbackLangCode}`)
    }

    console.log(`Downloading fallback language [${fallbackLangCode}]`)
    const json = await downloadLanguageFile({
      languageCode: fallbackLangCode,
      emptyTranslationsFallbackJson:
        // The root fallback language that other fallback languages may depend on
        translationsFallbackLangsDownloadedJson.get(ROOT_FALLBACK_LANG_CODE),
    })
    translationsFallbackLangsDownloadedJson.set(fallbackLangCode, json)
  }

  console.log('Finished downloading fallback languages')

  const nonFallbackProjectLanguages = projectLanguages.filter(
    (lang) => !fallbackLanguagesCodes.has(lang.code as POEditorPossibleLangCode),
  )

  for (const [count, language] of nonFallbackProjectLanguages.entries()) {
    if (
      LANGUAGE_CODE_LOCALE_FILENAME_MAP[
        language.code as keyof typeof LANGUAGE_CODE_LOCALE_FILENAME_MAP
      ] === undefined
    ) {
      console.log(
        `${language.name} [${language.code}] not supported ${count + 1}/${
          projectLanguages.length
        }`,
      )
      continue
    }
    const languageCode = language.code as keyof typeof LANGUAGE_CODE_LOCALE_FILENAME_MAP
    const { emptyTranslationsFallbackLangCode } =
      LANGUAGE_CODE_LOCALE_FILENAME_MAP[languageCode]

    console.log(
      `Downloading ${language.name} [${languageCode}] ${count + 1}/${
        nonFallbackProjectLanguages.length
      }`,
    )

    if (
      emptyTranslationsFallbackLangCode &&
      !translationsFallbackLangsDownloadedJson.has(emptyTranslationsFallbackLangCode)
    ) {
      throw new Error(
        `Required fallback language [${emptyTranslationsFallbackLangCode}] not downloaded`,
      )
    }

    await downloadLanguageFile({
      languageCode,
      emptyTranslationsFallbackJson: emptyTranslationsFallbackLangCode
        ? translationsFallbackLangsDownloadedJson.get(emptyTranslationsFallbackLangCode)
        : undefined,
    })
  }

  console.log('Complete')
}

main().catch(console.error)

export type LocaleFileKey =
  (typeof LANGUAGE_CODE_LOCALE_FILENAME_MAP)[keyof typeof LANGUAGE_CODE_LOCALE_FILENAME_MAP]['fileKey']
